<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  name: 'PassRateCharts',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    passRate: {
      require: false,
      type: Number,
      default: () => 0
    }
  },
  watch: {
    passRate: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      console.log(this.passRate)
      var category = [{
        value: this.passRate
      }] // 类别
      var total = 100// 数据总数
      var datas = []
      category.forEach(value => {
        datas.push(value.value)
      })
      this.chart = echarts.init(document.getElementById(this.id))
      var myColor = ['#0096f3']
      this.option = {
        animationDuration: 3000,
        xAxis: {
          max: total,
          splitLine: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        grid: {
          left: 0,
          top: 0, // 设置条形图的边距
          right: 0,
          bottom: 0
        },
        yAxis: [{
          type: 'category',
          inverse: false,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          }
        }],
        series: [{
          // 内
          type: 'bar',
          barWidth: 12,
          legendHoverLink: false,
          silent: true,
          itemStyle: {
            normal: {
              color: function(params) {
                var color = {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [{
                    offset: 0,
                    color: '#128dfb' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#17d3c5' // 100% 处的颜色
                  }
                  ]
                }
                return color
              }
            }
          },

          label: {
            normal: {
              show: true,
              position: 'left',
              formatter: '{b}',
              textStyle: {
                color: '#fff',
                fontSize: 14
              }
            }
          },
          data: category,
          z: 1,
          animationEasing: 'elasticOut'
        },
        {
          // 分隔
          type: 'pictorialBar',
          itemStyle: {
            normal: {
              color: '#061348'
            }
          },
          symbolRepeat: 'fixed',
          symbolMargin: 6,
          symbol: 'rect',
          symbolClip: true,
          symbolSize: [3, 13],
          symbolPosition: 'start',
          symbolOffset: [1, -1],
          symbolBoundingData: this.total,
          data: category,
          z: 2,
          animationEasing: 'elasticOut'
        },
        {
          name: '外框',
          type: 'bar',
          barGap: '-127%', // 设置外框粗细
          data: [total],
          barWidth: 18,
          itemStyle: {
            normal: {
              color: 'transparent', // 填充色
              barBorderColor: '#14A2F8', // 边框色
              barBorderWidth: 1, // 边框宽度
              barBorderRadius: 2, // 圆角半径
              label: {
                // 标签显示位置
                show: false,
                position: 'top' // insideTop 或者横向的 insideLeft
              }
            }
          },
          z: 0
        }]
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
