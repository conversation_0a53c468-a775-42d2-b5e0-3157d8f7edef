<template>
  <div
    v-if="propData.length"
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else :style="{ height: height, width: width }" class="no-data">
    <span>暂无数据</span>
  </div>
</template>
<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  name: 'ConstructionBar',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts',
    },
    width: {
      require: false,
      type: String,
      default: '200px',
    },
    height: {
      require: false,
      type: String,
      default: '200px',
    },
    propData: {
      require: false,
      type: Array,
      default: () => {
        return []
      },
    },
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.chart.dispose();
          this.chart = null
        }
        if(this.propData.length) {
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true, // true 深度监听
    },
  },
  created() {
    this.doNotRedraw = true
  },
  mounted() {
    if (this.propData.length) {
        this.$nextTick(() => {
            this.initChart();
        })
    }
  },
  methods: {
    initChart() {
      const colorArr = [
        '#0966E6',
        '#4CEFEF',
        '#357FDD',
        '#E8B501',
        '#8493FB',
        '#DDE67B',
      ]
      const dataArr = this.propData
      this.chart = echarts.init(document.getElementById(this.id))
      const data = this.propData.map((item) => {
        return {
          name: item.name,
          value: item.total,
        }
      })
      const nameArr = this.propData.map((item) => item.name)
      this.option = {
        backgroundColor: '#050C39',
        title: {
          x: 'center',
          y: 'center',
          textStyle: {
            fontWeight: 'normal',
            fontSize: 16,
          },
        },
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          itemHeight: 8,
          itemWidth: 8,
          top: '10%',
          right: '0%',
          textStyle: {
            fontSize: 12,
            color: '#ABC9FF',
          },
          data: nameArr,
        },
        series: [
          {
            type: 'pie',
            selectedMode: 'single',
            roseType: 'radius',
            radius: ['25%', '50%'],
            center: ['30%', '55%'],
            hoverAnimation: false,
            color: [
              '#0966E6',
              '#4CEFEF',
              '#357FDD',
              '#E8B501',
              '#8493FB',
              '#DDE67B',
            ],
            label: {
              show: false,
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: data,
          },
          {
            type: 'pie',
            radius: ['20%', '50%'],
            center: ['30%', '55%'],
            hoverAnimation: false,
            itemStyle: {
              normal: {
                color: 'RGBA(4, 22, 99, 1)',
              },
              emphasis: {
                color: 'RGBA(4, 22, 99, 1)',
              },
            },
            label: {
              normal: {
                show: false,
              },
            },
            data: data,
            z: -1,
          },
          {
            type: 'pie',
            radius: ['20%', '70%'],
            center: ['30%', '55%'],
            hoverAnimation: false,
            itemStyle: {
              normal: {
                color: 'RGBA(31, 79, 200, 0.2)',
              },
              emphasis: {
                color: 'RGBA(31, 79, 200, 0.2)',
              },
            },
            avoidLabelOverlap: true,
            // label: {
            //   // position: 'outside',
            //   normal: {
            //     position: 'inner',
            //     formatter: '{c}件',
            //     textStyle: {
            //       color: '#1FC8FF',
            //       fontWeight: 'bold',
            //       fontSize: 10
            //     }
            //   }
            // },
            data: data,
            z: -2,
          },
        ],
      }
      this.chart.setOption(this.option)
      this.autoPlayTool(this.chart, this.propData, 0)
    },
  },
}
</script>
<style scoped>
.no-data {
  text-align: center;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
