<style scoped>
.el-tooltip__popper {
  font-size: 16px;
}
::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}
</style>
<template>
  <div id="main">
    <div class="main-content globalOverview">
      <!-- 地图 -->
      <div id="map_container" ref="map_container" />
      <!-- 地图图例 -->
      <div class="legend">
        <div class="legend-item">
          <img src="../../assets/img/<EMAIL>" alt="" />
          <span>高风险</span>
        </div>
        <div class="legend-item">
          <img src="../../assets/img/<EMAIL>" alt="" />
          <span>中风险</span>
        </div>
        <div class="legend-item">
          <img src="../../assets/img/<EMAIL>" alt="" />
          <span>低风险</span>
        </div>
        <div class="legend-item">
          <img src="../../assets/img/<EMAIL>" alt="" />
          <span>歇业</span>
        </div>
      </div>
      <!-- 左侧图表 -->
      <div
        class="left-side"
        :class="{ 'left-hide': animationIndex == 0 && readingMode }"
      >
        <div class="box-title">
          <img src="../../assets/img/<EMAIL>" alt />
          <div>系统接入</div>
        </div>
        <div class="box1 box">
          <div style="height: 100%">
            <coverChart
              :id="'coverChart'"
              :width="'1rem'"
              :height="'100%'"
              :value="26"
            />
          </div>
          <div>
            <div>
              <div>
                <img src="../../assets/img/<EMAIL>" alt />
                <div>项目点总数</div>
              </div>
              <countTo
                style="color: #20c6e1; font-size: 0.15rem"
                :start-val="0"
                :end-val="50"
                :duration="3000"
              />
            </div>
            <div style="margin-top: 0.1rem">
              <div>
                <img src="../../assets/img/<EMAIL>" alt />
                <div>系统接入数</div>
              </div>
              <countTo
                style="color: #8fd9c8; font-size: 0.15rem"
                :start-val="0"
                :end-val="13"
                :duration="3000"
              />
            </div>
          </div>
        </div>
        <div class="box-title">
          <img src="../../assets/img/<EMAIL>" alt />
          <div>分布统计</div>
        </div>
        <div class="box2 box">
          <div class="box-main">
            <div class="top-left">
              <div class="type-name">鲁南</div>
              <div>
                <span class="type-num">6</span>
                <span class="type-com"> 个</span>
              </div>
            </div>
            <div class="top-right">
              <div class="type-name">鲁西</div>
              <div>
                <span class="type-num">2</span>
                <span class="type-com"> 个</span>
              </div>
            </div>
            <div class="center">公司分布</div>
            <div class="bottom-left">
              <div class="type-name">鲁中</div>
              <div>
                <span class="type-num">3</span>
                <span class="type-com"> 个</span>
              </div>
            </div>
            <div class="bottom-right">
              <div class="type-name">新疆/西北</div>
              <div>
                <span class="type-num">2</span>
                <span class="type-com"> 个</span>
              </div>
            </div>
          </div>
        </div>
        <div class="box-title">
          <img src="../../assets/img/<EMAIL>" alt />
          <div>动态监测</div>
        </div>
        <div class="box3 box">
          <dynamic-monitoring-charts
            :id="'monitoringChartsData'"
            :width="'1.9rem'"
            :height="'100%'"
            :prop-data="dynamicMonitoring"
          />
        </div>
        <div class="box-title">
          <img src="../../assets/img/<EMAIL>" alt />
          <div>治理主题</div>
        </div>
        <div class="box4 box">
          <div v-if="theme.length > 0" class="theme">
            <div class="line1">
              <div>{{ theme[0].theme }}</div>
              <div>{{ theme[2].theme }}</div>
            </div>
            <div class="line2">
              <div>{{ theme[1].theme }}</div>
              <div class="center">{{ theme[4].theme }}</div>
              <div>{{ theme[3].theme }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧 -->
      <div
        class="right-side"
        style="height: calc(3.5rem - (calc(1080px - 100vh) / 4 * 3))"
        :class="{ 'right-hide': animationIndex == 1 && readingMode }"
      >
        <div class="box-title" style="position: relative">
          <img src="../../assets/img/<EMAIL>" alt />
          <div>管理聚焦</div>
          <!-- <el-select v-model="day" class="select" placeholder="请选择周期" @change="getSpecialAttention(null)">
            <el-option v-for="item in dayList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select> -->
          <div class="select">{{ lastdate }} - {{ noedate }}</div>
        </div>
        <!-- <rank-list id="rankList" :width="'100%'" :height="'100%'" /> -->
        <div class="risk-ranking">
          <div class="result">
            <div v-if="riskLevel === 3" class="title">
              近期
              <span>风险</span> 较多需加强管控
            </div>
            <div v-else-if="riskLevel === 2" class="title">
              近期
              <span>风险</span> 较多需加强管控
            </div>
            <div v-else-if="riskLevel === 1" class="title">
              近期
              <span>存在风险</span> 需加强管控
            </div>
            <div v-else class="title">暂无风险</div>
          </div>
          <div class="tabs">
            <div
              v-for="(item, index) in tabs"
              :key="index"
              class="button"
              :class="tabIndex === index ? 'active' : ''"
              @click="getSpecialAttention(index)"
            >
              {{ item.title }}
              <span>({{ item.number }})</span>
            </div>
          </div>
          <div v-if="especially.length !== 0" class="listCentent">
            <div
              v-for="(item, index) in especially"
              :key="index"
              :style="{ 'animation-delay': index * 100 + 'ms' }"
              class="cententItem"
              @click="showScore(item.serviceId, item.score, item.ranking)"
            >
              <div>
                <img
                  src="../../assets/img/lightning.png"
                  alt
                  class="dangeImg"
                />
                <div
                  class="indexrisk"
                  :style="{
                    color:
                      tabIndex === 0
                        ? '#FF403F'
                        : tabIndex === 1
                          ? '#F8B240'
                          : '#45CF69',
                  }"
                >
                  当前风险指数:
                  <span class="risks">{{ parseInt(item.score) }}</span>
                </div>
              </div>
              <div class="center">项目点名称：{{ item.canteenName }}</div>
              <div class="days">
                <div>
                  <span style="color: #45cf69">低风险：</span>{{ item.lowDay }}天
                </div>
                <div>
                  <span style="color: #f8b240">中风险：</span>{{ item.mediumDay }}天
                </div>
                <div>
                  <span style="color: #ff403f">高风险：</span>{{ item.highDay }}天
                </div>
              </div>
            </div>
          </div>
          <div v-else class="no-data-img">
            <img src="../../assets/img/<EMAIL>" alt="" />
            <span>暂无数据</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 图片预览 -->
    <preview-image ref="previewImage" :url-list="previewImageUrlList" />
    <!-- 地图筛选 -->
    <map-marker-selects @postSelectsData="getCenterList" />
    <!-- 分数详情 -->
    <score-detail
      ref="scoreDetail"
      :cid="cid"
      :score="score"
      :rank="rank"
      class="score-detail"
      @dialogClosed="scoreDetailClosed"
    />
  </div>
</template>

<script>
import AMap from 'AMap'
import PreviewImage from '@/components/PreviewImage'
// import componentTitle from '@/components/title'
import viewMixins from '@/views/mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
import VerticalBarChart from '@/components/Charts/verticalBarChart'
import DynamicMonitoringCharts from '@/components/Charts/dynamicMonitoringCharts'
import ScoreDetail from '@/components/scoreDetail'
import MapMarkerSelects from '@/components/mapMarkerSelects'
import coverChart from '@/components/Charts/coverChart'
import RankList from '@/components/Charts/rankList'
import WarterBall from '@/components/Charts/warterBall'
import echarts from 'echarts'
import countTo from 'vue-count-to'
import mapImg from '@/assets/img/map.png'
import bxlh from '../../assets/img/<EMAIL>'
import xygj from '../../assets/img/<EMAIL>'
import zdgz from '../../assets/img/<EMAIL>'
import yfj from '../../assets/img/<EMAIL>'
import {
  fetchAllData,
  fetchRiskRanking,
  fetchCenterList,
  fetchSpecialAttention,
  fetchGovernanceTheme
} from '@/api/globalOverview'
import bus from '@/utils/bus'
let _this
export default {
  components: {
    PreviewImage,
    // componentTitle,
    VerticalBarChart,
    DynamicMonitoringCharts,
    coverChart,
    WarterBall,
    countTo,
    RankList,
    MapMarkerSelects,
    ScoreDetail
  },
  mixins: [viewMixins, componentMixin],
  data() {
    return {
      score: 0,
      rank: 0,
      tabIndex: 0,
      tabs: [
        {
          title: '高风险',
          number: 8
        },
        {
          title: '中风险',
          number: 16
        },
        {
          title: '低风险',
          number: 188
        }
      ],
      especially: [],
      type: 1,
      theme: [],
      previewImageUrlList: [],
      map: null,
      timer: null,
      search: '',
      centerList: [],
      curCenter: {},
      curMarker: {},
      systemCoverage: {},
      mealRating: [],
      dynamicMonitoring: [],
      eventSummary: {}, // 任务汇总
      practitioners: {}, // 从业人员
      tripartiteCompany: {}, // 三方公司
      hardwareEquipment: {}, // 硬件设备
      rankList: [],
      day: 1,
      dayList: [
        { id: 1, name: '当日' },
        { id: 7, name: '一周内' },
        { id: 30, name: '一月内' },
        { id: 365, name: '一年内' }
      ],
      delMarkerList: [],
      animationTimer: null,
      cid: undefined,
      nowdata: '',
      lastdata: ''
    }
  },
  computed: {
    // 计算风险级别
    riskLevel() {
      const total = this.tabs.reduce((prev, next) => prev + next.number, 0)
      if (total >= 10) {
        return 3
      } else if (total >= 5) {
        return 2
      } else if (total >= 1) {
        return 1
      } else {
        return 0
      }
    }

  },
  async mounted() {
    this.getTimes()
    await this.initMap()
    // this.getInfolist()
    this.getCenterList()
    this.getAllData()
    this.getRiskRanking()
    this.getSpecialAttention()
    this.getGovernanceTheme()
    this.startInterval()
    bus.$on('getSearch', (msg) => {
      this.search = msg
      this.getCenterList()
    })
    window.showScore = this.showScore
  },
  beforeDestroy() {
    this.stopInterval()
  },
  methods: {
    // 获取当天及三十天前数据
    getTimes() {
      // 获取当前日期
      var myDate = new Date()
      // var nowY = myDate.getFullYear()
      var nowM = myDate.getMonth() + 1
      var nowD = myDate.getDate()
      this.noedate =
        // nowY +
        // '/' +
        (nowM < 10 ? '0' + nowM : nowM) +
        '/' +
        (nowD < 10 ? '0' + nowD : nowD) // 当前日期

      // 获取三十天前日期
      var lw = new Date(myDate - 1000 * 60 * 60 * 24 * 30) // 最后一个数字30可改，30天的意思
      var lastY = lw.getFullYear()
      var lastM = lw.getMonth() + 1
      var lastD = lw.getDate()
      this.lastdate =
      lastY +
        '/' +
        (lastM < 10 ? '0' + lastM : lastM) +
        '/' +
        (lastD < 10 ? '0' + lastD : lastD) // 三十天之前日期
    },
    // 显示得分详情
    showScore(cid, score, rank) {
      this.stopInterval()
      this.cid = cid
      this.score = score
      this.rank = Number(rank)
      this.$refs.scoreDetail.showScoreDetail = true
    },
    scoreDetailClosed() {
      console.log(this.timer)
      if (!this.timer) {
        this.startInterval()
      }
    },
    getSpecialAttention(index) {
      this.stopInterval()
      this.$refs.scoreDetail.showScoreDetail = false
      this.tabIndex = index || 0
      const params = {
        pageNum: 1,
        pageSize: 100,
        params: {
          type: this.tabIndex + 1,
          day: 30
        }
      }
      this.especially = []
      fetchSpecialAttention(params).then((res) => {
        this.tabs[0].number = res.data.data.high
        this.tabs[1].number = res.data.data.medium
        this.tabs[2].number = res.data.data.low
        for (let i = 0; i < res.data.data.records.records.length; i++) {
          this.especially.push(res.data.data.records.records[i])
        }
      })
    },
    startInterval() {
      this.timer = setInterval(() => {
        this.tabIndex++
        if (this.tabIndex > 2) {
          this.tabIndex = 0
        }
        this.getSpecialAttention(this.tabIndex)
      }, 8000)
    },
    stopInterval() {
      clearInterval(this.timer)
      this.timer = null
      console.log(this.timer)
    },
    getGovernanceTheme() {
      fetchGovernanceTheme(this.DISTRICT_CODE['大邑县']).then((res) => {
        this.theme = res.data.data
      })
    },
    initMap() {
      const _this = this
      const map = new AMap.Map(this.$refs.map_container, {
        // center: _this.mapCenter,
        center: [106.958633, 34.43728],
        zoom: 5,
        viewMode: '3D',
        // pitch: 40,
        // rotation: -50,
        rotateEnable: true,
        zoomEnable: true,
        dragEnable: true,
        animateEnable: true,
        zooms: [5, 18],
        // features: [],
        mapStyle: 'amap://styles/darkblue', // 设置地图的显示样式
      })

      this.map = map
      this.addControl(AMap, map)
      map.on('click', function(e) {
        map.clearInfoWindow()
      })
    },
    getCenterList(selectsData) {
      console.log(selectsData)
      const obj = {
        centerName: this.search ? this.search : undefined,
        centerType: selectsData ? selectsData.centerType : undefined,
        streetCode: selectsData ? selectsData.streetCode : undefined,
        level: selectsData ? selectsData.level : undefined
      }
      fetchCenterList(obj).then((res) => {
        console.log(res.data, 89456)
        this.centerList = res.data.data
        this.creatMarker()
        this.map.clearInfoWindow()
      })
    },
    // 创建marker
    creatMarker() {
      const map = this.map
      // 重点关注
      const zdgzIcon = new AMap.Icon({
        size: new AMap.Size(48, 55),
        image: zdgz,
        imageSize: new AMap.Size(48, 55),
        imageOffset: new AMap.Pixel(0, 0)
      })
      // 需要改进
      const xygjIcon = new AMap.Icon({
        size: new AMap.Size(48, 55),
        image: xygj,
        imageSize: new AMap.Size(48, 55),
        imageOffset: new AMap.Pixel(0, 0)
      })
      // 表现良好
      const bxlhIcon = new AMap.Icon({
        size: new AMap.Size(48, 55),
        image: bxlh,
        imageSize: new AMap.Size(48, 55),
        imageOffset: new AMap.Pixel(0, 0)
      })
      // 歇业
      const yfjIcon = new AMap.Icon({
        size: new AMap.Size(48, 55),
        image: yfj,
        imageSize: new AMap.Size(48, 55),
        imageOffset: new AMap.Pixel(0, 0)
      })
      if (this.delMarkerList && this.delMarkerList.length > 0) {
        map.remove(this.delMarkerList)
        this.delMarkerList = []
      }
      this.centerList.forEach((item) => {
        const stationMarker = new AMap.Marker({
          map,
          icon: item.isHoliday
            ? yfjIcon
            : item.centerLevel === '1'
              ? bxlhIcon
              : item.centerLevel === '2'
                ? xygjIcon
                : zdgzIcon,
          zIndex: 999,
          position: [item.lng, item.lat],
          offset: new AMap.Pixel(-24, -27.5)
        })
        this.delMarkerList.push(stationMarker)
        // if (!item.isHoliday) {
        AMap.event.addListener(stationMarker, 'click', (e) => {
          this.showInfo(stationMarker, item, 0)
        })
        // }
      })
    },
    // 打开弹框
    showInfo(marker, data, index) {
      console.log(data.canteenRiskDTOS[index], 66666)
      const canteenData = JSON.parse(
        JSON.stringify(data.canteenRiskDTOS[index])
      )
      this.curCenter = data
      this.curMarker = marker
      const _this = this
      const map = this.map
      let html = `<div class="marker-content">
          <div class="title"><div title="${data.centerName}">${data.centerName}</div></div>`
      if (data.canteenRiskDTOS.length) {
        html +=
          '<div class="one"><div>选择食堂</div><select class="canteen-select" onchange="selectStages()">'
        data.canteenRiskDTOS.forEach((item, cellIndex) => {
          console.log(index === cellIndex)
          if (index === cellIndex) {
            html += `
              <option selected>${item.canteenName}</option>
            `
          } else {
            html += `
              <option>${item.canteenName}</option>
            `
          }
        })
        html += '</select></div>'
        let mealRating = ''
        let ratingClass = ''
        let color = ''
        let text = 1
        switch (data.canteenRiskDTOS[index].mealRating) {
          case 1:
            mealRating = '差'
            text = 1
            ratingClass = 'ratingClass1'
            color = '#E70013'
            break
          case 2:
            mealRating = '一般'
            text = 2
            ratingClass = 'ratingClass2'
            color = '#E76100'
            break
          case 3:
            mealRating = '较好'
            text = 3
            ratingClass = 'ratingClass3'
            color = '#F9B551'
            break
          case 4:
            mealRating = '好'
            text = 4
            ratingClass = 'ratingClass4'
            color = '#B5D368'
            break
          default:
            mealRating = '很好'
            text = 5
            ratingClass = 'ratingClass5'
            color = '#26CC6F'
            break
        }
        let scoreHtml = ``
        if (Number(data.canteenRiskDTOS[index].score) <= 20) {
          scoreHtml += `<div style="color:#45CF69">${data.canteenRiskDTOS[index].score}</div>`
        } else if (Number(data.canteenRiskDTOS[index].score) <= 60) {
          scoreHtml += `<div style="color:#F8B240">${data.canteenRiskDTOS[index].score}</div>`
        } else {
          scoreHtml += `<div style="color:#FF403F">${data.canteenRiskDTOS[index].score}</div>`
        }
        html += `
          <div class="two">
            <div>
              <div>风险排名</div>
              <div style="color:white">${data.canteenRiskDTOS[index].riskRank}</div>
            </div>
             <div style="cursor: pointer" onClick="showScore(${canteenData.serviceId},${canteenData.score},${canteenData.riskRank})">
              <div>风险指数</div>
              ${scoreHtml}
            </div>
          </div>
          <div class="three" id="markerInfo"></div>
          <div class="five">动态监测</div>
        </div>`
      } else {
        html +=
          '<div style="text-align:center;margin-top:0.5rem">暂无数据</div></div>'
      }
      const infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        offset: new AMap.Pixel(0, 0),
        isCustom: true,
        content: html
      })
      infoWindow.open(map, marker.getPosition())
      if (data.canteenRiskDTOS.length) {
        setTimeout(() => {
          const dataArr = [
            data.canteenRiskDTOS[index].person || 0,
            data.canteenRiskDTOS[index].certificate || 0,
            data.canteenRiskDTOS[index].event || 0,
            data.canteenRiskDTOS[index].food || 0,
            data.canteenRiskDTOS[index].surroundings || 0
          ]
          let newDataArr = JSON.parse(JSON.stringify(dataArr))
          newDataArr = newDataArr.sort((a, b) => b - a)
          const allCount = dataArr.reduce((a, b) => a + b)
          const chart = echarts.init(document.getElementById('markerInfo'))
          console.log(chart, 'poop')
          chart.setOption({
            // title: {
            //   subtext: allCount,
            //   subtextStyle: {
            //     fontSize: 28,
            //     color: '#FFFFFF'
            //   },
            //   textAlign: 'center',
            //   left: '48%',
            //   top: '40%'
            // },
            tooltip: {},
            radar: {
              radius: '80%',
              center: ['50%', '57%'],
              nameGap: 5, // 类目和雷达图距离
              splitNumber: 4, // 雷达图圈数设置
              name: {
                // formatter: function(value, indicator) {
                //   const name = value.substring(0, value.indexOf('\n'))
                //   const values = value.substring(
                //     value.indexOf('\n') + 1,
                //     value.length
                //   ) // 去掉\n
                //   return '{yellow|' + name + '\n}{white| ' + values + '}'
                // },
                formatter: '{value}',
                textStyle: {
                  color: '#A9DDEE'
                }
                // rich: {
                //   yellow: {
                //     color: '#DCEC95'
                //   },
                //   white: {
                //     color: '#A9DDEE'
                //   }
                // }
              },
              indicator: [
                { name: '人员', max: newDataArr[0] || 10 },
                { name: '证件', max: newDataArr[0] || 10 },
                { name: '任务', max: newDataArr[0] || 10 },
                { name: '食材', max: newDataArr[0] || 10 },
                { name: '环境', max: newDataArr[0] || 10 }
              ],
              axisLine: {
                lineStyle: {
                  color: '#3069C7'
                }
              },
              splitArea: {
                show: false,
                areaStyle: {
                  color: 'rgba(255,0,0,0)' // 图表背景的颜色
                }
              },
              splitLine: {
                show: true,
                lineStyle: {
                  width: 1,
                  color: '#2564D6' // 设置网格的颜色
                }
              }
            },
            series: [
              {
                name: '风险指数',
                type: 'radar',
                areaStyle: {
                  normal: {
                    width: 1,
                    opacity: 0.5,
                    color: '#70EDFC'
                  }
                },
                symbolSize: 6, // 拐点的大小
                itemStyle: {
                  borderColor: '#70EDFC',
                  borderWidth: 2,
                  shadowColor: '#fff',
                  shadowBlur: 8
                },
                data: [
                  {
                    value: dataArr,
                    name: '风险指数',
                    lineStyle: {
                      color: '#70EDFC'
                    }
                  }
                ]
              }
            ]
          })
        }, 100)
      }
    },
    getAllData() {
      fetchAllData(this.DISTRICT_CODE['大邑县']).then((res) => {
        const data = res.data.data
        data.systemCoverage.coverRate = parseInt(
          data.systemCoverage.coverRate * 100
        )
        this.systemCoverage = data.systemCoverage
        this.mealRating = data.mealRating.map((item) => item.ratingNum)
        data.dynamicMonitoring = data.dynamicMonitoring.sort(
          (a, b) => a.riskType - b.riskType
        )
        console.log(this.mealRating, 55656565)
        this.dynamicMonitoring = data.dynamicMonitoring.map(
          (item) => item.riskNum
        )
        this.eventSummary = data.eventSummary
        this.practitioners = data.practitioners
        this.tripartiteCompany = data.tripartiteCompany
        this.hardwareEquipment = data.hardwareEquipment
      })
    },
    getRiskRanking() {
      fetchRiskRanking(this.DISTRICT_CODE['大邑县']).then((res) => {
        this.rankList = res.data.data
      })
    }
  }
}
window.selectStages = function() {
  var selectNode = document.querySelector('.canteen-select')
  var selIndex = selectNode.selectedIndex // 获取当前选择的选项的index值
  _this.showInfo(_this.curMarker, _this.curCenter, selIndex)
}
</script>
<style lang="scss">
.globalOverview {
  .amap-info-content,
  .amap-info-outer {
    padding: 0;
  }
  .amap-info-content {
    background: transparent !important;
  }
  .amap-info-outer,
  .amap-menu-outer {
    box-shadow: none !important;
  }
  .amap-info-sharp {
    display: none !important;
  }
  .marker-content {
    width: 1.74rem;
    height: 1.67rem;
    background-image: url('../../assets/img/marker-bg.png');
    background-size: 100% 100%;
    position: relative;
    box-sizing: border-box;
    padding-left: 0.15rem;
    padding-right: 0.15rem;
    .closeInfoWindow {
      position: absolute;
      top: 0.1rem;
      right: 0.05rem;
      width: 0.15rem;
      height: 0.15rem;
      cursor: pointer;
    }
    .title {
      width: 1.24rem;
      height: 0.2rem;
      line-height: 0.2rem;
      box-sizing: border-box;
      padding-bottom: 0.01rem;
      background-image: url('../../assets/img/marker-title.png');
      background-size: 100% 100%;
      text-align: center;
      margin: 0 auto;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      > div {
        width: 0.65rem;
        height: 0.2rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .one {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 0.05rem;
      color: #ffffff;
      div {
        font-size: 0.08rem;
      }
      select {
        height: 0.18rem;
        outline: none;
        border: none;
        background-color: transparent;
        background: linear-gradient(
          to right,
          rgba(6, 36, 102, 0.2) 0%,
          rgba(6, 36, 102, 0.8) 50%,
          rgba(6, 36, 102, 0.2) 100%
        );
        color: #1dbcf2;
        font-size: 0.08rem;
        max-width: 1rem;
        padding: 0 0.05rem;
      }
    }
    .two {
      margin-top: 0.05rem;
      display: flex;
      align-items: center;
      justify-content: space-around;
      > div {
        text-align: center;
        > div:nth-of-type(1) {
          font-size: 0.08rem;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #a8bee7;
        }
        > div:nth-of-type(2) {
          font-size: 0.08rem;
          margin-top: 0.01rem;
          color: #ffd12d;
          height: 0.16rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      // > div:nth-of-type(1) {
      //   > div:nth-of-type(2) {
      //     > div {
      //       width: 0.16rem;
      //       height: 0.16rem;
      //       background-size: 100% 100%;
      //     }
      //     > .ratingClass1 {
      //       background-image: url('../../assets/img/<EMAIL>');
      //     }
      //     > .ratingClass2 {
      //       background-image: url('../../assets/img/<EMAIL>');
      //     }
      //     > .ratingClass3 {
      //       background-image: url('../../assets/img/<EMAIL>');
      //     }
      //     > .ratingClass4 {
      //       background-image: url('../../assets/img/<EMAIL>');
      //     }
      //     > .ratingClass5 {
      //       background-image: url('../../assets/img/<EMAIL>');
      //     }
      //   }
      // }
    }
    .three {
      margin-top: 0.1rem;
      // border: 1px solid #fff;
      height: 0.7rem;
      width: 100%;
    }
    .five {
      position: absolute;
      left: 0.15rem;
      top: 0.85rem;
      color: #ffffff;
    }
  }
}
.el-select-dropdown {
  z-index: 999999999 !important;
}
</style>

<style lang="scss" scoped>
@import url('../../assets/css/index.css');
@import './index.scss';
</style>

