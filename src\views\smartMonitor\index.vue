<template>
  <div id="main">
    <!-- <component-title @toggleReaddingMode="toggleReaddingMode" /> -->
    <div class="main-content smartMonitor">
      <div id="focus_toolTip" class="special_focus_toolTip" v-html="toolTopbody" />
      <div id="focus_tool" class="special_focus_tool" v-html="toolTopbody1" />
      <audio id="audio1" ref="audio1" src="../../assets/audio/1.mp3" @ended="overaudio" />
      <div v-if="showWindow" class="list-window">
        <div class="window-quxiao" @click="showWindow = false" />
        <div v-if="isMorningCheck" class="is-morning-check">
          <div class="window-title">晨检异常报警</div>
          <div class="morning-check-warning">
            <div class="left">
              <el-image :src="windowInfo.image" fit="cover" :preview-src-list="[windowInfo.image]" />
            </div>
            <div class="right">
              <div class="line">
                <div class="label">姓名：</div>
                <div class="info">{{ windowInfo.personName }}</div>
              </div>
              <div class="line">
                <div class="label">时间：</div>
                <div class="info">{{ windowInfo.time }}</div>
              </div>
              <div class="line">
                <div class="label">项目点：</div>
                <div class="info">{{ windowInfo.name }}</div>
              </div>
              <div class="temp">{{ windowInfo.bodyTemperature }}</div>
              <div class="content">{{ windowInfo.alarmContent }}</div>
            </div>
          </div>
        </div>
        <div v-else class="not-morning-check">
          <div class="window-title">{{ windowInfo.alarmType }}</div>
          <div v-if="!windowInfo.image" class="window-week" />
          <div v-else class="window-image">
            <el-image fit="cover" :preview-src-list="[windowInfo.image]" :src="windowInfo.image" alt="" />
          </div>
          <div v-if="windowInfo.isProcess=='true'" class="processed">{{ '已处理 '+windowInfo.disposalTime }}</div>
          <div v-else class="unprocessed">{{ '未处理' }}</div>
          <div class="window-info">{{ windowInfo.alarmContent }}</div>
        </div>

      </div>
      <!-- 地图 -->
      <div id="map_container" ref="map_container" />
      <!-- 地图图例 -->
      <div class="legend">
        <div class="legend-item">
          <img src="../../assets/img/<EMAIL>" alt="">
          <span>营业</span>
        </div>
        <div class="legend-item">
          <img src="../../assets/img/<EMAIL>" alt="">
          <span>歇业</span>
        </div>
      </div>
      <!-- 左侧 -->
      <div class="left-side" :class="{'left-hide':animationIndex==0 && readingMode}">
        <div class="box-title">
          <img src="../../assets/img/<EMAIL>" alt>
          <div>人员工况</div>
        </div>
        <div class="box1 box">
          <div v-if="leftTopChartData.bottomList.length>0" style="height: 100%">
            <div class="total">
              <span>从业人员总数：</span>
              <div class="nums">
                <div v-for="(item,index) in employeeStatus.total" :key="index" class="num-item" v-text="item" />
              </div>
            </div>
            <div class="person-detail">
              <div>
                <div>今日到岗</div>
                <div class="num green">
                  <countTo :start-val="0" :end-val="employeeStatus.onDuty" />
                </div>
              </div>
              <el-popover
                placement="bottom"
                :title="'未晨检人数：'+number"
                width="320"
                trigger="hover"
              >
                <div v-loading="popupLoading" class="list-box">
                  <div v-for="(item,index) in datainfo" :key="index">
                    <div class="list-icon" />
                    <div class="list-item">{{ item.centerName+' '+item.name }}</div>
                  </div>
                </div>
                <div slot="reference">
                  <div>今日晨检</div>
                  <div class="num blue">
                    <countTo :start-val="0" :end-val="employeeStatus.check" />
                  </div>
                </div>
              </el-popover>
              <el-popover
                v-if="employeeStatus.abnormal!=0"
                placement="top"
                :title="'异常人数：'+employeeStatus.abnormal"
                width="320"
                trigger="hover"
              >
                <div v-loading="popupLoading" class="list-box">
                  <div v-for="(item,index) in datainfo2" :key="index">
                    <div class="list-icon" />
                    <div class="list-item">{{ item.centerName+item.name }}</div>
                  </div>
                </div>
                <div slot="reference" style="cursor:pointer">
                  <div>今日异常</div>
                  <div class="num red">
                    <countTo :start-val="0" :end-val="employeeStatus.abnormal" />
                  </div>
                </div>
              </el-popover>
              <div v-else slot="reference" style="cursor:pointer">
                <div>今日异常</div>
                <div class="num red">
                  <countTo :start-val="0" :end-val="employeeStatus.abnormal" />
                </div>
              </div>
            </div>
            <div style="color:#159FCD">一周趋势</div>
            <verticalLineChart
              :id="'leftTopChartData'"
              style="position:relative;left:-35px"
              :width="'2.2rem'"
              :height="'45%'"
              :colors="['#00BBF5','#0053BD']"
              :prop-data="leftTopChartData"
              :chart-type="'line'"
            />
          </div>
          <div v-else class="loading">数据获取中...</div>
        </div>
        <div class="box-title">
          <img src="../../assets/img/<EMAIL>" alt>
          <div>告警汇总</div>
        </div>
        <div class="box2 box">
          <div class="warning-nums">
            <div>
              <span>总告警</span>
              <span class="num">
                <countTo :start-val="0" :end-val="alarmStatistics.total?alarmStatistics.total:0" />
              </span>
            </div>
            <div>
              <span>已处理</span>
              <span class="num">
                <countTo :start-val="0" :end-val="alarmStatistics.process?alarmStatistics.process:0" />
              </span>
            </div>
            <div>
              <span>未处理</span>
              <span class="num red">
                <countTo :start-val="0" :end-val="alarmStatistics.unProcess?alarmStatistics.unProcess:0" />
              </span>
            </div>
          </div>
          <radar-chart :width="'1.25rem'" :series-name="'告警汇总'" :prop-data="leftCenterChartData" :height="'100%'" />
        </div>
        <div class="box-title">
          <img src="../../assets/img/<EMAIL>" alt>
          <div>系统告警</div>
        </div>
        <div class="box3 box" @mouseenter="stopSwiper('systemSwiper')" @mouseleave="startSwiper('systemSwiper')">
          <div class="thead">
            <div>项目点</div>
            <div>时间</div>
            <div>告警</div>
            <div>处理</div>
          </div>
          <swiper ref="systemSwiper" :options="swiperOption" class="tbody">
            <swiper-slide
              v-for="(item, index) in systemWarningList"
              :key="index"
              :name="item.name"
              :time="item.time"
              :is-process="item.isProcess"
              :disposal-time="item.disposalTime"
              :alarm-type="item.alarmType"
              :alarm-content="item.earlyWarningContent"
              :image="item.image"
            >
              <div
                class="tr target-box"
                :is-process="item.isProcess"
                :disposal-time="item.disposalTime"
                :name="item.name"
                :time="item.time"
                :alarmType="item.alarmType"
                :alarmContent="item.earlyWarningContent"
                :image="item.image"
                :personName="item.personName"
                :bodyTemperature="item.bodyTemperature"
              >
                <div
                  class="td target-box"
                  :is-process="item.isProcess"
                  :disposal-time="item.disposalTime"
                  :name="item.name"
                  :time="item.time"
                  :alarmType="item.alarmType"
                  :alarmContent="item.earlyWarningContent"
                  :image="item.image"
                  :personName="item.personName"
                  :bodyTemperature="item.bodyTemperature"
                >{{ item.name }}</div>
                <div
                  class="td target-box"
                  :is-process="item.isProcess"
                  :disposal-time="item.disposalTime"
                  :name="item.name"
                  :time="item.time"
                  :alarmType="item.alarmType"
                  :alarmContent="item.earlyWarningContent"
                  :image="item.image"
                  :personName="item.personName"
                  :bodyTemperature="item.bodyTemperature"
                >{{ item.time }}</div>
                <div
                  class="td target-box"
                  :is-process="item.isProcess"
                  :disposal-time="item.disposalTime"
                  :name="item.name"
                  :time="item.time"
                  :alarmType="item.alarmType"
                  :alarmContent="item.earlyWarningContent"
                  :image="item.image"
                  :personName="item.personName"
                  :bodyTemperature="item.bodyTemperature"
                >{{ item.alarmType }}</div>
                <div
                  class="td"
                  :is-process="item.isProcess"
                  :disposal-time="item.disposalTime"
                  :name="item.name"
                  :time="item.time"
                  :alarmType="item.alarmType"
                  :alarmContent="item.earlyWarningContent"
                  :image="item.image"
                  :personName="item.personName"
                  :bodyTemperature="item.bodyTemperature"
                >
                  <img v-if="item.isProcess" src="../../assets/img/wisdomFoodSafety/<EMAIL>" alt>
                  <img v-else src="../../assets/img/wisdomFoodSafety/<EMAIL>" alt>
                </div>
              </div>
            </swiper-slide>
          </swiper>
        </div>
      </div>
      <!-- 右侧 -->
      <div class="right-side">
        <div class="box-title">
          <img src="../../assets/img/<EMAIL>" alt>
          <div>标准指南</div>
        </div>
        <div class="box1 box">
          <table border cellspacing="0" class="table" cellpadding="10%">
            <thead>
              <tr>
                <td colspan="3">仓库温湿度标准</td>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>仓库类别</td>
                <td>温度</td>
                <td>湿度</td>
              </tr>
              <tr>
                <td>常温</td>
                <td>0~30℃</td>
                <td>45~75%</td>
              </tr>
              <tr>
                <td>冷藏</td>
                <td>0~5℃</td>
                <td>45~75%</td>
              </tr>
              <tr>
                <td>冷冻</td>
                <td>负18℃</td>
                <td>45~75%</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="box-title">
          <img src="../../assets/img/<EMAIL>" alt>
          <div>设备工况</div>
        </div>
        <div class="box2 box">
          <el-popover
            v-if="deviceStatusTotal.thDevice.offlineList.length!=0"
            placement="top"
            :title="'温湿度传感器离线数：'+(deviceStatusTotal.thDevice.offlineList.length)"
            width="320"
            trigger="hover"
          >
            <div class="list-box">
              <div v-for="(item,index) in deviceStatusTotal.thDevice.offlineList" :key="index">
                <div class="list-icon" />
                <div class="list-item">{{ item }}</div>
              </div>
            </div>
            <div slot="reference" class="equipment">
              <div class="name">温湿度传感器</div>
              <pie-chart1 :id="'1'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[0]" />
            </div>
          </el-popover>
          <div v-else class="equipment">
            <div class="name">温湿度传感器</div>
            <pie-chart1 :id="'1'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[0]" />
          </div>
          <el-popover
            v-if="deviceStatusTotal.smogDevice.offlineList.length!=0"
            placement="top"
            :title="'烟雾探测器离线数：'+(deviceStatusTotal.smogDevice.offlineList.length)"
            width="320"
            trigger="hover"
          >
            <div class="list-box">
              <div v-for="(item,index) in deviceStatusTotal.smogDevice.offlineList" :key="index">
                <div class="list-icon" />
                <div class="list-item">{{ item }}</div>
              </div>
            </div>
            <div slot="reference" class="equipment">
              <div class="name">烟雾探测器</div>
              <pie-chart1 :id="'2'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[1]" />
            </div>
          </el-popover>
          <div v-else class="equipment">
            <div class="name">烟雾探测器</div>
            <pie-chart1 :id="'2'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[1]" />
          </div>
          <el-popover
            v-if="deviceStatusTotal.combustibleDevice.offlineList.length!=0"
            placement="top"
            :title="'可燃气体探测器离线数：'+(deviceStatusTotal.combustibleDevice.offlineList.length)"
            width="320"
            trigger="hover"
          >
            <div class="list-box">
              <div v-for="(item,index) in deviceStatusTotal.combustibleDevice.offlineList" :key="index">
                <div class="list-icon" />
                <div class="list-item">{{ item }}</div>
              </div>
            </div>
            <div slot="reference" class="equipment">
              <div class="name">可燃气体探测器</div>
              <pie-chart1 :id="'3'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[2]" />
            </div>
          </el-popover>
          <div v-else class="equipment">
            <div class="name">可燃气体探测器</div>
            <pie-chart1 :id="'3'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[2]" />
          </div>
          <el-popover
            v-if="deviceStatusTotal.cvtDevice.offlineList.length!=0"
            placement="top"
            :title="'智慧公屏离线数：'+(deviceStatusTotal.cvtDevice.offlineList.length)"
            width="320"
            trigger="hover"
          >
            <div class="list-box">
              <div v-for="(item,index) in deviceStatusTotal.cvtDevice.offlineList" :key="index">
                <div class="list-icon" />
                <div class="list-item">{{ item }}</div>
              </div>
            </div>
            <div slot="reference" class="equipment">
              <div class="name">智慧公屏</div>
              <pie-chart1 :id="'4'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[3]" />
            </div>
          </el-popover>
          <div v-else class="equipment">
            <div class="name">智慧公屏</div>
            <pie-chart1 :id="'4'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[3]" />
          </div>
          <el-popover
            v-if="deviceStatusTotal.accessControlDevice.offlineList.length!=0"
            placement="top"
            :title="'智能门禁离线数：'+(deviceStatusTotal.accessControlDevice.offlineList.length)"
            width="320"
            trigger="hover"
          >
            <div class="list-box">
              <div v-for="(item,index) in deviceStatusTotal.accessControlDevice.offlineList" :key="index">
                <div class="list-icon" />
                <div class="list-item">{{ item }}</div>
              </div>
            </div>
            <div slot="reference" class="equipment">
              <div class="name">智能门禁</div>
              <pie-chart1 :id="'5'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[4]" />
            </div>
          </el-popover>
          <div v-else class="equipment">
            <div class="name">智能门禁</div>
            <pie-chart1 :id="'5'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[4]" />
          </div>
          <el-popover
            v-if="deviceStatusTotal.morningCheckDevice.offlineList.length!=0"
            placement="top"
            :title="'智慧晨检仪离线数：'+(deviceStatusTotal.morningCheckDevice.offlineList.length)"
            width="320"
            trigger="hover"
          >
            <div class="list-box">
              <div v-for="(item,index) in deviceStatusTotal.morningCheckDevice.offlineList" :key="index">
                <div class="list-icon" />
                <div class="list-item">{{ item }}</div>
              </div>
            </div>
            <div slot="reference" class="equipment">
              <div class="name">智慧晨检仪</div>
              <pie-chart1 :id="'6'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[5]" />
            </div>
          </el-popover>
          <div v-else class="equipment">
            <div class="name">智慧晨检仪</div>
            <pie-chart1 :id="'6'" :width="pieChartWidth" :height="pieChartHeight" :prop-data="deviceStatus[5]" />
          </div>
        </div>
        <div class="box-title">
          <img src="../../assets/img/<EMAIL>" alt>
          <div>设备告警</div>
        </div>
        <div class="box3 box" @mouseenter="stopSwiper('equipmentSwiper')" @mouseleave="startSwiper('equipmentSwiper')">
          <div class="thead">
            <!-- <div>序号</div> -->
            <div>项目点</div>
            <div>时间</div>
            <div>告警</div>
            <div>处理</div>
          </div>
          <swiper ref="equipmentSwiper" :options="swiperOption" class="tbody">
            <swiper-slide
              v-for="(item, index) in deviceAlarmList"
              :key="index"
              :name="item.name"
              :time="item.time"
              :is-process="item.isProcess"
              :disposal-time="item.disposalTime"
              :alarm-type="item.alarmType"
              :alarm-content="item.alarmContent"
            >
              <div
                class="tr target-box"
                :is-process="item.isProcess"
                :disposal-time="item.disposalTime"
                :name="item.name"
                :time="item.time"
                :alarmType="item.alarmType"
                :alarmContent="item.alarmContent"
              >
                <div
                  class="td target-box"
                  :is-process="item.isProcess"
                  :disposal-time="item.disposalTime"
                  :name="item.name"
                  :time="item.time"
                  :alarmType="item.alarmType"
                  :alarmContent="item.alarmContent"
                >{{ item.name }}</div>
                <div
                  class="td target-box"
                  :is-process="item.isProcess"
                  :disposal-time="item.disposalTime"
                  :name="item.name"
                  :time="item.time"
                  :alarmType="item.alarmType"
                  :alarmContent="item.alarmContent"
                >{{ item.time }}</div>
                <div
                  class="td target-box"
                  :is-process="item.isProcess"
                  :disposal-time="item.disposalTime"
                  :name="item.name"
                  :time="item.time"
                  :alarmType="item.alarmType"
                  :alarmContent="item.alarmContent"
                >{{ item.alarmType }}</div>
                <div
                  class="td"
                  :is-process="item.isProcess"
                  :disposal-time="item.disposalTime"
                  :name="item.name"
                  :time="item.time"
                  :alarmType="item.alarmType"
                  :alarmContent="item.alarmContent"
                >
                  <img v-if="item.isProcess" src="../../assets/img/wisdomFoodSafety/<EMAIL>" alt>
                  <img v-else src="../../assets/img/wisdomFoodSafety/<EMAIL>" alt>
                </div>
              </div>
            </swiper-slide>
          </swiper>
        </div>
      </div>
      <!-- 报警弹窗 -->
      <div v-if="warningShow" class="warning-main">
        <div class="warning-title">
          <img src="../../assets/img/lightning.png" alt :class="dangering ? 'start' : ''">
          <div class="center-name">{{ popupName }}</div>
        </div>
        <div v-if="popupName !== '成都市双流区实验小学外国语学校'" class="bg-box">
          <div v-if="showWarning" class="warning-content">{{ warningContent }}</div>
          <div v-for="(item, index) in deviceListData" :key="index" class="img-area">
            <el-tooltip
              class="item"
              effect="dark"
              :content="'是否在线：'+(item.onLine ? '在线' : '离线')+'，温度：'+ (item.temperature==''?'-':item.temperature+'℃' )+'，湿度：'+(item.humidity==''? '-':item.humidity+'%')"
              placement="bottom"
            >
              <img :src="item.icon" :style="{'top': item.top +'rem', 'left': item.left+ 'rem'}" alt>
            </el-tooltip>
          </div>

        </div>
        <div v-else class="bg-box1">
          <div v-if="showWarning" class="warning-content">{{ warningContent }}</div>
          <div v-for="(item, index) in deviceListData" :key="index" class="img-area">
            <el-tooltip
              class="item"
              effect="dark"
              :content="'是否在线：'+(item.onLine ? '在线' : '离线')+'，温度：'+ (item.temperature==''?'-':item.temperature+'℃' )+'，湿度：'+(item.humidity==''? '-':item.humidity+'%')"
              placement="bottom"
            >
              <img :src="item.icon" :style="{'top': item.top +'rem', 'left': item.left+ 'rem'}" alt>
            </el-tooltip>
          </div>
        </div>
        <div class="warning-footer">
          <div>
            <img :src="icon1" alt>
            <div>摄像头</div>
          </div>
          <div>
            <img :src="icon2" alt>
            <div>可燃气体监测</div>
          </div>
          <div>
            <img :src="icon3" alt>
            <div>烟雾监测</div>
          </div>
          <div>
            <img :src="icon4" alt>
            <div>温湿度监测</div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="infoWindow">
      <div class="infoWindow-title">彭镇中学</div>
    </div>-->
    <!-- 地图筛选 -->
    <map-marker-selects @postSelectsData="getCenters" />
  </div>
</template>

<script>
import componentTitle from '@/components/title'
import viewMixins from '@/views/mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
import AMap from 'AMap'
import mapImg from '@/assets/img/map.png'
import verticalLineChart from '@/components/Charts/verticalLineChart'
import MapMarkerSelects from '@/components/mapMarkerSelects'
import RadarChart from '@/components/Charts/radarCharts'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import countTo from 'vue-count-to'
import PieChart1 from '@/components/Charts/pieChart1'
import {
  centers,
  alarmStatistics,
  deviceStatus,
  deviceAlarm,
  systemAlarm,
  employeeStatus,
  getDeviceList,
  noMorningCheck
} from '@/api/smartMonitor'
import bxlh from '../../assets/img/<EMAIL>'
import yfj from '../../assets/img/<EMAIL>'
import bus from '@/utils/bus'
let _this

export default {
  components: {
    componentTitle,
    verticalLineChart,
    // VerticalBarChart,
    RadarChart,
    Swiper,
    SwiperSlide,
    PieChart1,
    countTo,
    MapMarkerSelects
  },
  mixins: [viewMixins, componentMixin],
  data() {
    return {
      windowInfo: {
        name: '',
        time: '',
        alarmType: ''
      },
      isMorningCheck: false,
      showWindow: false,
      popupName: '彭镇初级中学',
      showWarning: 'false',
      map: '',
      totalArray: [0, 1, 2, 3],
      pieChartWidth: '70%',
      pieChartHeight: '70%',
      employeeStatus: {},
      leftTopChartData: {
        bottomList: [],
        work: [],
        abnormal: [],
        check: []
      },
      toolTopbody1: '',
      icon1: require('../../assets/img/shexiangtou.png'),
      icon2: require('../../assets/img/<EMAIL>'),
      icon3: require('../../assets/img/<EMAIL>'),
      icon4: require('../../assets/img/<EMAIL>'),
      icon5: require('../../assets/img/<EMAIL>'),
      icon6: require('../../assets/img/<EMAIL>'),
      icon7: require('../../assets/img/<EMAIL>'),
      icon8: require('../../assets/img/<EMAIL>'),
      alarmStatistics: {},
      leftCenterChartData: {
        indicator: [
          { name: '28%\n资质', max: 100 },
          { name: '21%\n行为', max: 100 },
          { name: '19%\n食材', max: 100 },
          { name: '16%\n设备', max: 100 },
          { name: '10%\n穿戴', max: 100 },
          { name: '22%\n人员', max: 100 }
        ],
        data: [65, 73, 31, 95, 51, 79]
      },
      swiperOption: {
        direction: 'vertical',
        slidesPerView: 3,
        slidesPerGroup: 1,
        loop: true,
        autoplay: {
          delay: 1000,
          disableOnInteraction: false
        },
        on: {
          click: (v) => {
            // console.log('点击了', v.target)

            const className = v.target.className
            // console.log(className)
            if (!className.includes('target-box')) return

            if (v.target.getAttribute('personName')) {
              this.isMorningCheck = true
            } else {
              this.isMorningCheck = false
            }
            const windowInfo = {
              name: v.target.getAttribute('name'),
              time: v.target.getAttribute('time'),
              isProcess: v.target.getAttribute('is-process'),
              disposalTime: v.target.getAttribute('disposal-time'),
              alarmType: v.target.getAttribute('alarmType'),
              alarmContent: v.target.getAttribute('alarmContent'),
              image: v.target.getAttribute('image'),
              personName: v.target.getAttribute('personName'),
              bodyTemperature: v.target.getAttribute('bodyTemperature')
            }
            console.log(windowInfo)
            this.windowInfo = windowInfo
            this.showWindow = true
          }
        }
      },
      deviceStatus: [
        {
          total: 0,
          online: 0
        },
        {
          total: 0,
          online: 0
        },
        {
          total: 0,
          online: 0
        },
        {
          total: 0,
          online: 0
        },
        {
          total: 0,
          online: 0
        },
        {
          total: 0,
          online: 0
        }
      ],
      deviceStatusTotal: {
        thDevice: { offlineList: [] },
        smogDevice: { offlineList: [] },
        combustibleDevice: { offlineList: [] },
        cvtDevice: { offlineList: [] },
        accessControlDevice: { offlineList: [] },
        morningCheckDevice: { offlineList: [] }
      },
      systemWarningList: [
        { name: '棠湖中学', time: '09:05', type: '穿戴告警', hasDeal: false },
        { name: '棠湖中学', time: '09:05', type: '穿戴告警', hasDeal: true },
        { name: '棠湖中学', time: '09:05', type: '穿戴告警', hasDeal: true },
        { name: '棠湖中学', time: '09:05', type: '穿戴告警', hasDeal: true },
        { name: '棠湖中学', time: '09:05', type: '穿戴告警', hasDeal: true },
        { name: '棠湖中学', time: '09:05', type: '穿戴告警', hasDeal: true },
        { name: '棠湖中学', time: '09:05', type: '穿戴告警', hasDeal: false },
        { name: '棠湖中学', time: '09:05', type: '穿戴告警', hasDeal: true },
        { name: '棠湖中学', time: '09:05', type: '穿戴告警', hasDeal: false },
        { name: '棠湖中学', time: '09:05', type: '穿戴告警', hasDeal: true },
        { name: '棠湖中学', time: '09:05', type: '穿戴告警', hasDeal: true }
      ],
      toolTopbody: '',
      marker: [
        {
          id: 1,
          lng: 103.956649,
          lat: 30.499132
        },
        {
          id: 2,
          lng: 103.9586649,
          lat: 30.599132
        },
        {
          id: 3,
          lng: 103.909649,
          lat: 30.596135
        }
      ],
      deviceAlarmList: [],
      delMarkerList: [],
      search: '',
      warningShow: false,
      warningContent: '',
      deviceListData: [],
      dangering: false,
      popupLoading: false,
      number: 0,
      datainfo: '',
      datainfo2: ''
    }
  },
  mounted() {
    this.itemMouseover()
    this.initMap()
    this.getCenters()
    this.getEmployeeStatus()
    this.getAlarmStatistics()
    this.getSystemAlarm()
    this.getDeviceStatus()
    this.getDeviceAlarm()
    bus.$on('getSearch', (msg) => {
      this.search = msg
      this.getCenters()
    })
    bus.$on('getSmartWarning', (msg) => {
      this.warningContent = msg.content
      if (msg.centerId) {
        this.getDeviceList(msg.centerId, msg.deviceId)
      }
      // this.warningShow = true
    })
    this.getQueryMsg()
    _this = this
  },
  methods: {
    async itemMouseover() {
      const data = {
        type: 1
      }
      const data2 = {
        type: 2
      }
      this.popupLoading = true
      try {
        const res = await noMorningCheck(data)
        this.number = res.data.data.noMorningCheck
        this.datainfo = res.data.data.noMorningCheckDTOS
      } finally {
        this.popupLoading = false
      }
      try {
        const res = await noMorningCheck(data2)
        this.datainfo2 = res.data.data.noMorningCheckDTOS
      } finally {
        this.popupLoading = false
      }
    },
    // 播放完
    overaudio() {
      this.$refs.audio1.pause()
    },
    // 播放报警语音
    playAlarmVoice() {
      this.$refs.audio1.play()
    },
    // 背景图出来
    changeShow() {
      // this.warningShow = true
      this.showWarning = false
    },
    getQueryMsg() {
      const { data } = this.$route.query
      if (data) {
        this.dangering = true
        this.popupName = data.centerName
        this.warningContent = data.content
        if (data.centerId) {
          this.playAlarmVoice()
          this.getDeviceList(data.centerId, data.deviceId)
        }
        // this.warningShow = true
        this.showWarning = true
      }
    },
    async initMap() {
      const _this = this
      const map = new AMap.Map(this.$refs.map_container, {
        // center: _this.mapCenter,
        zoom: 5,
        viewMode: '2D',
        // pitch: 40,
        // rotation: -50,
        rotateEnable: true,
        zoomEnable: true,
        dragEnable: true,
        zooms: [5, 18],
        center: [106.958633, 34.43728],
        // features: [],
        mapStyle: 'amap://styles/darkblue', // 设置地图的显示样式
      })
      this.map = map
      map.on('click', function(e) {
        map.clearInfoWindow()
        _this.creatMarker()
        _this.warningShow = false
        _this.dangering = false
      })
      // this.addGeoJson(this.map)

      this.addControl(AMap, map)
    },
    // 获取学校列表
    getCenters(selectsData) {
      const obj = {
        centerName: this.search ? this.search : undefined,
        centerType: selectsData ? selectsData.centerType : undefined,
        streetCode: selectsData ? selectsData.streetCode : undefined,
        level: selectsData ? selectsData.level : undefined
      }
      centers(obj).then((res) => {
        const data = res.data.data
        this.marker = data
        this.creatMarker()
      })
    },
    // 创建marker
    creatMarker() {
      const map = this.map
      // 营业
      const bxlhIcon = new AMap.Icon({
        size: new AMap.Size(48, 55),
        image: bxlh,
        imageSize: new AMap.Size(48, 55),
        imageOffset: new AMap.Pixel(0, 0)
      })
      // 歇业
      const yfjIcon = new AMap.Icon({
        size: new AMap.Size(48, 55),
        image: yfj,
        imageSize: new AMap.Size(48, 55),
        imageOffset: new AMap.Pixel(0, 0)
      })
      if (this.delMarkerList && this.delMarkerList.length > 0) {
        map.remove(this.delMarkerList)
        this.delMarkerList = []
      }
      this.marker.forEach((marker) => {
        let stationMarker = ''
        stationMarker = new AMap.Marker({
          map,
          icon: marker.isHoliday ? yfjIcon : bxlhIcon,
          zIndex: 999,
          position: [marker.lng, marker.lat],
          offset: new AMap.Pixel(-24, -27.5),
          data: marker
        })
        this.delMarkerList.push(stationMarker)
        AMap.event.addListener(stationMarker, 'click', this.showInfo)
      })
    },
    getDeviceList(id, deviceId) {
      getDeviceList({ id: id }).then((res) => {
        this.warningShow = true
        const type = id === 18
        console.log(id, type)
        const data1 = [
          { top: 0.6, left: 2.4 },
          { top: 0.85, left: 2.15 },
          { top: 1.15, left: 1.65 },
          { top: 0.85, left: 1.35 },
          { top: 1.5, left: 1.65 },
          { top: 1.15, left: 0.95 },
          { top: 1.5, left: 0.95 },
          { top: 1.5, left: 2.6 },
          { top: 0.85, left: 1.5 },
          { top: 0.85, left: 2 },
          { top: 1.8, left: 2.6 },
          { top: 1.2, left: 2.6 }
        ]
        const data2 = [
          { top: 0.9, left: 0.05 },
          { top: 0.9, left: 0.6 },
          { top: 0.9, left: 1.4 },
          { top: 0.9, left: 1.8 },
          { top: 1.4, left: 2.1 },
          { top: 1.2, left: 1.7 },
          { top: 1.2, left: 0.2 },
          { top: 1.2, left: 0.7 },
          { top: 1.2, left: 1.2 },
          { top: 1.4, left: 0.08 },
          { top: 1.05, left: 1.95 },
          { top: 1.75, left: 0.25 },
          { top: 0.95, left: 2.1 }
        ]
        console.log(this.deviceListData, deviceId)
        this.deviceListData = (res.data.data || []).map((item) => {
          item.icon =
            item.type === 1
              ? this.icon3
              : item.type === 2
                ? this.icon2
                : item.type === 3
                  ? this.icon4
                  : this.icon1
          if (deviceId && item.id === deviceId) {
            item.icon =
              item.type === 1
                ? this.icon7
                : item.type === 2
                  ? this.icon6
                  : item.type === 3
                    ? this.icon8
                    : this.icon5
          }
          return item
        })
        const arr1 = this.deviceListData.filter((item) => item.type === 1)
        const arr2 = this.deviceListData.filter((item) => item.type === 2)
        const arr3 = this.deviceListData.filter((item) => item.type === 3)
        const arr4 = this.deviceListData.filter((item) => item.type === 4)
        const arr5 = this.deviceListData.filter((item) => item.type === 1) // 烟雾
        const arr6 = this.deviceListData.filter((item) => item.type === 2) // 可燃
        const arr7 = this.deviceListData.filter((item) => item.type === 3) // 温湿度
        const arr8 = this.deviceListData.filter((item) => item.type === 4) // 摄像头
        if (type) {
          data1.forEach((item, index) => {
            if (index < 8) {
              arr4[index].top = item.top
              arr4[index].left = item.left
            } else if (index < 10 && index > 7) {
              arr3[index - 8].top = item.top
              arr3[index - 8].left = item.left
            } else {
              arr1[index - 10].top = item.top
              arr1[index - 10].left = item.left
            }
          })
        } else {
          data2.forEach((item, index) => {
            if (index < 9) {
              arr8[index].top = item.top
              arr8[index].left = item.left
            } else if (index < 11 && index > 8) {
              arr5[index - 9].top = item.top
              arr5[index - 9].left = item.left
            } else {
              arr7[index - 11].top = item.top
              arr7[index - 11].left = item.left
            }
          })
        }
        this.deviceListData = type
          ? arr1.concat(arr2).concat(arr3).concat(arr4)
          : arr5.concat(arr6).concat(arr7).concat(arr8)
      })
    },
    // 打开弹框
    showInfo(data) {
      const detail = data.target.w.data
      this.popupName = detail.centerName
      this.changeShow()
      this.getDeviceList(detail.centerId)
    },
    // 人员工况
    getEmployeeStatus() {
      employeeStatus({ }).then((res) => {
        const data = res.data.data
        for (let index = 0; index <= 4 - String(data.total).length; index++) {
          data.total = '0' + String(data.total)
        }
        this.employeeStatus = data
        for (const item of data.abnormalTrend) {
          // const checkDate = item.checkDate.slice(8, 10).indexOf(0) === 0 ? item.checkDate.slice(8, 10).slice(1) : item.checkDate.slice(8, 10)
          const checkDate = item.checkDate.slice(5, 10)
          this.leftTopChartData.bottomList.push(checkDate.split('-')[1] + '日')
          this.leftTopChartData.abnormal.push(item.abnormal)
          this.leftTopChartData.check.push(item.check)
          this.leftTopChartData.work.push(item.work)
        }
      })
    },
    // 告警汇总
    getAlarmStatistics() {
      alarmStatistics().then((res) => {
        const data = res.data.data
        this.alarmStatistics = data
        const leftCenterChartData = {
          indicator: [],
          data: []
        }
        const arr = [
          data.credentials,
          data.device,
          data.ingredients,
          data.webCam,
          data.person
        ]
        arr.sort((a, b) => b - a)
        leftCenterChartData.indicator.push({
          name: `${parseInt((data.credentials / data.total) * 100)}%\n资质`,
          max: arr[0]
        })
        leftCenterChartData.indicator.push({
          name: `${parseInt((data.device / data.total) * 100)}%\n设备`,
          max: arr[0]
        })

        leftCenterChartData.indicator.push({
          name: `${parseInt((data.ingredients / data.total) * 100)}%\n食材`,
          max: arr[0]
        })
        leftCenterChartData.indicator.push({
          name: `${parseInt((data.webCam / data.total) * 100)}%\n环境`,
          max: arr[0]
        })
        leftCenterChartData.indicator.push({
          name: `${parseInt((data.person / data.total) * 100)}%\n人员`,
          max: arr[0]
        })
        leftCenterChartData.data = [
          data.credentials,
          data.device,
          data.ingredients,
          data.webCam,
          data.person
        ]
        this.leftCenterChartData = leftCenterChartData
      })
    },
    // 系统告警
    getSystemAlarm() {
      systemAlarm().then((res) => {
        const data = res.data.data
        this.systemWarningList = data
      })
    },
    // 设备工况
    getDeviceStatus() {
      deviceStatus().then((res) => {
        const data = res.data.data
        this.deviceStatusTotal = data
        this.deviceStatus = [
          {
            total: data.thDevice.total,
            online: data.thDevice.online
          },
          {
            total: data.smogDevice.total,
            online: data.smogDevice.online
          },
          {
            total: data.combustibleDevice.total,
            online: data.combustibleDevice.online
          },
          {
            total: data.cvtDevice.total,
            online: data.cvtDevice.online
          },
          {
            total: data.accessControlDevice.total,
            online: data.accessControlDevice.online
          },
          {
            total: data.morningCheckDevice.total,
            online: data.morningCheckDevice.online
          }
        ]
      })
    },
    // 设备告警
    getDeviceAlarm() {
      deviceAlarm().then((res) => {
        const data = res.data.data
        this.deviceAlarmList = data
      })
    }
  }
}
</script>
<style lang="scss">
.smartMonitor {
  .amap-info-content,
  .amap-info-outer {
    padding: 0;
  }
  .amap-info-content {
    background: transparent !important;
  }
  .amap-info-outer,
  .amap-menu-outer {
    box-shadow: none !important;
  }
  .amap-info-sharp {
    display: none !important;
  }
  .marker-content {
    width: 1.75rem;
    height: 2.2rem;
    background-image: url('../../assets/img/marker-bg.png');
    background-size: 100% 100%;
    position: relative;
    box-sizing: border-box;
    padding-left: 0.15rem;
    padding-right: 0.15rem;
    padding-top: 0.02rem;
    .closeInfoWindow {
      position: absolute;
      top: 0.1rem;
      right: 0.05rem;
      width: 0.15rem;
      height: 0.15rem;
      cursor: pointer;
    }
    .title {
      width: 1.24rem;
      height: 0.2rem;
      line-height: 0.2rem;
      box-sizing: border-box;
      padding-bottom: 0.01rem;
      background-image: url('../../assets/img/marker-title.png');
      background-size: 100% 100%;
      text-align: center;
      margin: 0 auto;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      > div {
        width: 0.65rem;
        height: 0.2rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .one {
      color: #fff;
      font-size: 0.08rem;
      margin-top: 0.05rem;
    }
    #markerInfo {
      height: 0.75rem;
      width: 100%;
    }
    .two {
      color: #fff;
      font-size: 0.08rem;
      margin-top: 0.05rem;
    }
    .three {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      > div {
        display: flex;
        align-items: center;
        > div:nth-of-type(1) {
          font-size: 0.07rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #a8bee7;
          margin-right: 0.05rem;
        }
        > div:nth-of-type(2) {
          font-size: 0.12rem;
          font-family: PingFang SC;
          font-weight: bold;
          color: #dee67c;
        }
      }
    }
    .four {
      .table {
        width: 100%;
        .thead {
          .tr {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 0.07rem;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #7aa4ff;
            height: 0.12rem;
          }
        }
        .swiper-wrapper {
          height: 0.3rem !important;
          width: 100%;
        }
        .swiper-container {
          height: 0.3rem !important;
          width: 100%;
          .tr {
            margin-bottom: 0 !important;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
        }
        .tr {
          .td {
            text-align: center;
            font-size: 0.07rem;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .td:nth-of-type(1) {
            width: 20% !important;
          }
          .td:nth-of-type(2) {
            width: 30% !important;
          }
          .td:nth-of-type(3) {
            width: 25% !important;
          }
          .td:nth-of-type(4) {
            width: 20% !important;
          }
        }
        .swiper-container {
          .online-count {
            color: #3ae88e;
          }
          .td:nth-of-type(1) {
            display: flex;
            align-items: center;
            justify-content: center;
            > div {
              width: 0.07rem;
              height: 0.07rem;
              font-size: 0.05rem;
              font-family: Source Han Sans CN;
              font-weight: 400;
              border-radius: 50%;
              border: 1px solid #9ab0dc;
              color: #9ab0dc;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss" scoped>
@import '../../assets/css/index.css';
@import './index.scss';
</style>
