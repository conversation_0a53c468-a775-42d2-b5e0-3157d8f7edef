<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixin from './mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
export default {
  name: 'WarningCharts',
  mixins: [chartsMixin, componentMixin],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    propData: {
      require: false,
      type: Array,
      default: () => []
    }
  },
  watch: {
    propData: { // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(
        document.getElementById(this.id)
      )
      const currentIndex = 0
      this.option = {
        animationDuration: 3000,

        color: [
          '#8378EA',
          '#1D9DFF',
          '#32C5E9',
          '#9FE6B8',
          '#FFDB5C',
          '#FF9F7F',
          '#FB7293',
          '#E7BCF3'
        ],
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'item',
          formatter: '{b}<br/>{c} ({d}%)'
        },
        series: [
          {
            name: '预警类型分布',
            type: 'pie',
            radius: [20, 100],
            center: ['50%', '55%'],
            roseType: 'radius',
            data: this.propData
          }
        ]
      }
      this.chart.setOption(this.option)
      this.autoPlayTool(this.chart, this.propData, currentIndex)
    }
  }
}
</script>
