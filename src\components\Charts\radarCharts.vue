<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  name: 'RadarCharts',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    propData: {
      require: false,
      type: [Object, Array],
      default: () => { }
    },
    seriesName: {
      require: false,
      type: String,
      default: '风险指数'
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeD<PERSON>roy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.option = {
        animationDuration: 3000,

        tooltip: {},
        radar: {
          radius: '70%',
          center: ['50%', '50%'],
          nameGap: 5, // 类目和雷达图距离
          splitNumber: 4, // 雷达图圈数设置
          name: {
            formatter: function(value, indicator) {
              const values = value.substring(value.indexOf('\n') + 1, value.length) // 去掉\n
              return '' + values + ''
            },
            textStyle: {
              color: '#A9DDEE'
            }
          },
          indicator:
            this.propData.indicator ||
            [
              { name: '人员', max: 6500 },
              { name: '资质', max: 16000 },
              { name: '事件', max: 30000 },
              { name: '食材', max: 38000 },
              { name: '环境', max: 52000 }
            ],
          axisLine: {
            lineStyle: {
              color: '#3069C7'
            }
          },
          splitArea: {
            show: false,
            areaStyle: {
              color: 'rgba(255,0,0,0)' // 图表背景的颜色
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#2564D6' // 设置网格的颜色
            }
          }
        },
        series: [
          {
            name: this.seriesName,
            type: 'radar',
            areaStyle: {
              normal: {
                width: 1,
                opacity: 0.5,
                color: '#70EDFC'
              }
            },
            symbolSize: 6, // 拐点的大小
            itemStyle: {
              borderColor: '#70EDFC',
              borderWidth: 2,
              shadowColor: '#fff',
              shadowBlur: 8
            },
            data: [
              {
                value: this.propData.data || [0, 0, 0, 0,0, 0],
                lineStyle: {
                  color: '#70EDFC'
                }
              }
            ]
          }
        ]
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
