import request from '@/utils/request'
export function fetchEquipment(districtCode) {
  return request('/api/v1/web/shuangliu/bigdata/deviceStat', {
    method: 'post',
    data: {
      districtCode
    }
  })
}
export function fetchCenterMarker(data) {
  return request('/api/v1/web/shuangliu/bigdata/warningStat', {
    method: 'post',
    data
  })
}
export function fetchAiWarning(districtCode) {
  return request('/api/v1/web/shuangliu/bigdata/AIWraning-dayi', {
    method: 'post',
    data: {
      districtCode
    }
  })
}
export function fetchNewWarning(data) {
  return request('/api/v1/web/shuangliu/bigdata/newWarning', {
    method: 'post',
    data
  })
}
export function startLive(data) {
  return request('/api/v1/web/camera/startLive', {
    method: 'post',
    data
  })
}
export function randomSampling(ids) {
  return request('/api/v1/web/shuangliu/bigdata/webSpotCheck', {
    method: 'post',
    data: {
      pageNum: 1,
      pageSize: 8,
      params: {
        state: 1,
        ids,
        type: 3
      }
    }
  })
}
// export function selectAlarm(data) {
//   return request('/api/v1/web/shuangliu/bigdata/webSpotCheck', {
//     method: 'post',
//     data
//   })
// }
// export function newWarning() {
//   return request('/api/v1/web/shuangliu/bigdata/newWarning', {
//     method: 'post'
//   })
// }
export function cameraList(districtCode) {
  return request('/api/v1/web/shuangliu/bigdata/cameraList', {
    method: 'post',
    data: {
      districtCode
    }
  })
}
// 获取报警回放
export function fetchPlayback(data) {
  return request('/api/v1/web/shuangliu/bigdata/startPlayback', {
    method: 'post',
    data
  })
}
export function warningLatest(data) {
  return request('/api/v1/web/shuangliu/bigdata/newWarning', {
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 报警回放保活
export function keepPlayback(streamId) {
  return request('/api/v1/web/shuangliu/bigdata/keepPlayback', {
    method: 'post',
    data: {
      streamId
    }
  })
}
// 离线摄像头名称
export function offline(code) {
  return request('/api/v1/web/shuangliu/bigdata/offline?code=' + code, {
    method: 'get'
  })
}
