<template>
  <el-image-viewer v-if="showViewer" :append-to-body="true" :on-close="closeViewer" :url-list="urlList" />
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

export default {
  name: 'PreviewImage',
  components: {
    ElImageViewer
  },
  props: {
    urlList: { // 预览图片列表
      type: Array,
      default: null
    }
  },
  data() {
    return {
      showViewer: false
    }
  },
  methods: {
    closeViewer() {
      this.showViewer = false
    }
  }
}
</script>

<style>
</style>
