import request from '@/utils/request'
export function fetchAllData(code) {
  return request('/api/v1/web/sl/data/globalOverview/overview', {
    method: 'get',
    params: {
      code
    }
  })
}
export function fetchRiskRanking(code) {
  return request('/api/v1/web/sl/data/globalOverview/riskRanking', {
    method: 'post',
    data: {
      code,
      type: 1
    }
  })
}
export function fetchCenterList(params) {
  return request('/api/v1/web/sl/data/globalOverview/canteenRiskList', {
    method: 'get',
    params
  })
}
export function fetchSpecialAttention({ pageNum, pageSize, params }) {
  return request('/api/v1/web/sl/data/globalOverview/specialAttention', {
    method: 'post',
    data: { pageNum, pageSize, params }
  })
}
export function fetchGovernanceTheme(code) {
  return request('/api/v1/web/sl/data/globalOverview/governanceTheme?code=' + code, {
    method: 'get'
  })
}

