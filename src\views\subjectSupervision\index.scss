.main-content {
  padding-top: 0.2rem;
  box-sizing: border-box;
  padding-left: 0.19rem;
  padding-right: 0.16rem;
  padding-bottom: 0.1rem;
  display: flex;
  margin-top: 0;
  position: relative;
  top: 0.42rem;
  position: relative;
}

.special_focus_toolTip {
  padding: 0.15rem;
  z-index: 7000000000;
  position: absolute;
  display: none;
  width: 1rem;
  min-height: 0.4rem;
  background: url(../../assets/img/<EMAIL>) no-repeat;
  background-size: 100% 100%;
  background-position: 100% 100%;
}

.list-window {
  padding: 0.1rem;
  width: 4.4rem;
  height: 2.6rem;
  // border: 1px solid red;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -60%);
  z-index: 100000;
  background: url(../../assets/img/<EMAIL>) no-repeat;
  background-position: 100% 100%;
  background-size: 100% 100%;

  .window-quxiao {
    position: absolute;
    right: 0.1rem;
    top: 0.1rem;
    width: 0.35rem;
    height: 0.35rem;
    background: url(../../assets/img/<EMAIL>) no-repeat;
    background-position: 100% 100%;
    background-size: 100% 100%;
  }

  .window-title {
    margin-top: 0.1rem;
    text-align: center;
    font-size: 0.16rem;
    font-family: PingFang SC;
    font-weight: bold;
    color: #13E5F9;
    line-height: 0.17rem;
  }

  .window-week {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    margin-top: 0.1rem;
    width: 100%;
    height: 0.9rem;
    background: RGBA(11, 29, 91, 0.5);
    // opacity: 0.1;
    border-radius: 9px;

    .weekrow {
      // border: 1px solid;
      width: 100%;
      padding: 0 5%;
      color: #fff;
      box-sizing: border-box;

      >span:nth-child(1) {
        font-size: 0.08rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: #13ECFF;
      }

      >span:nth-child(2) {
        margin-left: 0.2rem;
        font-size: 0.08rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: rgba(171, 201, 255, 1);
      }
    }
  }

  .window-tab {
    // float: right;
    margin-left: 3.2rem;
    margin-top: 0.1rem;
    display: flex;
    // align-items: center;
    justify-content: space-around;
    width: 0.9rem;
    height: 0.25rem;

    .tab-buttom {
      text-align: center;
      border: 1px solid;
      color: #0886ED;
      cursor: pointer;
      border: 1px solid;
      font-size: 0.06rem;
      width: 0.38rem;
      line-height: 0.13rem;
      height: 0.13rem;
      margin: 0.02rem auto;
      border: 1px solid #0886ED;
      border-radius: 0.02rem;
    }

    .active {
      color: #14ECFF;
      text-align: center;
      width: 0.43rem;
      background: url(../../assets/img/<EMAIL>) no-repeat;
      background-size: 100% 100%;
      position: relative;
      top: -0.03rem;
      line-height: 0.19rem;
      border: none;
      height: 0.21rem;
    }
  }

  .window-list {
    display: flex;
    width: 100%;
    height: 0.95rem;
    padding-left: 5%;
    box-sizing: border-box;

    .no-center {
      padding-top: 0.1rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 3rem;
      font-size: 0.1rem;
      font-family: PingFang SC;
      font-weight: bold;
      color: #ABC9FF;
      line-height: 0.23rem;

    }

    .list-left {
      display: flex;
      flex-wrap: nowrap;
      width: 0.5rem;
      font-size: 0.08rem;
      font-family: PingFang SC;
      font-weight: bold;
      color: #13ECFF;
    }

    .right-item {
      width: 3.5rem;
      position: relative;
      display: flex;
      align-items: center;
      margin-bottom: 0.1rem;
      height: 0.1rem;

      .rowline {
        position: absolute;
        left: 0.03rem;
        top: 0.07rem;
        height: 0.15rem;
        width: 0.01rem;
        background-color: rgba(244, 191, 0, 1);
      }

      >div {
        color: rgba(171, 201, 255, 1);
        font-size: 0.08rem;
        margin-right: 0.05rem;
      }

      >div:nth-child(1) {
        width: 0.07rem;
        height: 0.07rem;
        border-radius: 50%;
        background-color: rgba(244, 191, 0, 1);
      }

      >div:nth-child(5) {
        margin-left: auto;

      }
    }
  }
}

.title {
  display: flex;
  align-items: center;
  font-size: 0.1rem;
  font-family: PingFang SC;
  font-weight: bold;
  color: #CDDFFF;
  margin-bottom: 0.07rem;

  img {
    width: 0.16rem;
    height: 0.145rem;
    margin-right: 0.02rem;
  }
}

// 左边图表
.left-main {
  position: absolute;
  left: 0.2rem;
  top: 0.1rem;
  width: 2.25rem;
  // height: calc(100% - 1.65rem);
  width: 2.25rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  // 自查汇总
  .left-main-one {
    margin-bottom: 0.1rem;

    .contents {
      box-sizing: border-box;
      width: 2.25rem;
      // height: 1.17rem;
      height: calc(1.29rem - (calc(1080px - 100vh) / 4));
      background: rgba(3, 3, 29, 0.5) !important;
      padding: 0.1rem 0.15rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .one {
        // font-size: 0.1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #20C8FF;

        .end {
          color: #A5BAE3;
        }

        .nums {
          margin: 0 0.02rem 0 0.05rem;
          display: flex;
          align-items: baseline;

          >span:last-child {
            margin-left: 0.02rem;
          }

          .words {
            font-size: 0.06rem;
            color: #70BAFF;
          }

          .num-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 0.16rem;
            height: 0.2rem;
            font-family: myFont;
            font-size: 0.14rem;
            color: #70BAFF;
            margin-right: 0.02rem;
            background: url(../../assets/img/multipleGovernance/sz.png);
            background-size: 100% 100%;
          }
        }
      }

      .two {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-top: 0.07rem;

        >div {
          text-align: center;
          font-size: 0.09rem;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #A8BEE7;
        }

        .two-one {
          >div:nth-of-type(2) {
            font-size: 0.14rem;
            font-family: PingFang SC;
            font-weight: bold;
            color: #90E1FF;
            margin-top: 0.015rem;
          }
        }

        .two-two {
          >div:nth-of-type(2) {
            font-size: 0.14rem;
            font-family: PingFang SC;
            font-weight: bold;
            color: #F5CA36;
            margin-top: 0.015rem;
          }
        }

        .two-three {
          >div:nth-of-type(2) {
            font-size: 0.14rem;
            font-family: PingFang SC;
            font-weight: bold;
            color: #33B6A6;
            margin-top: 0.015rem;
          }
        }

        .two-four {
          >div:nth-of-type(2) {
            font-size: 0.14rem;
            font-family: PingFang SC;
            font-weight: bold;
            color: #EA4A48;
            margin-top: 0.015rem;
          }
        }
      }

      .three {
        // margin-top: 0.1rem;
        font-size: 0.1rem;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #20C8FF;
      }

      .four {
        // margin-top: 0.05rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;

        .four-one {
          position: relative;
          top: -3px;
          font-size: 0.1rem;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #A8BEE7;
          // min-width: 0.35rem;
          white-space: nowrap;
        }

        .four-two {
          flex: 1;
          margin-left: 0.05rem;
          margin-right: 0.08rem;
        }

        .four-three {
          position: relative;
          top: -1px;
          font-size: 0.09rem;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #1FC8FF;
          white-space: nowrap;
        }
      }
    }
  }

  // 自查统计
  .left-main-two {

    .contents {
      box-sizing: border-box;
      width: 2.25rem;
      // height: 1.6rem;
      height: calc(1.4rem - (calc(1080px - 100vh) / 4));
      background: rgba(3, 3, 29, 0.5) !important;
      padding: 0.15rem;

      .center-name {
        width: 100%;
        line-height: 30px;
        box-sizing: border-box;
        background: linear-gradient(to right, rgba(6, 36, 102, .2) 0%, rgba(6, 36, 102, .8) 50%, rgba(6, 36, 102, .2) 100%)
      }

      .decontamination-area {
        height: 100%;
        width: 100%;
        background-color: #050C39;
        padding: 10px 20px 0;
        box-sizing: border-box;
      }

      .decontamination-chart {
        margin-top: 20px;
        height: calc(100% - 60px);
        width: 100%;
        display: flex;

        .left-title {
          width: 38%;
          height: 100%;
        }

        .right-chart {
          height: 100%;
          width: 62%;
        }
      }

      .left-title {
        // width: 100%;
        display: flex;
        justify-content: space-between;
        flex-direction: column;

        .item-flex {
          display: flex;
          align-items: center;

          .img-box {
            width: 0.25rem;
            height: 0.25rem;
            margin-right: 0.05rem;

            >img {
              width: 100%;
              height: 100%;
            }
          }

          .num {
            font-size: 0.07rem;
            font-weight: bold;
            color: #00B2FF;
          }

          .text {
            font-size: 0.08rem;
            color: #fff;
            margin-top: 0.03rem;
          }
        }
      }

      // >div {
      //   width: 100%;

      //   >div:nth-of-type(1) {
      //     display: flex;
      //     align-items: center;
      //     justify-content: space-between;
      //     width: 100%;
      //     font-size: 0.09rem;
      //     font-family: Source Han Sans CN;
      //     font-weight: 400;
      //     color: #1FC8FF;

      //     >div:nth-of-type(1) {
      //       width: 0.65rem;
      //       text-align: right;
      //     }

      //     >div:nth-of-type(2) {
      //       flex: 1;
      //       display: flex;
      //       align-items: center;
      //       justify-content: space-between;
      //       margin-left: 0.18rem;
      //     }
      //   }

      //   >div:nth-of-type(2) {

      //     font-size: 0.08rem;
      //     font-family: Source Han Sans CN;
      //     font-weight: 400;
      //     color: #A8BEE7;

      //     >div {
      //       display: flex;
      //       align-items: center;
      //       justify-content: space-between;
      //       width: 100%;
      //       height: 0.13rem;

      //       >div:nth-of-type(1) {
      //         width: 0.65rem;
      //         text-align: right;
      //       }

      //       >div:nth-of-type(2) {
      //         flex: 1;
      //         display: flex;
      //         align-items: center;
      //         justify-content: space-between;
      //         margin-left: 0.18rem;

      //         >div {
      //           font-size: 0.06rem;
      //           display: flex;
      //           align-items: center;
      //           justify-content: center;
      //           font-family: PingFang SC;
      //           font-weight: 500;
      //           color: #FFFFFF;
      //           height: 0.065rem;
      //         }

      //         .qualified {
      //           background: #32B6A6;
      //         }

      //         .unQualified {
      //           background: #CE6B6D;
      //         }
      //       }
      //     }
      //   }
      // }
    }
  }
}

// 右边图表
.right-main {
  position: absolute;
  right: 0.16rem;
  top: 0.1rem;
  width: 2.25rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 0.20rem);
  box-sizing: border-box;

  // 培训考试
  .right-main-one {
    .contents {
      box-sizing: border-box;
      width: 2.25rem;
      // height: 1.29rem;
      height: calc(1.29rem - (calc(1080px - 100vh) / 4));
      background: rgba(3, 3, 29, 0.5) !important;
      padding: 0.1rem;
      display: flex;
      // justify-content: center;
      align-items: center;

      >div {
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 100%;
        align-items: center;

        .left,
        .right {
          width: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;

          .bottom-fix {
            // position: absolute;
            // top: 0.5rem;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: -0.2rem;

            .one {
              width: 0.45rem;
              height: 0.13rem;
              line-height: 0.13rem;
              background: #192E7A;
              border: 1px solid #5297FF;
              border-radius: 0.02rem;
              font-size: 0.07rem;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #B2C9F6;
              text-align: center;
            }

            .two {
              width: 0.85rem;
              height: 0.25rem;
              background: #081B51;
              border: 1px solid #02427C;
              border-radius: 0.02rem;
              display: flex;
              justify-content: space-around;
              align-items: center;
              margin-top: 0.05rem;

              >div {
                text-align: center;

                >div:nth-of-type(1) {
                  font-size: 0.07rem;
                  font-family: Source Han Sans CN;
                  font-weight: 400;
                  color: #B2C9F6;
                }

                >div:nth-of-type(2) {
                  font-weight: bold;
                  color: #F0C635;
                }
              }
            }
          }

          .lines {
            background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
            background-size: 100% 100%;
            height: 0.25rem;
            width: 0.01rem;
          }
        }

        .lines {
          background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
          background-size: 100% 100%;
          height: 100%;
          width: 0.01rem;
        }

        // .right {
        //   width: 50%;
        // }


      }


    }
  }

  // 事件分类
  .right-main-two {
    .contents {
      box-sizing: border-box;
      width: 2.25rem;
      // height: 1.4rem;
      height: calc(1.4rem - (calc(1080px - 100vh) / 4));
      background: rgba(3, 3, 29, 0.5) !important;
      padding: 0.1rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  // 事件分析
  .right-main-three {
    .contents {
      box-sizing: border-box;
      width: 2.25rem;
      // height: 1.05rem;
      height: calc(1.05rem - (calc(1080px - 100vh) / 4));
      background: rgba(3, 3, 29, 0.5) !important;
      padding: 0.1rem;
      display: flex;
      align-items: center;

      >div:nth-of-type(1) {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-right: 0.085rem;
        box-sizing: border-box;
        padding: 0.05rem 0;

        >div {
          >div:nth-of-type(1) {
            font-size: 14px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
            margin-bottom: 0.02rem;
          }

          >div:nth-of-type(2) {
            font-size: 0.14rem;
            font-family: PingFang SC;
            font-weight: bold;
          }
        }
      }

      >div:nth-of-type(2) {
        height: 100%;
        flex: 1;
        display: flex;
        align-items: center;

        >div {
          width: 50%;
          height: 100%;

          >div {
            text-align: center;
            font-size: 0.08rem;
            font-family: PingFang SC;
            font-weight: bold;
            color: #CDDFFF;
          }

          .text {
            margin-top: 0.05rem;
          }
        }
      }
    }
  }
}

// 下边图表
.bottom-main {
  position: absolute;
  left: 0.16rem;
  bottom: 0.1rem;
  width: 6.84rem;

  .contents {
    box-sizing: border-box;
    width: 6.84rem;
    // height: 1.05rem;
    height: calc(1.05rem - (calc(1080px - 100vh) / 4));
    background: rgba(3, 3, 29, 0.5) !important;
    padding: 0.1rem;
    display: flex;
    align-items: center;
    justify-content: center;

    >div {
      width: 100%;

      .thead,
      .tbody {
        display: flex;
        align-items: center;

        .tr {
          height: 0.25rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;

          .td {
            font-size: 0.08rem;
            height: 0.25rem;
            line-height: 0.25rem;
            text-align: center;
            padding: 0 0.0.2rem;
            box-sizing: border-box;
            // border: 1px solid #fff;
            // display: flex;
            // align-items: center;
            // justify-content: center;
          }

          .td:nth-of-type(1) {
            width: 6%;
          }

          .td:nth-of-type(2) {
            width: 12%;
          }

          .td:nth-of-type(3) {
            width: 8%;
          }

          .td:nth-of-type(4) {
            width: 12.5%;
          }

          .td:nth-of-type(5) {
            width: 12.5%;
          }

          .td:nth-of-type(6) {
            width: 21.5%;
          }

          .td:nth-of-type(7) {
            width: 16%;
          }

          .td:nth-of-type(8) {
            width: 12.5%;
          }
        }
      }

      .tbody {
        font-size: 0.08rem;
        font-family: PingFang SC;
        font-weight: 500;
        color: #ABC9FF;
        height: 0.5rem;

        .tr:nth-child(even) {
          // background-color: rgba(6, 36, 102, 0.8);
          background: linear-gradient(to right, rgba(6, 36, 102, .2) 0%, rgba(6, 36, 102, .8) 50%, rgba(6, 36, 102, .2) 100%);
        }

        .tr {
          .td {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .td:nth-of-type(1) {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;

            >div {
              width: 0.1rem;
              height: 0.1rem;
              font-size: 0.075rem;
              font-family: PingFang SC;
              // font-weight: bold;
              border-radius: 50%;
              border: 1px solid #ABC9FF;
              color: #ABC9FF;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .td:nth-of-type(7) {
            display: block;
            line-height: 0.11rem !important;
            margin-top: 0.05rem;

            >div {

              width: 100%;
              box-sizing: border-box;
              padding: 0 0.15rem;
              position: relative;

              .one {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 0.04rem;

                .line {
                  width: 0.14rem;
                  height: 0;
                  border-bottom: 1px solid #2060AC
                }

                .line-dashed {
                  width: 0.14rem;
                  height: 0;
                  border-bottom: 1px dashed #2060AC
                }

                >div:nth-child(odd) {
                  width: 0.08rem;
                  height: 0.08rem;
                  background: #0B448F;
                  border-radius: 50%;
                  position: relative;

                  >div {
                    width: 0.25rem;
                    position: absolute;
                    left: 50%;
                    top: 0.08rem;
                    transform: translateX(-50%);
                    font-size: 0.07rem;
                    font-family: PingFang SC;
                    // font-weight: bold;
                    color: #ABC9FF;
                  }
                }

                .success {
                  background: #98FDFF;
                }

                .processing {
                  background: #FFE905;
                }
              }

              // .line {
              //   width: 100%;
              //   height: 1px;
              //   margin-top: -0.04rem;
              //   background: #205FAB;
              // }
            }
          }
        }
      }

      .thead {
        font-family: PingFang SC;
        font-weight: bold;
        color: #ABC9FF;

        .tr {
          // height: 0.2rem !important;
          background: linear-gradient(to right, rgba(6, 36, 102, .2) 0%, rgba(6, 36, 102, .8) 50%, rgba(6, 36, 102, .2) 100%);

          .td {
            // height: 0.2rem !important;
          }
        }

      }
    }
  }
}