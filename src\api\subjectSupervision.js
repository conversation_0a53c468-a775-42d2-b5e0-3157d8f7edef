import request from '@/utils/request'
export function fetchCenterList(params) {
  return request('/api/v1/web/sl/data/main/services', {
    method: 'get',
    params
  })
}
export function fetchCenterInfo(centerId) {
  return request('/api/v1/web/sl/data/main/infoByCenter', {
    method: 'post',
    data: {
      params: centerId
    }
  })
}
export function fetchSelfCheckGather() {
  return request('/api/v1/web/sl/data/main/selfCheckGather', {
    method: 'post'
  })
}
export function fetchSelfCheckStatistics() {
  return request('/api/v1/web/sl/data/main/selfStatistics', {
    method: 'post'
  })
}
export function fetchTrainingExamination() {
  return request('/api/v1/web/sl/data/main/trainingExam', {
    method: 'post'
  })
}
export function fetchEventSource() {
  return request('/api/v1/web/sl/data/main/eventSource', {
    method: 'post'
  })
}
export function fetchEventList() {
  return request('/api/v1/web/sl/data/main/eventList', {
    method: 'post'
  })
}
export function fetchEventAnalysis() {
  return request('/api/v1/web/sl/data/main/eventAnalysis', {
    method: 'post'
  })
}
export function fetchDisinfectRecord(code) {
  return request('/api/v1/web/bigData/disinfectRecord?serviceId=' + code, {
    method: 'get'
  })
}
export function taskDetails(id) {
  return request('/api/v1/web/sl/data/main/taskDetails?id=' + id, {
    method: 'get'
  })
}
export function unqualified(code) {
  return request('/api/v1/web/sl/data/monitoring/unqualified?code=' + code, {
    method: 'get'
  })
}
