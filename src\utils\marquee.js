/* eslint-disable */
// ;(function (root, factory) {
//   if (typeof exports === 'object' && typeof module === 'object') {
//     module.exports = factory();
//   } else if (typeof exports === 'object') {
//     exports["Marquee"] = factory();
//   } else if (typeof define === 'function' && define.amd) {
//     define([], factory);
//   } else {
//     root.Marquee = factory();
//   }
// }

//兼容性
var lastTime = 0;
var vendors = ['webkit', 'moz', 'ms', 'o'];

for(var x = 0; x < vendors.length && !window.requestAnimationFrame; ++x) {
  window.requestAnimationFrame = window[vendor[x] + 'RequestAnimationFrame'];
  window.cancelAnimationFrame = window[vendor[x] + 'CancelAnimationFrame'] || window[vendor[x] + 'CancelRequestAnimationFrame'];
}

if (!window.requestAnimationFrame) {
  window.requestAnimationFrame = function(callback) {
    var currTime = Date.now();
    var timeToCall = Math.max(0, 16 - (currTime - lastTime));
    var id = window.setTimeout(function() { 
        callback(currTime + timeToCall);
    }, timeToCall);
    lastTime = currTime + timeToCall;
    return id;
  }
}

if (!window.cancelAnimationFrame) {
  window.cancelAnimationFrame = function(id) {
    clearTimeout(id);
  }
}

const Marquee = function () {
  'use strict';

  class Marquee {
    constructor(element, options) {
      this.element = element;
      this.selector = options.selector;
      this.speed = element.dataset.speed || 0.25;
      this.pausable = element.dataset.pausable || true;
      this.reverse = element.dataset.reverse || false;
      this.paused = false;
      this.parent = element.parentElement;
      this.parentProps = this.element.getBoundingClientRect();
      this.content = element.children[0];
      this.innerContent = this.content.innerHTML;
      this.wrapStyles = '';
      this.offset = 0;

      this._setupContent();
      this._setupEvents();
    }


    _setupContent() {
      this.content.classList.add(`${this.selector}__copy`);
      this.content.style.display = 'block';
      this.contentHeight = this.content.scrollHeight;

      this.requiredReps = this.contentHeight > this.parentProps.height ? 2 : Math.ceil((this.parentProps.height - this.contentHeight) / this.contentHeight) + 1;

      for (let i = 0; i < this.requiredReps; i++) {
        this._createClone();
      }

      if (this.reverse) {
        this.offset = this.contentHeight * -1;
      }

      this.element.classList.add('is-init');
    }

    _setupEvents() {
      this.element.addEventListener('mouseenter', () => {
        if (this.pausable) this.paused = true;
      });

      this.element.addEventListener('mouseleave', () => {
        if (this.pausable) this.paused = false;
      });
    }

    _createClone() {
      const clone = this.content.cloneNode(true);
      clone.style.display = 'block';
      clone.classList.add(`${this.selector}__copy`);
      this.element.appendChild(clone);
    }

    animate() {
      if (!this.paused) {
        const isScrolled = this.reverse ? this.offset < 0 : this.offset > this.contentHeight * -1;
        const direction = this.reverse ? -1 : 1;
        const reset = this.reverse ? this.contentHeight * -1 : 0;

        if (isScrolled) this.offset -= this.speed * direction;
        else this.offset = reset;
        this.element.style.transform = `translate(0, ${this.offset}px) translateZ(0)`;
      }
    }

    _refresh() {
      this.contentHeight = this.content.scrollHeight;
    }

    repopulate(difference, isLarger) {
      this.contentHeight = this.content.scrollHeight;

      if (isLarger) {
        const amount = Math.ceil(difference / this.contentHeight) + 1;

        for (let i = 0; i < amount; i++) {
          this._createClone();
        }
      }
    }

    static refresh(index) {
      MARQUEES[index]._refresh();
    }

    static refreshAll() {
      for (let i = 0; i < MARQUEES.length; i++) {
        MARQUEES[i]._refresh();
      }
    }

    static init(options = { selector: 'Marquee' }) {
      // window.MARQUEES = [];
      // const marquees = Array.from(document.querySelectorAll(`.${options.selector}`));
      const marquee = document.querySelectorAll(`.${options.selector}`)[0];
      let previousHeight = window.innerHeight;
      let timer;
      
      // for (let i = 0; i < marquees.length; i++) {
      //   const marquee = marquees[i];
      //   const instance = new Marquee(marquee, options);
      //   MARQUEES.push(instance);
      // }
      const instance = new Marquee(marquee, options);
      instance._refresh()

      animate();

      function animate() {
        // for (let i = 0; i < MARQUEES.length; i++) {
        //   MARQUEES[i].animate();
        // }
        instance.animate();
        requestAnimationFrame(animate);
      }

      window.addEventListener('resize', () => {
        clearTimeout(timer);

        timer = setTimeout(() => {
          const isLarger = previousHeight < window.innerHeight;
          const difference = window.innerHeight - previousHeight;
          instance.repopulate(difference, isLarger);
          // for (let i = 0; i < MARQUEES.length; i++) {
          //   MARQUEES[i].repopulate(difference, isLarger);
          // }

          previousHeight = this.innerHeight;
        });
      }, 250);
    }
  }

  return Marquee

}

export default Marquee()
