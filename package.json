{"name": "canteen-big-data-3.0", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "npm eslint && npm stylelint", "eslint": "eslint --ext .js,.vue src --fix", "stylelint": "stylelint src/**/*.{html,vue,css,sass,scss} --fix"}, "dependencies": {"@antv/g6": "^4.0.4", "@liveqing/liveplayer": "^2.1.5", "animate.css": "^4.1.1", "axios": "^0.19.2", "copy-webpack-plugin": "^4.6.0", "core-js": "^3.6.5", "echarts": "^4.8.0", "echarts-liquidfill": "^2.0.6", "echarts-wordcloud": "^1.1.3", "element-ui": "^2.13.2", "jquery": "^3.5.1", "less-loader": "^6.2.0", "moment": "^2.28.0", "jsencrypt": "^3.3.2", "node-rsa": "^1.1.1", "register-service-worker": "^1.7.1", "sass": "^1.26.10", "scss": "^0.2.4", "stompjs": "^2.3.3", "swiper": "^5.3.6", "vue": "^2.6.11", "vue-awesome-swiper": "^4.1.1", "vue-count-to": "^1.0.13", "vue-router": "^3.2.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-eslint": "~4.4.0", "@vue/cli-plugin-pwa": "~4.4.0", "@vue/cli-plugin-router": "~4.4.0", "@vue/cli-service": "~4.4.0", "@vue/eslint-config-airbnb": "^6.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.8.0", "eslint-import-resolver-webpack": "^0.13.2", "eslint-plugin-import": "^2.25.4", "eslint-plugin-vue": "^6.2.2", "husky": "^7.0.4", "lint-staged": "^12.3.1", "prettier": "^3.2.5", "sass-loader": "^8.0.2", "stylelint": "^14.3.0", "stylelint-config-recess-order": "^3.0.0", "stylelint-config-standard": "^24.0.0", "stylelint-prettier": "^2.0.0", "stylelint-scss": "^4.1.0", "vue-template-compiler": "^2.6.11"}, "rules": {"no-unused-vars": "off"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint", "git add"]}, "_id": "canteen-big-data-3.0@0.1.0", "readme": "ERROR: No README data found!"}