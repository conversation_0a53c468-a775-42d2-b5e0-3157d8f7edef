import { debounce } from '@/utils'

export default {
  data() {
    return {
      $_sidebarElm: null,
      chartsTimer: null,
      option: {},
      animationTimer: null,
      doNotRedraw: false
    }
  },
  mounted() {
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    // 统一动画重载
    if (!this.doNotRedraw) {
      this.animationTimer = setInterval(() => {
        this.chart.clear()
        this.chart.setOption(this.option)
      }, 8000)
    }

    window.addEventListener('resize', this.__resizeHandler)

    this.$_sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.$_sidebarElm && this.$_sidebarElm.addEventListener('transitionend', this.$_sidebarResizeHandler)
  },
  beforeDestroy() {
    clearInterval(this.animationTimer)
    window.removeEventListener('resize', this.__resizeHandler)
    this.$_sidebarElm && this.$_sidebarElm.removeEventListener('transitionend', this.$_sidebarResizeHandler)
  },
  methods: {
    // use $_ for mixins properties
    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential
    $_sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    /**
     * echarts加载，暂无数据，隐藏加载方法
     * @param {echarts 实例} myChart
     */
    showLoading(myChart) {
      myChart.showLoading({
        text: '加载中',
        color: '#fff',
        textColor: '#fff',
        maskColor: 'rgba(3,169,255,0.1)',
        zlevel: 10
      })
    },
    hideLoading(myChart) {
      myChart.showLoading({
        text: '暂无数据',
        color: 'rgba(3,169,255,0)',
        textColor: '#fff',
        maskColor: 'rgba(3,169,255,0)',
        zlevel: 10
      })
      return
    },
    cancelLoading(myChart) {
      myChart.hideLoading()
    },

    /**
     * @param {chartIns} echarts实例
     * @param {chartDate} echarts数据
     * @param {curIndex} 当前要展示的数据index
     */
    autoPlayTool(chartIns, chartDate, curIndex, seriesIndex = 0, speed = 1000) {
      console.log(777777777)
      clearInterval(this.chartsTimer)
      const setpfun = () => {
        chartIns.dispatchAction({
          type: 'downplay',
          seriesIndex: seriesIndex,
          dataIndex: curIndex - 1 < 0 ? chartDate.length - 1 : curIndex - 1
        })
        chartIns.dispatchAction({
          type: 'highlight',
          seriesIndex: seriesIndex,
          dataIndex: curIndex
        })
        chartIns.dispatchAction({
          type: 'showTip',
          seriesIndex: seriesIndex,
          dataIndex: curIndex
        })
        curIndex === chartDate.length - 1 ? curIndex = 0 : curIndex++
      }
      this.chartsTimer = setInterval(setpfun, speed)
      chartIns.on('mouseover', () => {
        console.log(888888888888)
        clearInterval(this.chartsTimer)
        console.log(this.chartsTimer)
      })
      chartIns.on('globalout', () => {
        console.log(66666666)
        clearInterval(this.chartsTimer)
        this.chartsTimer = setInterval(setpfun, speed)
      })
    }
  }
}
