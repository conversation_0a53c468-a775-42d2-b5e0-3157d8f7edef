* {
  margin: 0;
  padding: 0;
}

ul {
  list-style: none;
}

.BMap_cpyCtrl {
  display: none;
}

#main {
  overflow: hidden;
}

.main-content {
  height: calc(100vh - 0.42rem);
  margin-top: 0.42rem;
  width: 100%;
  box-sizing: border-box;
}

.header {
  height: 0.42rem;
  /* background: url('../../assets/img/header.png') no-repeat; */
  /* background: url('../../assets/gif/header.gif') no-repeat;
  background-size: 100% 100%; */
  position: relative;
  color: #39D6FE;
  z-index: 99;
  display: flex;
  /* align-items: flex-start; */
}

.font16 {
  font-size: 16px;
}

.font18 {
  font-size: 18px;
}

.font20 {
  font-size: 20px;
}

.datetime {
  width: 25%;
  display: flex;
}

.time {
  text-align: right;
  width: 100px;
  font-size: 20px;
  font-weight: bold;
  padding: 10px 15px;
  float: left;
}

.date {
  text-align: left;
  width: 50%;
  font-size: 14px;
  padding: 5px;
  float: left;
}

.week {
  /* padding-left: 15px; */
  font-size: 14px;
}

.food {
  float: left;
  padding: 10px 0px;
  cursor: pointer;
}

.place-orgin {
  position: absolute;
  right: 200px;
  top: 10px;
  cursor: pointer;
}

.back {
  position: absolute;
  right: 200px;
  top: 10px;
  cursor: pointer;
}

.fullsize {
  width: 20px;
  height: 20px;
  position: absolute;
  right: 15px;
  top: 10px;
}

.fullsize>button {
  border: none;
  outline: none;
  background: url('../../assets/img/fullsize.png') no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.logout {
  position: absolute;
  top: 10px;
  right: 100px;
  cursor: pointer;
}

#map_container {
  width: 100%;
  height: 100%;
  background: url('../img/bg2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: 0 0;
}

/* 高德地图 */
.amap-info-content,
.amap-info-outer {
  padding: 0;
}

.amap-info-content {
  background: transparent !important;
}

.amap-info-outer,
.amap-menu-outer {
  box-shadow: none !important;
}

.main {
  width: 100%;
  height: 100%;
  background: url('../img/bg2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: 0 0;
}

.buttons {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 200px;
  display: -ms-flexbox;
  /* IE 10 */
  display: -webkit-flex;
  /* Chrome 21+ */
  display: flex;
  padding: 0 1%;
  box-sizing: border-box;
  justify-content: space-between
}

.buttons>div {
  width: 7%;
  height: 88%;
  /* background-size: 100% 100% !important */
}

.frame {
  position: relative;
  background: rgba(1, 18, 71, 0.6);
  border: 1px solid rgba(33, 109, 253, 1);
  box-shadow: 0px 0px 60px 0px rgba(1, 9, 96, 0.5);
  margin: 20px 0;
}

.frame-corner-right,
.frame-corner-left {
  display: block;
  width: 20px;
  height: 20px;
  border: 2px solid #00FCF9;
  position: absolute;
  top: -2px;
  border-bottom: none
}

.frame-corner-right {
  border-left: none;
  right: -2px;
}

.frame-corner-left {
  border-right: none;
  left: -2px;
}

.frame-title {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  height: 45px;
  width: 200px;
  font-size: 24px;
  line-height: 45px;
  /* background: url('../../assets/img/title_bg.png'); */
  background-size: 100% 100%;
  z-index: 99;
}

.charts {
  height: 100%;
}

.map-legend-warn {
  position: absolute;
  bottom: 18%;
  left: 29%;
  width: 12%;
  display: flex;
  z-index: 9;
  pointer-events: none;
}

.map-legend-fine {
  position: absolute;
  bottom: 6%;
  left: 29%;
  width: 12%;
  display: flex;
  z-index: 9;
  pointer-events: none;
}

.legend-left-number {
  width: 55px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  color: #FFF;
}

.legend-left-rline {
  width: 0;
  height: 0;
  border-width: 0 3px 50px;
  border-style: solid;
  border-color: transparent transparent #f00;
}

.legend-left-rcircle {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 35px;
  height: 20px;
  /* border: 1px solid #f00;
  border-radius: 50%; */
  /* background: url("../gif/warning.gif") no-repeat 100%; */
}

/* .legend-left-rcircle div{
  width: 6px;
  height: 2px;
  background-color: #f00;
  margin: 0 auto;
  margin-top: 1px;
} */
.legend-left-gline {
  width: 0;
  height: 0;
  border-width: 0 3px 50px;
  border-style: solid;
  border-color: transparent transparent #0f0;
}

.legend-left-gcircle {
  position: absolute;
  bottom: -20px;
  left: 10px;
  width: 35px;
  height: 20px;
  /* border: 1px solid #0f0;
  border-radius: 50%; */
  /* background: url("../gif/seleted.gif") no-repeat 100%; */
}

/* .legend-left-gcircle div{
  width: 6px;
  height: 2px;
  background-color: #0f0;
  margin: 0 auto;
  margin-top: 1px;
} */
.map-legend-left {
  position: relative;
  margin-right: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 65px;
  justify-content: space-between;
}

.map-legend-right {
  text-align: left;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
}

.map-point {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 65px;
  justify-content: space-between;
  z-index: 10;
  pointer-events: none;
}

/* ::-webkit-scrollbar{
  width: 8px;
  height: 8px;
  background-color: transparent;
}
::-webkit-scrollbar-track{
  border-radius: 10px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb{
  height: 10px;
  border-radius: 10px;
  background-color: #33B8E4;
} */
.numbers {
  display: inline-block;
  color: #00E6FF;
  font-size: 35px;
  font-family: digital-7;
  border: 1px solid #00E6FF;
  width: 34px;
  height: 45px;
  line-height: 47px;
  text-align: center;
}

.red {
  color: red
}

.green {
  color: green
}

@font-face {
  font-family: myFont;
  src: url("../../assets/fonts/300-CAI978-2.ttf");
}

@font-face {
  font-family: HYZongYiJ;
  src: url("../../assets/fonts/HYZongYiTiJ.otf");
}

/* 涟漪效果小点类 */
.dot {
  transform: rotateX(60deg);
  content: ' ';
  z-index: 2;
  width: 10px;
  height: 10px;
  background-color: #ff4200;
  border-radius: 50%;
}

.dot:after {
  content: ' ';
  position: absolute;
  z-index: 1;
  width: 10px;
  height: 10px;
  background-color: #ff4200;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 0, 0, .3) inset;
  -webkit-animation-name: 'ripple';
  -webkit-animation-duration: 1s;
  -webkit-animation-timing-function: ease;
  -webkit-animation-iteration-count: infinite;
}

.dot:before {
  content: ' ';
  position: absolute;
  z-index: 1;
  width: 10px;
  height: 10px;
  background-color: #ff4200;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 0, 0, .3) inset;
  -webkit-animation-name: 'ripple2';
  -webkit-animation-duration: 1s;
  -webkit-animation-timing-function: ease;
  -webkit-animation-delay: 0.5s;
  -webkit-animation-iteration-count: infinite;
}

@keyframes ripple {
  0% {
    left: 5px;
    top: 5px;
    opcity: 75;
    width: 0;
    height: 0;
  }

  100% {
    left: -20px;
    top: -20px;
    opacity: 0;
    width: 50px;
    height: 50px;
  }
}

@keyframes ripple2 {
  0% {
    left: 5px;
    top: 5px;
    opcity: 50;
    width: 0;
    height: 0;
  }

  100% {
    left: -20px;
    top: -20px;
    opacity: 0;
    width: 50px;
    height: 50px;
  }
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 0.05rem;
  background-color: rgba(8, 33, 81, 0.7);
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  border-radius: 0;
  background-color: rgba(8, 33, 81, 0.7);
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 0;
  background-color: rgba(4, 162, 235, 1);
}

/*定义最上方和最下方的按钮*/
::-webkit-scrollbar-button {
  background-color: rgba(8, 33, 81, 0.7);
  height: 0;
}

.box {
  background: rgba(4, 5, 26, 0.3);
  box-shadow: inset 0 0 30px #164a9d90;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}



/* ::-webkit-scrollbar-track{
  border-radius: 8px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb{
  height: 8px;
  border-radius: 8px;
  background-color: #216dfd;
} */

/* 巡阅模式动画 */
.left-hide {
  left: -30% !important;
}

.right-hide {
  right: -30% !important;
}

.left-side,
.right-side {
  transition: all 1s;
}

.swiper-container-free-mode .swiper-wrapper {
  -webkit-transition-timing-function: linear !important;
  /*之前是ease-out*/
  -moz-transition-timing-function: linear;
  -ms-transition-timing-function: linear;
  -o-transition-timing-function: linear;
  transition-timing-function: linear;
  margin: 0 auto;
}

.el-select .el-input__inner {
  background: transparent;
  color: #ABCAFF;
}

.el-select-dropdown__wrap {
  background: #012474;
}

.el-select-dropdown {
  border: none
}

.el-select-dropdown__empty {
  background: #012474;
  color: #ABCAFF;
}

.popper__arrow {
  background: #012474;
}

.el-select-dropdown__item {
  color: #ABCAFF
}

.el-select-dropdown__item:hover {
  background: #1368C1 !important;
}

.el-input .el-input__inner {
  background: transparent;
  border: none;
  color: #ABCAFF;
}

.popper__arrow:after {
  border-bottom-color: #012474 !important;
  border-top-color: #012474 !important
}

.el-popover {
  padding: 0.15rem !important;
  background: url(../img/<EMAIL>) no-repeat;
  background-size: 100% 100%;
  /* max-height: 0.5rem !important; */
  border: none;
  overflow: hidden;
}

.el-popover__title {
  font-size: 0.08rem;
  font-weight: bold;
  color: #13ECFF;
}

.loading {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.list-icon {
  background-color: #BE1B24;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 10px;
}

.list-box {
  max-height: 80px;
  overflow-y: scroll;
  overflow-x: hidden;
}

.list-box>div {
  display: flex;
  align-items: center;
}

.list-item {
  font-size: 0.07rem;
  font-weight: bold;
  color: #D4DCEC;
}

.legend {
  position: absolute;
  display: flex;
  bottom: 0.3rem;
  right: 2.8rem;
}

.legend-item {
  margin-left: 0.1rem;
  display: flex;
  align-items: center;
}

.amap-controlbar-zoom {
  display: none;
}

.amap-luopan,
.amap-luopan-bg {
  background: url(../img/luopan.png) -44px -60px no-repeat;
}

.amap-compass {
  background: url(../img/luopan.png) -462px -52px no-repeat;
}

.amap-pointers {
  background: url(../img/luopan.png) -562px -52px no-repeat;
}

.amap-pitchDown,
.amap-pitchUp {
  background: url(../img/luopan.png) -605px -98px no-repeat;
}

.amap-rotateLeft,
.amap-rotateRight {
  background: url(../img/luopan.png) -603px -154px no-repeat;
}