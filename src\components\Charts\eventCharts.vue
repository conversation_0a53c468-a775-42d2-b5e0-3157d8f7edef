<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  name: 'EventCharts',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    propData: {
      require: false,
      type: Object,
      default: () => { }
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  created() {
    this.doNotRedraw = true
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.option = {
        animationDuration: 3000,
        color: ['#98FDFF', '#0966E6', '#00C7AD', '#F5C51E'],
        tooltip: {
          // trigger: 'item',
          // formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        grid: {
          right: '5%',
          left: '5%',
          bottom: '5%',
          top: '5%',
          containLabel: true
        },
        legend: {
          // orient: 'vertical',
          left: 10,
          bottom: 20,
          // data: ['告警', '投诉', '自查', '其他'],
          textStyle: {
            color: '#97BCEC'
          },
          itemWidth: 8,
          itemHeight: 8
        },
        title: {
          text: '未处理',
          subtext: this.propData.total,
          textStyle: {
            fontSize: 16,
            color: '#5196FF',
            lineHeight: 20
          },
          subtextStyle: {
            fontSize: 28,
            color: '#A8BEE7'
          },
          textAlign: 'center',
          left: '48%',
          top: '28%'
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['35%', '50%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: true,
            label: {
              show: true,
              // position: 'center',
              color: '#A5B5D7'
            },
            // emphasis: {
            //     label: {
            //         show: true,
            //         fontSize: '30',
            //         fontWeight: 'bold'
            //     }
            // },

            // labelLine: {
            //     show: true
            // },
            data: this.propData.data
          }
        ]
      }
      this.chart.setOption(this.option)
      if (this.propData.data && this.propData.data.length > 0) {
        this.autoPlayTool(this.chart, this.propData.data, 0, 0, 5000)
      }
    }
  }
}
</script>
