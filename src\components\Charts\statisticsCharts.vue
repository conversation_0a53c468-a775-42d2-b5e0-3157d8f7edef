<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>

<script>
import echarts from 'echarts'
import resize from './mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
export default {
  name: 'StatisticsCharts',
  mixins: [resize, componentMixin],
  props: {
    id: {
      require: false,
      type: String,
      default: 'StatisticsCharts'
    },
    width: {
      require: false,
      type: String,
      default: '1rem'
    },
    height: {
      require: false,
      type: String,
      default: '1.25rem'
    },
    title: {
      require: false,
      type: String,
      default: ''
    },
    startColor: {
      require: false,
      type: String,
      default: ''
    },
    endColor: {
      require: false,
      type: String,
      default: ''
    },
    value: {
      require: false,
      type: Number,
      default: 0
    }
  },
  watch: {
    value: { // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(
        document.getElementById(this.id)
      )
      const currentIndex = 0
      this.option = {
        animationDuration: 3000,
        backgroundColor: 'transparent',
        title: {
          text: this.value + '%',
          x: 'center',
          y: 'center',
          textStyle: {
            color: this.endColor,
            fontSize: 30
          },
          subtext: this.title,
          subtextStyle: {
            color: '#fff',
            fontSize: 16,
            lineHeight: 200
          }
        },
        series: [{
          type: 'pie',
          startAngle: 220,
          center: ['50%', '55%'],
          radius: ['70%', '80%'],
          hoverAnimation: false,
          labelLine: {
            show: false
          },
          data: [{
            name: '',
            value: this.value,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                offset: 0,
                color: this.startColor

              }, {
                offset: 1,
                color: this.endColor

              }])
            }
          },
          { // 画中间的图标
            name: '',
            value: 0,
            label: {
              position: 'inside',
              backgroundColor: this.endColor,
              // width: 50,
              // height: 50,
              padding: 0,
              borderRadius: 50,
              shadowColor: '#fff',
              shadowBlur: 5
            }
          },
          { // 画剩余的刻度圆环
            name: '',
            value: 100 - this.value,
            itemStyle: {
              color: '#051C48'
            }

          }, { // 画剩余的刻度圆环
            name: '',
            value: 38,
            itemStyle: {
              color: 'transparent'
            }
          }
          ]
        }]
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
