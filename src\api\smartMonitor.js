import request from '@/utils/request'

// 项目点列表
export function centers(params) {
  return request('/api/v1/web/sl/data/globalOverview/canteenRiskList', {
    method: 'get',
    params
  })
}

// 根据项目点查询信息
// export function centerInfo(id) {
//   return request({
//     url: '/api/v1/web/sl/data/monitoring/centerInfo',
//     method: 'post',
//     data: {
//       id
//     }
//   })
// }

// 告警汇总
export function alarmStatistics(data) {
  return request({
    url: '/api/v1/web/sl/data/monitoring/alarmStatistics',
    method: 'post'
  })
}

// 设备工况
export function deviceStatus(data) {
  return request({
    url: '/api/v1/web/sl/data/monitoring/deviceStatus',
    method: 'post'
  })
}

// 设备告警
export function deviceAlarm(data) {
  return request({
    url: '/api/v1/web/sl/data/monitoring/deviceAlarm',
    method: 'post'
  })
}

// 系统告警
export function systemAlarm(data) {
  return request({
    url: '/api/v1/web/sl/data/monitoring/systemAlarm',
    method: 'post'
  })
}

// 人员工况
export function employeeStatus(data) {
  return request({
    url: '/api/v1/web/sl/data/monitoring/employeeStatus',
    method: 'post',
    params: data
  })
}

// 项目点设备列表
export function getDeviceList(data) {
  return request({
    url: '/api/v1/web/sl/data/monitoring/deviceInfo',
    method: 'post',
    data
  })
}

// 人员晨间情况
export function noMorningCheck(params) {
  return request({
    url: '/api/v1/web/sl/data/monitoring/noMorningCheck',
    method: 'get',
    params
  })
}
