import request from '@/utils/request'

export function fetchObtainStreet(code) {
  return request('/api/v1/web/sl/data/globalOverview/obtainStreetByCode?code=' + code, {
    method: 'get'
  })
}
export function fetchUserInfo() {
  return request('/api/v1/public/qryUserInfo', {
    method: 'get'
  })
}

export function fetchCkpi(canteenId, detailDate) {
  return request('/api/v1/web/fkpi/selectByCidInfo?cid=' + canteenId + '&date=' + detailDate, {
    method: 'get'
  })
}
