.main-left {
  position: absolute;
  top: 0.7rem;
  left: 0.175rem;
  z-index: 100;
  font-size: 0.1rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100vh - 0.75rem);

  // 陪餐次数
  .rankList{
    width: 2.02rem;
    background-size: 100% 100%;
    font-size: 0.1rem;
    font-family: PingFang SC;
    color: #ABC9FF;
    padding: 0.15rem;
    box-sizing: border-box;
    .calendar-top {
      width: 100%;
      height: calc(1rem - (calc(1080px - 100vh) / 3));
      overflow: hidden;
      .single {
        height: 1.1rem;
        width: 100%;
        margin-bottom: 0.1rem;
        .title {
          display: flex;
          height: 0.22rem;
          padding: 0 0.07rem;
          align-items: center;
          justify-content: space-between;
          // background: rgba(3, 169, 255, 0.1);
          background-color:#062261;
          box-shadow: 0px 1px 0px 0px rgba(20, 137, 94, 0.4);
          .left {
            font-size: 0.09rem;
            max-width: 1rem;
            overflow: hidden;
            text-overflow:ellipsis;
            white-space: nowrap;
            font-weight: bold;
            color: #FFFFFF;
          }
          .right {
            min-width: 0.6rem;
            color: #00BEBD;
            font-size: 0.07rem;
          }
        }
        .content {
          display: flex;
          align-items: center;
          padding: 0.1rem;
          height: auto !important;
          .left {
            width: 0.6rem;
            height: 0.6rem;
            margin-right: 0.13rem;
          }
          .right {
            display: flex;
            height: 0.6rem;
            flex-direction: column;
            justify-content: space-around;
            text-align: left;
            span {
              font-size: 0.09rem;
              color: #CDDFFF;
              overflow: hidden;
              text-overflow:ellipsis;
              white-space: nowrap;
              font-weight: bold;
            }
          }
        }
      }
      .item {
        display: flex;
        align-items: center;
        .number {
          font-size: 0.08rem;
          margin-right:0.1rem ;
        }
        .right-flex {
          flex: 1;
          display: flex;
          align-items: center;
          border-bottom: 1px dotted #0090FF;
          .center-main {
            flex: 1;
            padding: 0.03rem 0;
            .center-name {
              font-size: 0.06rem;
            }
            .center-box {
              display: flex;
              align-items: center;
              .progress {
                height: 0.05rem;
                background: #1242A5;
                box-shadow: inset 0 0 5px #1F88F1;
                margin-right: 0.05rem;
              }
              .count {
                color: #fff;
                font-size: 0.06rem;
              }
            }
          }
          .center-count {
            font-size: 0.07rem;
            color: #00FCF9;
            margin-right: 0.03rem;
          }
        }

      }
      // .calendar_con {
      //   position: relative;
      //   text-align: center;
      //   height: 0.31rem;
      //   width: 0.215rem;
      //   line-height: 0.31rem;
      //   font-size: 0.2rem;
      //   color: #76ADFF;
      //   font-family: 300-CAI978;
      //   font-weight: 400;
      //   background-image: url('../../assets/img/multipleGovernance/sz.png');
      // }
    }
  }


.left-two{
  width: 2.02rem;
  height: 1.1rem;
  padding: 0.1rem;
  box-sizing: border-box;
}

  .words{
    width: 2.02rem;
    height: calc(1.3rem - (calc(1080px - 100vh) / 3));
  }

  .calendar-content {
    width: 2.02rem;
    // height: 2rem;
    height: calc(2.2rem - (calc(1080px - 100vh) / 4));
    background-image: url('../../assets/img/multipleGovernance/dk.png');
    background-size: 100% 100%;

    table {
      color: #ABC9FF;
      font-size: 0.08rem;
      width: 100%;
      box-sizing: border-box;
      padding: 0.175rem 0.12rem;

      tbody {
        margin-top: 0.05rem;
      }
    }
  }

  .calendar-bottom {
    width: 2.02rem;
    // height: 1.25rem;
    height: calc(1.3rem - (calc(1080px - 100vh) / 4));
    box-sizing: border-box;
    padding: 0.1rem;
    background-image: url('../../assets/img/multipleGovernance/dk.png');
    background-size: 100% 100%;
  }
}

.main-right {
  position: absolute;
  top: 0.7rem;
  right: 0.205rem;
  width: 2.25rem;
  z-index: 100;
  font-size: 0.1rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100vh - 0.75rem);
  .dietaryCountriesStructure {
    padding-right: 0.01rem;
    display: flex;
    font-size: 0.1rem;
    font-family: PingFang SC;
    font-weight: bold;
    color: #CCDAF0;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
    }
    .right {
      width: 0.855rem;
      height: 0.155rem;
      background: rgba(14, 139, 255, 0.32);
      //border: 1px solid #00EAFF;
      //opacity: 0.5;
      background: url("../../assets/img/shaixuanc.png") no-repeat;
      background-size: 100% 100%;
    }
    .el-input .el-input__inner {
      color: #3ADBFF!important;
    }
  }
  .data-right-top {
    height: calc(1.4rem - (calc(1080px - 100vh) / 4));
    padding: 0.1rem;
    background-image: url('../../assets/img/multipleGovernance/dk.png');
    background-size: 100% 100%;
    display: flex;
    align-items: center;
  }

  .data-right-Nutritional{
    width: 2.05rem;
    // height: 0.98rem;
    height: calc(0.98rem - (calc(1080px - 100vh) / 4));
    padding: 0.1rem;
    background-image: url('../../assets/img/multipleGovernance/dk.png');
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    .nutr-top{
      height: 0.3rem;
      width: 100%;
      margin-top: 0.07rem;
      .top-title{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title-left{
          margin-left: 0.1rem;
          font-size: 0.09rem;
          color: #CDDFFF;
        }
        .title-right{
          margin-left: 0.1rem;
          color: #00E7FF;
          font-size: 0.08rem;
          padding: 0 0.3rem 0 0.08rem;
          background: linear-gradient(40deg, #0C408B 0%, #04113E 100%);
        }
      }
      .top-branch{
        display: flex;
        margin-top: 0.05rem;
        .branch-name{
          font-size: 0.09rem;
          color: #CDDFFF;
          margin-left: 0.1rem;
        }
        .branch-num{
          margin-left: 0.07rem;
          overflow: hidden;
          .startImg{
            margin-right: 0.04rem;
          }
        }

      }
    }
    .nutr-foot{
      width: 90%;
      height: 0.3rem;
      padding: 0.05rem;
      margin-top: 0.1rem;
      background-color: #041948;
      color: #CDDFFF;
      font-size: 0.08rem;
    }
  }

  .data-right-content {
    width: 2.05rem;
    // height: 0.98rem;
    height: calc(0.98rem - (calc(1080px - 100vh) / 4));
    padding: 0.1rem;
    background-image: url('../../assets/img/multipleGovernance/dk.png');
    background-size: 100% 100%;
    display: flex;
    align-items: center;
  }

  .data-right-bottom {
    position: relative;
    width: 2.05rem;
    height: calc(0.98rem - (calc(1080px - 100vh) / 4));
    padding: 0.1rem;
    background-image: url('../../assets/img/multipleGovernance/dk.png');
    background-size: 100% 100%;
    display: flex;
    align-items: center;
  }
}

.calendar_td {
  width: 0.25rem;
  height: 0.29rem;
  padding-top: 0.02rem;
  padding-left: 0.04rem;
  background-image: url('../../assets//img/multipleGovernance/rq.png');
  background-size: 100% 100%;
}

.calendar_tds {
  width: 0.2rem;
  height: 0.24rem;
  padding-top: 0.02rem;
  padding-left: 0.04rem;
  background-image: url('../../assets//img/multipleGovernance/rq.png');
  background-size: 100% 100%;
}

.calendar_event {
  font-size: 0.115rem;
  color: #F5CA35;
  font-family: PingFang SC;
  font-weight: bold;
  text-align: center;
}

.calendar_nbsp {
  width: 0.25rem;
  height: 0.29rem;
  padding-top: 0.02rem;
  padding-left: 0.04rem;
  background-image: url('../../assets//img/multipleGovernance/rq.png');
  background-size: 100% 100%;
}

.main-left-top {
  display: flex;
  font-size: 0.1rem;
  font-family: PingFang SC;
  font-weight: bold;
  color: #CCDAF0;
}

.totalnum {
  position: absolute;
  //width: 1.04rem;
  height: 0.291rem;
  background-image: url('../../assets/img/multipleGovernance/tsk.png');
  background-size: 100% 100%;
  padding: 0.09rem;
  // top:0.125rem;
  right: 0.12rem;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  font-size: 0.07rem;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ABC9FF;

  .complaint_div {
    width: 0.52rem;
    margin-right: 0.05rem;
  }
}

// 弹窗
.popup {
  width: 2.0rem;
  // height: 1.89rem;
  box-sizing: border-box;
  padding: 0.04rem 0.04rem 0.12rem 0.04rem;
  z-index: 10000000;
  position: relative;
  background-image: url('../../assets/img/multipleGovernance/tc.png');
  background-size: 100% 100%;

  .popup_title {
    position: absolute;
    top: -0.0098rem;
    left: 0rem;
    right: 0rem;
    margin: 0 auto;
    width: 1.2rem;
    height: 0.2rem;
    text-align: center;
    line-height: 0.18rem;
    // z-index: 10000000000000;
    background-size: 100% 100%;
    background-image: url('../../assets/img/multipleGovernance/tcbt.png');
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    >div {
      width: 0.65rem;
      height: 0.2rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  #accompany {
    margin-top: 0.2rem;
    padding: 0 0.06rem;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      span {
        &:first-child {
          width: 0.32rem;
          font-size: 0.08rem;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
        }
        &:last-child {
          font-size: 0.06rem;
          font-family: PingFang SC;
          font-weight: 500;
          color: #A9C2E2;
        }
      }
    }
    .structure {
      width: 100%;
      height: 0.3rem;
      padding: 0.05rem 0.05rem;
      .list {
        display: inline-flex;
        float: left;
        width: 33%;
        align-items: center;
        height: 0.1rem;
        &:nth-child(1) {
          span {
            &:first-child {
              background-color: #408BE8;
            }
          }
        }
        &:nth-child(2) {
          span {
            &:first-child {
              background-color: #8492FF;
            }
          }
        }
        &:nth-child(3) {
          span {
            &:first-child {
              background-color: #FFD400;
            }
          }
        }
        &:nth-child(4) {
          span {
            &:first-child {
              background-color: #B8E9F2;
            }
          }
        }
        &:nth-child(5) {
          span {
            &:first-child {
              background-color: #2459E0;
            }
          }
        }
        &:nth-child(6) {
          span {
            &:first-child {
              background-color: #4DF1F0;
            }
          }
        }
        span {
          &:first-child {
            display: inline-block;
            width: 0.05rem;
            height: 0.05rem;
            background: #408BE8;
            border-radius: 50%;
            margin-right: 0.02rem;
          }
          &:last-child {
            width: 0.45rem;
            font-size: 0.07rem;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #A8BEE7;
          }
        }
        display: flex;
        align-items: center;
      }
    }
    .score {
      .score-title {
        display: inline-flex;
        align-items: center;
        font-size: 0.08rem;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        span {
          margin-right: 0.12rem;
        }
        img {
          width: 0.09rem;
          height: 0.08rem;
          margin-right: 0.03rem;
          &:last-child {
            margin-right: 0;
          }
        }
      }
      .score-content {
        padding: 0.02rem 0.01rem;
        color: #A8BEE7;
        font-size: 0.08rem;
        display: -webkit-box !important;
        overflow: hidden;
        text-overflow: ellipsis;
        // work-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
    }
  }
  .popup_conter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.06rem 0.03rem;
    //margin-top: 0.4rem;
    .popup_conter_time {
      font-family: PingFang SC;
      font-weight: 500;
      color: #A9C2E2;
      font-size: 0.08rem;
    }
  }

  .popup_back {
    width: 100%;
    height: 0.48rem;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    padding: 0rem 0.045rem;
    background-image: url('../../assets/img/multipleGovernance/rld.png');

    .popup_week {
      display: flex;
      justify-content: space-around;
      text-align: center;
      color: #7A819F;
      font-size: 0.057rem;
    }

    .popup_ones {
      color: #FFFFFF;
      font-size: 0.064rem;
    }

    .popup_dirleft {
      background: url(~@/assets/img/multipleGovernance/zuo.png) center / 100% 100% no-repeat;
      width: 0.135rem;
      height: 0.135rem;
      border-radius: 50%;
      margin-top: 0.15rem;
      // margin-right: 0.05rem;
      cursor: pointer;
    }

    .popup_dirright {
      background: url('../../assets/img/multipleGovernance/you.png') center / 100% 100% no-repeat;
      width: 0.135rem;
      height: 0.135rem;
      border-radius: 50%;
      margin-top: 0.15rem;
      // margin-right: 0.05rem;
      cursor: pointer;
    }
  }

  .popup_bottom {
    width: 1.8rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 0.01rem 0.115rem;
    justify-content: space-between;

    .popup_bottom_div {
      // width: 50%;
      font-size: 0.08rem;

      font-family: Source Han Sans CN;
      display: flex;
      text-align: center;
      // justify-content: space-between;
      // justify-content: space-between;
      // align-items: center;
    }
  }

  .week_image {
    border-radius: 50%;
    width: 0.1675rem;
    height: 0.1675rem;
    line-height: 0.1675rem;
    // margin-top: 0.1rem;
    cursor: pointer;
  }
}
