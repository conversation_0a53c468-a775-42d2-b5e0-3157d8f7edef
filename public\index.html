<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta
    http-equiv="X-UA-Compatible"
    content="IE=edge"
  >
  <meta
    name="viewport"
    content="width=device-width,initial-scale=1.0"
  >
  <title>山东能源食安智慧监管平台</title>
  <script src="js/liveplayer-lib.min.js"></script>
  <style>
    .BMap_cpyCtrl {
      display: none;
    }

    .anchorBL {
      display: none;
    }

    /* elementUI颜色修改 */
    .el-tabs__item {
      color: #f9f9f9 !important;
      font-size: 18px !important;
    }

    .el-tabs__item.is-active {
      color: #409EFF !important;
    }

    .el-tabs__header {
      background: rgba(0, 162, 255, 0.2) !important;
      padding-bottom: 5px !important;
    }

    .el-tabs__nav-scroll {
      padding-left: 20px !important
    }

    .el-collapse-item__header {
      padding-left: 30px;
      box-sizing: border-box;
      border-color: #012C69 !important;
      background: #011247 !important;
      color: #ABC9FF !important;
    }

    .el-collapse {
      border-color: #31406d !important;
    }

    .el-collapse-item__content {
      /* color: #f6f6f6 !important; */
      background: #011247 !important;
    }

    .el-collapse-item__wrap {
      border-color: #31406d !important;
    }

    .el-table__fixed-right-patch {
      background: #011247 !important;
    }

    .el-tabs__nav-wrap::after {
      background: none !important
    }

    .el-table td {
      color: #f9f9f9;
      background: #193A91 !important;
      background-size: 100% 100% !important;
    }

    .el-table tr:nth-child(odd) td {
      background: #142D6F !important;
    }

    .el-table th {
      color: #fff !important;
      background: #052780 !important
    }

    .el-table--border,
    .el-table--group {
      color: #00A2FF !important
    }

    .el-table td,
    .el-table th.is-leaf {
      border-color: #00A2FF !important
    }

    .el-table {
      border-color: #00A2FF !important
    }

    .el-table__empty-block {
      background: #052780 !important
    }

    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background: none !important;
    }

    .image-slot {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #011247;
      color: #909399;
    }

    .el-icon-circle-close {
      color: #D8D8D8
    }

    /* .el-image-viewer__wrapper {
      z-index: 66666666666 !important;
    }

    .el-image-viewer__wrapper {
      margin-top: 85px !important;
      height: calc(100vh - 85px) !important;
    } */

    a.amap-logo {
      display: none !important;
    }
  </style>
</head>

<body>
  <div id="app"></div>
  <!-- built files will be auto injected -->
  <script
    src="https://webapi.amap.com/maps?v=1.4.15&key=9b28d203fb638acf11ff8911a2b3b84e&plugin=AMap.DistrictSearch&plugin=AMap.Heatmap&AMap.ControlBar&plugin=AMap.Object3DLayer&plugin=Map3D&plugin=AMap.Geocoder"
  >
  </script>
  <script src="https://webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>
  <script src="jessibuca/jessibuca.js"></script>
  <script>
    (function () {
      change();

      function debounce(func, wait, immediate) {
        let timeout, args, context, timestamp, result
        const later = function () {
          // 据上一次触发时间间隔
          const last = +new Date() - timestamp
          // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
          if (last < wait && last > 0) {
            timeout = setTimeout(later, wait - last)
          } else {
            timeout = null
            // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
            if (!immediate) {
              result = func.apply(context, args)
              if (!timeout) context = args = null
            }
          }
        }
        return function (...args) {
          context = this
          timestamp = +new Date()
          const callNow = immediate && !timeout
          // 如果延时不存在，重新设定延时
          if (!timeout) timeout = setTimeout(later, wait)
          if (callNow) {
            result = func.apply(context, args)
            context = args = null
          }
          return result
        }
      }

      function change() {
        document.documentElement.style.fontSize = document.documentElement.clientWidth * 100 /
          960 + 'px';
      }
      /* 监听窗口大小发生改变时 */
      window.addEventListener('DOMContentLoaded', change, false);
      window.addEventListener('orientationchange', change, false);
      window.addEventListener('resize', debounce(change, 300, true), false);
    })()
  </script>
</body>

</html>
