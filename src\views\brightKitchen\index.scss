.main-content {
  .special_focus_toolTip {
    padding: 0.15rem;
    z-index: 7000000000;
    position: absolute;
    display: none;
    width: 1.5rem;
    height: 0.45rem;
    background: url(../../assets/img/<EMAIL>) no-repeat;
    background-size: 100% 100%;
    background-position: 100% 100%;
  }

  color: #b9cdea;

  .list-window {
    padding: 0.1rem;
    width: 3.7rem;
    height: 3rem;
    // border: 1px solid red;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -30%);
    z-index: 33;
    background: url(../../assets/img/<EMAIL>) no-repeat;
    background-position: 100% 100%;
    background-size: 100% 100%;

    .window-quxiao {
      position: absolute;
      right: 0.1rem;
      top: 0.1rem;
      width: 0.35rem;
      height: 0.35rem;
      background: url(../../assets/img/<EMAIL>) no-repeat;
      background-position: 100% 100%;
      background-size: 100% 100%;
    }

    .window-title {
      margin-top: 0.1rem;
      text-align: center;
      font-size: 0.16rem;
      font-family: PingFang SC;
      font-weight: bold;
      color: #f66060;
      line-height: 0.17rem;
    }

    .window-week {
      color: #abc9ff;
      margin-top: 0.1rem;
      display: flex;
      padding: 0 0.3rem;

      .leftimg {
        border: 1px solid #24a4ff;
        width: 0.87rem;
        height: 0.57rem;
        margin-right: 0.1rem;
      }

      .infotop {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.1rem;

        .infoname {
          margin-top: 0.05rem;
        }

        .infotime {
          // margin-left: 0.2rem;
          margin-top: 0.05rem;
        }
      }
    }

    .window-video {
      background: url("../../assets/img/<EMAIL>") no-repeat;
      background-size: 100% 100%;
      height: 1.9rem;
      width: 3.2rem;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -25%);
      display: flex;
      justify-content: center;
      align-items: center;
      .player {
        width: 95%;
      }
      .player-img {
        width: 95%;
        height: 90%;
      }
    }
  }

  .box-title {
    font-size: 0.1rem;
    margin-bottom: 0.05rem;
    display: flex;
    align-items: center;
    color: #cbd9ef;

    > div {
      margin-left: 0.045rem;
    }
  }

  .left-main {
    position: absolute;
    left: 0.2rem;
    top: 0.6rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: calc(100vh - 0.75rem);

    .left-select {
      margin-bottom: 0.1rem;
      // .select-main{
      //   width: 2.25rem;
      //   // height: 0.46rem;
      //   height: calc(0.8rem - (calc(1080px - 100vh) / 4));
      //   display: flex;
      //   align-items: center;
      //   justify-content: space-between;
      //   box-sizing: border-box;
      //   padding: 0.05rem 0.2rem;
      //   background: rgba(0, 0, 0, 0.2);
      //   box-shadow: inset 0 0 40px #0936b0;
      //   text-align: center;
      //   display: flex;
      //   .left-video{
      //     height: 100%;
      //     width:50%;
      //     border: 1px solid;
      //   }
      //   .right-video{
      //     height: 100%;
      //     width: 50%;
      //     border: 1px solid;
      //   }
      // }
    }

    .left-top {
      margin-bottom: 0.1rem;

      .camera-main {
        width: 2.25rem;
        // height: 0.46rem;
        height: calc(0.6rem - (calc(1080px - 100vh) / 4));
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 0.1rem 0.2rem;
        background: rgba(0, 0, 0, 0.2);
        box-shadow: inset 0 0 40px #0936b0;
        text-align: center;

        > div {
          > div:first-child {
            font-size: 0.16rem;
          }
          > div:last-child {
            font-size: 0.09rem;
          }
        }
        .num {
          font-size: 0.16rem;
        }

        .title {
          font-size: 0.09rem;
        }
      }
    }

    .left-center {
      margin-bottom: 0.1rem;

      .warning-chart {
        width: 2.25rem;
        // height: 1.3rem;
        height: calc(0.8rem - (calc(1080px - 100vh) / 4));
        box-sizing: border-box;
        padding: 0.05rem 0.05rem;
        background: rgba(0, 0, 0, 0.2);
        box-shadow: inset 0 0 40px #0936b0;
      }
    }

    .left-bottom {
      position: relative;

      .dangetab {
        display: flex;
        position: absolute;
        bottom: 1.35rem;
        left: 0.95rem;
        width: 1.3rem;
        color: #2c6fb9;
        text-align: center;

        .buttom {
          cursor: pointer;
          border: 1px solid;
          width: 0.38rem;
          line-height: 0.13rem;
          height: 0.13rem;
          margin: 0.02rem auto;
          border: 1px solid #0886ed;
          border-radius: 0.02rem;
        }

        .active {
          color: #14ecff;
          width: 0.43rem;
          background: url(../../assets/img/<EMAIL>) no-repeat;
          background-size: 100% 100%;
          position: relative;
          top: -0.03rem;
          line-height: 0.19rem;
          border: none;
          height: 0.21rem;
        }
      }

      .warning-list {
        width: 2.25rem;
        height: 1.4rem;
        box-sizing: border-box;
        padding: 0.05rem 0.05rem;
        background: rgba(0, 0, 0, 0.2);
        box-shadow: inset 0 0 40px #0936b0;

        .warning-swiper {
          height: 1.4rem;
          cursor: pointer;

          .alarm-item:nth-child(odd) {
            background: rgba(14, 33, 85, 0.3);
          }

          .alarm-item {
            padding: 0.05rem 0.1rem;
            box-sizing: border-box;
            height: 2rem;

            .item-header {
              display: flex;
              align-items: center;
              margin-bottom: 4.5px;

              img {
                width: 0.04rem;
                height: 0.08rem;
                margin-right: 5px;
              }

              .title {
                font-size: 0.09rem;
                font-family: PingFang SC;
                font-weight: bold;
                color: #d6e4f5;
                flex: 1;
              }

              .header-time {
                font-size: 0.07rem;
                font-family: PingFang SC;
                font-weight: 500;
                color: #7f97b9;
              }
            }

            .item-main {
              display: flex;

              > img {
                width: 0.5rem;
                height: 0.38rem;
                margin-right: 6.5px;
              }

              > .item-right-main {
                font-size: 0.08rem;

                > div:last-child {
                  margin-top: 6px;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                }
              }
            }
          }
        }
      }
    }
  }

  .right-main {
    position: absolute;
    right: 0.2rem;
    top: 0.6rem;

    .right-container {
      width: 2.29rem;
      // height: 3.95rem;
      height: calc(100vh - 0.9rem);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0.15rem;
      background: rgba(0, 0, 0, 0.2);
      box-shadow: inset 0 0 40px #0936b0;
      position: relative;

      .center-bg {
        width: 1.77rem;
        height: 0.22rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.05rem;
        background: url("../../assets//img/school_bg.png") no-repeat;
        background-size: 100% 100%;

        > img {
          width: 0.1rem;
          height: 0.1rem;
          margin-right: 10px;
        }
      }

      .player-box {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        // height: 3.05rem;
        // height: calc(3.05rem - (calc(1080px - 100vh) / 4));

        .player-item {
          width: 0.95rem;
          margin-top: calc(0.15rem - (calc(1080px - 100vh) / 4));
          border: 1px solid #177bb6;
          position: relative;

          // margin-top: 0.08rem;
          > .monitor {
            // position: relative;

            .isHoliday {
              padding: 0.01rem 0.03rem;
              background: rgba(0, 0, 0, 0.5);
              border-radius: 0.01rem;
              position: absolute;
              top: 0.02rem;
              right: 0.02rem;
              display: flex;
              align-items: center;
              .icon {
                width: 0.03rem;
                height: 0.03rem;
                background: #f66060;
                border-radius: 50%;
                margin-right: 0.02rem;
              }
            }

            .left-top-img {
              position: absolute;
              width: 0.125rem;
              height: 0.075rem;
              z-index: 9999;
              top: 0.04rem;
              left: 0.05rem;
            }

            .poster {
              width: 100%;
              width: 190px;
              height: 107px;
            }

            .noVideoPoster {
              width: 190px;
              height: 107px;
              position: relative;
              left: 0;
              right: 0;
              bottom: 0;
              top: 0;
            }
          }

          .video-play {
            width: 0.25rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-50%);
            cursor: pointer;
          }

          > .monitor-name {
            text-align: center;
            height: 0.14rem;
            line-height: 0.14rem;
            box-shadow: inset 0 0 10px #153f81;
            font-size: 0.06rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .random-img {
        width: 1.49rem;
        height: 0.31rem;
        // position: absolute;
        // bottom: 0rem;
        // left: 0.4rem;
        margin: 0 auto;
        cursor: pointer;
      }
    }
  }

  .video-main {
    position: absolute;
    top: 24vh;
    left: 50%;
    transform: translate(-50%);
    width: 3.5rem;
    height: 1.97rem;
    border: 1px solid blue;
  }

  .warning-main {
    border: 1px solid;
    z-index: 1000000;
    width: 1.83rem;
    height: 1.07rem;
    background: url("../../assets/img/<EMAIL>") no-repeat;
    background-size: 100% 100%;
    position: fixed;
    top: calc(1rem - calc(1080px - 100vh) / 2);
    left: 4rem;
    box-sizing: border-box;
    padding: 0.04rem;

    .close {
      width: 0.15rem;
      height: 0.15rem;
      position: absolute;
      top: 0.05rem;
      right: 0.05rem;
      cursor: pointer;
    }

    .warning-header {
      color: #ffffff;
      font-size: 0.07rem;
      position: absolute;
      left: 0.5rem;
      top: 0.07rem;
      z-index: 10000;
    }

    .warning-footer {
      width: 96%;
      height: 0.17rem;
      background: rgba(8, 32, 79, 0.7);
      position: absolute;
      left: 2%;
      bottom: 18%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 0.1rem;

      .video-name {
        font-size: 0.08rem;
        color: #fff;
      }

      .playback {
        font-size: 0.07rem;
        color: #fff;
        padding: 0.02rem 0.09rem;
        background: url("../../assets/img/<EMAIL>") no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
      }
    }
  }
}
