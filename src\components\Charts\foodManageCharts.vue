<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  name: 'FoodManageCharts',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    propData: {
      require: false,
      type: Object,
      default: () => { }
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      if (this.propData.bottomList.length === 0) {
        this.hideLoading(this.chart)
        return
      } else {
        this.cancelLoading(this.chart)
      }
      this.option = {
        animationDuration: 3000,
        color: ['rgba(0, 191, 243, 1)', 'rgba(220, 209, 8, 1)'],
        xAxis: {
          type: 'category',
          data: this.propData.bottomList,
          nameTextStyle: {
            color: '#1FC8FF'
          },
          boundaryGap: false,
          axisLabel: {
            color: '#1FC8FF',
            interval: 0,
            textStyle: {
              fontSize: 14
            }
          },
          axisLine: {
            lineStyle: {
              color: '#8EE3FF'
            }
          },
          axisTick: {
            show: false
          }
        },
        grid: {
          right: 15,
          top: 30,
          left: 'left',
          bottom: 30,
          containLabel: true
        },
        legend: {
          data: ['入库', '丢弃'],
          right: 20,
          textStyle: {
            color: '#97BCEC'
          },
          icon: 'rect',
          itemWidth: 15,
          itemHeight: 8
          //   'circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow', 'none'
        },
        tooltip: {
          // show:true
          trigger: 'axis'
        },
        yAxis: {
          name: '（单位：kg）',
          axisLine: {
            show: false
          },
          type: 'value',
          nameTextStyle: {
            color: '#1FC8FF'
          },
          axisLabel: {
            color: '#1FC8FF',
            textStyle: {
              fontSize: 14
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(142, 227, 255, 0.2)'
            }
          }
        },
        series: [{
          data: this.propData.ins,
          name: '入库',
          type: 'line',
          symbol: 'none',
          smooth: true,
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: 'rgba(0, 191, 243, 0.8)'
                  },
                  {
                    offset: 0.8,
                    color: 'rgba(0, 191, 243, 0.1)'
                  }
                ],
                false
              ),
              shadowColor: 'rgba(0, 0, 0, 0.1)',
              shadowBlur: 10
            }
          }
        }, {
          data: this.propData.lose,
          name: '丢弃',
          type: 'line',
          symbol: 'none',
          smooth: true,
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: 'rgba(220, 209, 8, 0.8)'
                  },
                  {
                    offset: 0.8,
                    color: 'rgba(220, 209, 8, 0.1)'
                  }
                ],
                false
              ),
              shadowColor: 'rgba(0, 0, 0, 0.1)',
              shadowBlur: 10
            }
          }
        }]
      }
      this.chart.setOption(this.option)
      this.autoPlayTool(this.chart, this.propData.lose, 0)
    }
  }
}
</script>
