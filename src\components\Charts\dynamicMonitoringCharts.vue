<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    propData: {
      require: false,
      type: Array,
      default: () => { return [] }
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      const myColor = ['#60E589', '#FE9F1A', '#FD4D61']
      this.option = {
        animationDuration: 3000,
        grid: {
          left: 0,
          top: '12%',
          right: '30%',
          bottom: '-12%',
          containLabel: true
        },
        tooltip: {
          show: true,
          position: 'inside'
        },
        yAxis: [{
          axisTick: 'none',
          axisLine: 'none',
          axisLabel: {
            textStyle: {
              color: '#ABC9FF',
              fontSize: '16'
            }
          },
          data: ['低风险', '中风险', '高风险']
        }, {
          axisTick: 'none',
          axisLine: 'none',
          axisLabel: {
            textStyle: {
              color: '#ABC9FF',
              fontSize: '16'
            }
          }
        }, {
          axisLine: {
            lineStyle: {
              color: 'rgba(0,0,0,0)'
            }
          }
        }],
        xAxis: [{
          show: false
        }],
        series: {
          animationEasing: 'elasticOut',
          data: this.propData,
          type: 'pictorialBar',
          symbol: 'rect',
          symbolRepeat: 'fixed',
          symbolMargin: '36%',
          symbolClip: true,
          symbolSize: [4, 12],
          barCategoryGap: '80%',
          label: {
            normal: {
              show: true,
              position: 'right',
              formatter: function(param) {
                // console.log(param)
                return param.value + '家' + '{words| ' + (param.name === '低风险' ? '表现良好' : param.name === '中风险' ? '需要改进' : '重点关注') + '}'
              },
              textStyle: {
                normal: {
                  color: function(params) {
                    return myColor[params.dataIndex]
                  }
                },
                rich: {
                  words: {
                    color: '#ABC9FF',
                    fontSize: 16
                  }
                },
                fontSize: '16'
              }
            }
          },
          itemStyle: {
            normal: {
              color: function(params) {
                var num = myColor.length
                return myColor[params.dataIndex % num]
              }
            }
          }
        }
      }
      this.chart.setOption(this.option)
      // this.autoPlayTool(this.chart, this.propData, 0)
    }
  }
}
</script>
