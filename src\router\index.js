import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/login',
    meta: {
      title: '登录',
      hasHeader: false,
      hasButtons: false
    }
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录',
      hasHeader: false,
      hasButtons: false
    },
    component: () => import('../views/login.vue')
  },
  {
    path: '/globalOverview',
    name: 'globalOverview',
    meta: {
      title: '全域总览',
      hasButtons: true,
      hasHeader: true
    },
    component: () => import('../views/globalOverview/index.vue')
  },
  {
    path: '/smartMonitor',
    name: 'smartMonitor',
    meta: {
      title: '智慧监测',
      hasButtons: true,
      hasHeader: true
    },
    component: () => import('../views/smartMonitor/index.vue')
  },

  {
    path: '/brightKitchen',
    name: 'brightKitchen',
    meta: {
      title: '明厨亮灶',
      hasButtons: true,
      hasHeader: true
    },
    component: () => import('../views/brightKitchen/index.vue')
  },
  {
    path: '/subjectSupervision',
    name: 'subjectSupervision',
    meta: {
      title: '主体监管',
      hasButtons: true,
      hasHeader: true
    },
    component: () => import('../views/subjectSupervision/index.vue')
  },
  {
    path: '/multipleGovernance',
    name: 'multipleGovernance',
    meta: {
      title: '多元共治',
      hasButtons: true,
      hasHeader: true
    },
    component: () => import('../views/multipleGovernance/index.vue')
  },
  {
    path: '/wisdomFoodSafety',
    name: 'wisdomFoodSafety',
    meta: {
      title: '数智食安',
      hasHeader: true,
      hasButtons: false
    },
    component: () => import('../views/wisdomFoodSafety/index.vue')
  },
  {
    path: '/commandSandTable',
    name: 'commandSandTable',
    meta: {
      title: '指挥沙盘',
      hasHeader: false,
      hasButtons: false
    },
    component: () => import('../views/commandSandTable/index.vue')
  }
]

const router = new VueRouter({
  base: process.env.BASE_URL,
  routes
})

export default router
