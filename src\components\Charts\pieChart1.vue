<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  name: 'PieChart1',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    color: {
      require: false,
      type: String,
      default: '#2373EF'
    },
    propData: {
      require: false,
      type: Object,
      default: () => {}
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      const _this = this
      this.chart = echarts.init(document.getElementById(this.id))
      this.option = {
        animationDuration: 3000,
        // tooltip: {
        //   trigger: 'item',
        //   formatter: function(params, ticket, callback) {
        //     var res = params.seriesName
        //     res += '<br/>' + params.name + ' : ' + params.percent + '%'
        //     return res
        //   }
        // },
        grid: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0
        },
        yAxis: [
          {
            show: false
          }
        ],
        series: {
          hoverAnimation: false,
          center: ['50%', '50%'],
          radius: ['80%', '100%'],
          type: 'pie',
          labelLine: {
            normal: {
              show: false
            }
          },
          color: this.color,
          data: [
            {
              value: this.propData.online,
              name: '在线数',
              label: {
                normal: {
                  rich: {
                    a: {
                      color: '#A7BDE6',
                      align: 'center',
                      fontSize: 12,
                      fontWeight: 'bold',
                      lineHeight: 20
                    },
                    b: {
                      color: '#fff',
                      align: 'center',
                      fontSize: 15,
                      fontWeight: 'bold'
                    }
                  },
                  formatter: function(params) {
                    return '{a|' + '在线数' + '}\n' + '{b|' + _this.propData.online + '/' + _this.propData.total + '}'
                  },
                  position: 'center',
                  show: true
                }
              }
            },
            {
              value: this.propData.total - this.propData.online,
              name: '',
              tooltip: {
                show: false
              },
              itemStyle: {
                normal: {
                  color: '#384469'
                },
                emphasis: {
                  color: '#aaa'
                }
              },
              hoverAnimation: false
            }
          ]
        }
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
