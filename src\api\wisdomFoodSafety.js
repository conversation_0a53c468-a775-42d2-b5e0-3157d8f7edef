import request from '@/utils/request'

/*
  * 一企一档
*/
// 供应商列表
export function supplierList(data) {
  return request({
    url: '/api/v1/web/sl/data/oneBusiness/supplierList',
    method: 'post',
    data
  })
}

// 采购记录以及供应商详情
// export function purchaseList(data) {
//   return request({
//     url: '/api/v1/web/sl/data/oneBusiness/purchaseList',
//     method: 'post',
//     data
//   })
// }

// 供应商为主体的圆圈
export function supplierCircle(data) {
  // return request({
  //   // url: '/api/v1/web/sl/data/oneBusiness/supplierCircle',
  //   url: '/api/v1/web/sl/data/oneBusiness/providerCircle',
  //   method: 'post',
  //   data
  // })
  // return request({
  //   url: '/api/v1/web/sl/data/oneBusiness/providerCircle3',
  //   method: 'post',
  //   data
  // })
  return request({
    url: '/api/v1/web/sl/data/oneBusiness/providerCircle5',
    method: 'post',
    data
  })
}

// 区为主体的圆圈
export function areaCircle(data) {
  return request({
    url: '/api/v1/web/sl/data/oneBusiness/areaCircle',
    method: 'post',
    data
  })
}
// 树图菜品溯源
export function selectTraceabilityV2(data) {
  return request({
    url: '/api/v1/web/sl/data/oneBusiness/selectTraceabilityV2',
    method: 'post',
    data
  })
}
// 树图供应商详情
export function getSupplierDetails(data) {
  return request({
    url: '/api/v1/web/sl/data/oneBusiness/supplierDetails',
    method: 'post',
    data
  })
}

/*
 * 一人一档
 */
// 食堂员工列表
export function canteenStaff(data) {
  return request({
    url: '/api/v1/web/sl/data/onePersonOneFile/canteenStaff',
    method: 'post',
    data
  })
}

// 员工详情
export function staffDetails(data) {
  return request({
    url: '/api/v1/web/sl/data/onePersonOneFile/staffDetails',
    method: 'post',
    data
  })
}

/*
 * 一校一档
 */
// 食材管理
export function foodManagement(data) {
  return request({
    url: '/api/v1/web/sl/data/oneCenter/foodManagement',
    method: 'post',
    data
  })
}

// 实时预警
export function dealWarning(data) {
  return request({
    url: '/api/v1/web/sl/data/oneCenter/dealWarning',
    method: 'post',
    data
  })
}

// 所有项目点的服务商
export function centerList(data) {
  return request({
    url: '/api/v1/web/sl/data/oneCenter/centerList',
    method: 'post',
    data
  })
}

// 动态监测
export function dynamicMonitoring(data) {
  return request({
    url: '/api/v1/web/sl/data/oneCenter/dynamicMonitoring',
    method: 'post',
    data
  })
}

// 自查记录
// export function self(data) {
//   return request({
//     url: '/api/v1/web/sl/data/oneCenter/self',
//     method: 'post',
//     data
//   })
// }

// 从业人员
export function employees(data) {
  return request({
    url: '/api/v1/web/sl/data/oneCenter/employees',
    method: 'post',
    data
  })
}

// 待处理事件
export function handlingEvents(data) {
  return request({
    url: '/api/v1/web/sl/data/oneCenter/handlingEvents',
    method: 'post',
    data
  })
}

// 风险研判
export function fetchRiskAnalysis({ name, day }) {
  return request({
    url: '/api/v1/web/sl/data/oneBusiness/riskAnalysis',
    method: 'post',
    data: { name, day }
  })
}

// 本周菜单
export function dishesWeek(data) {
  return request({
    url: '/api/v1/web/sl/data/oneCenter/dishesWeek',
    method: 'post',
    data
  })
}

// 获取自查记录列表
export function detailsList(data) {
  return request({
    url: '/api/v1/web/sl/data/oneCenter/self',
    method: 'post',
    data
  })
}
// 获取自查详情
export function checkDetails(params) {
  return request({
    url: '/api/v1/web/sl/data/oneCenter/checkDetails',
    method: 'get',
    params
  })
}
// 获取晨检异常数据
export function checkAbnormalDetails(params) {
  return request({
    url: '/api/v1/web/sl/data/oneCenter/checkAbnormalDetails',
    method: 'get',
    params
  })
}
