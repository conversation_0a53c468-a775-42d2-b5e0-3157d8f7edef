const CopyWebpackPlugin = require('copy-webpack-plugin')
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}
module.exports = {
  devServer: {
    port: 3001,
    proxy: {
      '/api': {
        target: 'https://sn-bigdata.vankeytech.com/',
        // target: 'http://192.168.0.62:8081', // 石伟本地
        changeOrigin: true
      }
    }
  },
  configureWebpack: {
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    externals: {
      AMap: 'AMap',
      AMapUI: 'AMapUI'
    },
    plugins: [
      new CopyWebpackPlugin([{
        from: 'node_modules/@liveqing/liveplayer/dist/component/crossdomain.xml'
      },
      {
        from: 'node_modules/@liveqing/liveplayer/dist/component/liveplayer.swf'
      },
      {
        from: 'node_modules/@liveqing/liveplayer/dist/component/liveplayer-lib.min.js',
        to: 'js/'
      }
      ])

    ]
  },
  lintOnSave: false
}
