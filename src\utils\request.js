import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import router from '../router'

// 创建axios实例
const service = axios.create({
  timeout: 25000, // 请求超时时间
  baseURL: process.env.VUE_APP_BASE_API
})

// request拦截器
service.interceptors.request.use(
  config => {
    const userToken = localStorage.getItem('cateenBigDataUserInfo') ? JSON.parse(localStorage.getItem('cateenBigDataUserInfo')).userToken : localStorage.getItem('token')
    if (userToken) {
      config.headers['X-Auth-Token'] = userToken // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    return config
  },
  error => {
    // Do something with request error
    Promise.reject(error)
  }
)

// response 拦截器
service.interceptors.response.use(
  response => {
    if (!response.data && response.config.url === '/api/v1/login/qryValidateCode') {
      return response
    }
    const res = response.data
    if (res.code === 10011) {
      localStorage.removeItem('cateenBigDataUserInfo')
      router.push('/login')
      return
    }
    if (res.code !== 200 && res.code !== 10011) {
      Message({
        message: res.msg || res.message || 'Error',
        type: 'error',
        duration: 3 * 1000
      })
    } else {
      return response
    }
  },
  error => {
    const errRes = error.response
    console.log(error, 'errorerrorerrorerrorerrorerrorerrorerrorerrorerrorerrorerrorerrorerrorerrorerrorerrorerrorerrorerrorerrorerror')
    if (error.response) {
      Message({
        message: error.response.data.msg || 'Error',
        type: 'error',
        duration: 3 * 1000
      })
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
    } else if (error.request) {
      // The request was made but no response was received
      // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
      // http.ClientRequest in node.js
      // localStorage.removeItem('cateenBigDataUserInfo')
      // router.push('/login')
    } else {
      // Something happened in setting up the request that triggered an Error
    }
    return Promise.reject(error)
  }
)

export default service
