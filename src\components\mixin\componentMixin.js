/* eslint-disable */
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { formatDate } from '@/utils/date.js'
export const componentMixin = {
  data() {
    const DISTRICT_CODE = {
      '双流区': 510116,
      '郫都区': 510116,
      '大邑县': 510129,
    }
    const QUERY_LEVEL = {
      '区': 1,
      '街道': 2
    }
    const STREET_CODE = {
      // 双流区
      '合作街道办事处': 510117019,
      '东升街道办': 510116001,
      '西航港街道办': 510116002,
      '华阳镇街道办': 510116003,
      '中和街道办': 510116004,
      '九江街道办事处': 510116005,
      '黄甲街道办事处': 510116006,
      '公兴街道办事处': 510116007,
      '协和街道办事处': 510116008,
      '太平镇': 510116101,
      '永兴镇': 510116102,
      '籍田镇': 510116106,
      '正兴镇': 510116107,
      '彭镇': 510116108,
      '大林镇': 510116109,
      '煎茶镇': 510116110,
      '黄龙溪镇': 510116111,
      '永安镇': 510116112,
      '黄水镇': 510116115,
      '金桥镇': 510116116,
      '胜利镇': 510116119,
      '新兴镇': 510116120,
      '兴隆镇': 510116121,
      '万安镇': 510116122,
      '白沙镇': 510116123,
      '三星镇': 510116124,
      '合江镇': 510116125,
      // 双流区
      '郫筒街道办事处': 510117001,
      '西园街道办事处': 510117020,
      '团结镇': 510117101,
      '犀浦镇': 510117102,
      '花园镇': 510117103,
      '唐昌镇': 510117104,
      '安德镇': 510117105,
      '三道堰镇': 510117106,
      '安靖镇': 510117107,
      '红光镇': 510117108,
      '新民场镇': 510117110,
      '德源镇': 510117112,
      '友爱镇': 510117115,
      '古城镇': 510117117,
      '唐元镇': 510117118,
    }
    const ALERT_TYPE = {
      'behaviorSum': '行为',
      'ingredientsSum': '食材',
      'sensorSum': '传感器',
      'healthCertificateSum': '健康证'
    }
    const MONTH_DAY = {
      '1': 31,
      '2': new Date().getFullYear()%4 == 0?29:28,
      '3': 31,
      '4': 30,
      '5': 31,
      '6': 30,
      '7': 31,
      '8': 31,
      '9': 30,
      '10': 31,
      '11': 30,
      '12': 31,
    }
    const HEALTHY_TYPE = {
      '1': '正常',
      '2': '临期',
      '3': '过期'
    }
    const DISTRICT_POSITION = {
      '郫筒街道办事处': {lon: 103.887299, lat: 30.810468},
      '合作街道办事处': {lon: 103.922515,lat: 30.757167},
      '西园街道办事处': {lon: 103.919434,lat: 30.760007},
      '团结镇': {lon: 103.984794,lat: 30.821474},
      '犀浦镇': {lon: 103.977278,lat: 30.765639},
      '花园镇': {lon: 103.761442,lat: 30.864913},
      '唐昌镇': {lon: 103.828235,lat: 30.926515},
      '安德镇': {lon: 103.809568,lat: 30.879973},
      '三道堰镇': {lon: 103.922637,lat: 30.868781},
      '安靖镇': {lon: 104.020473,lat: 30.772748},
      '红光镇': {lon: 103.947946,lat: 30.780326},
      '新民场镇': {lon: 103.86601,lat: 30.884144},
      '德源镇': {lon: 103.854805,lat: 30.776088},
      '友爱镇': {lon: 103.801336,lat: 30.829536},
      '古城镇': {lon: 103.933808,lat: 30.90234},
      '唐元镇': {lon: 103.886732,lat: 30.909176},
      '东升街道办': {lon: 103.934087,lat: 30.575407},
      '西航港街道办': {lon: 104.017462,lat: 30.55631},
      '华阳镇街道办': {lon: 104.062961,lat: 30.505987},
      '中和街道办': {lon: 104.099236,lat: 30.56437},
      '九江街道办事处': {lon: 103.924574,lat: 30.628134},
      '黄甲街道办事处': {lon: 103.954191,lat: 30.499561},
      '公兴街道办事处': {lon: 103.999278,lat: 30.492718},
      '协和街道办事处': {lon: 104.022171,lat: 30.510047},
      '太平镇': {lon: 104.20736,lat: 30.43476},
      '永兴镇': {lon: 104.156408,lat: 30.360302},
      '籍田镇': {lon: 104.03111,lat: 30.313485},
      '正兴镇': {lon: 104.049776,lat: 30.442123},
      '彭镇': {lon: 103.88167,lat: 30.588587},
      '大林镇': {lon: 104.127994,lat: 30.289339},
      '煎茶镇': {lon: 104.059634,lat: 30.381825},
      '黄龙溪镇': {lon: 103.976489,lat: 30.323445},
      '永安镇': {lon: 103.995085,lat: 30.412652},
      '黄水镇': {lon: 103.887842,lat: 30.533766},
      '金桥镇': {lon: 103.83603,lat: 30.583092},
      '胜利镇': {lon: 103.914284,lat: 30.507526},
      '新兴镇': {lon: 103.852837,lat: 31.148735},
      '兴隆镇': {lon: 104.113507,lat: 30.423858},
      '万安镇': {lon: 104.118355,lat: 30.495223},
      '白沙镇': {lon: 104.159569,lat: 30.469318},
      '三星镇': {lon: 104.1574,lat: 30.315366},
      '合江镇': {lon: 104.175588,lat: 30.398933},
    }
    const SCHOOL_POSITION = {
      '棠湖中学': {lon: 103.923388, lat: 30.57431},
      '夏风项目点': {lon: 103.908853, lat: 30.549434},
    }
    const DISTRICT_LENGEND = {
      '黄龙溪镇': { left: 33,top: 35 },
      '永安镇': { left: 37,top: 45 },
      '公兴街道办事处': { left: 43,top: 55 },
      '黄甲街道办事处': { left: 48,top: 51 },
      '协和街道办事处': { left: 46,top: 65 },
      '胜利镇': { left: 50,top: 43 },
      '西航港街道办': { left: 52,top: 65 },
      '黄水镇': { left: 54,top: 38 },
      '东升街道办': { left: 58,top: 54 },
      '金桥镇': { left: 58,top: 34 },
      '彭镇': { left: 65,top: 45 },
      '九江街道办事处': { left: 64,top: 58 },
    }
    return {
      DISTRICT_CODE,
      QUERY_LEVEL,
      ALERT_TYPE,
      MONTH_DAY,
      HEALTHY_TYPE,
      STREET_CODE,
      DISTRICT_POSITION,
      SCHOOL_POSITION,
      DISTRICT_LENGEND,
      showViewer: false,
      previewUrl: null
    }
  },
  components: {  ElImageViewer },
  mounted() {},
  computed: {},
  watch: {},
  filters: {
    numberFilter: function (value) {
      if (value) {
        if (value <= 0) {
          return `000`
        } else if (value < 10) {
          return `00${value}`
        } else if (value < 100) {
          return `0${value}`
        } else {
          return value
        }
      } else {
        return `000`
      }
    },
    formatDayTime: function (value, level) {
      if (value) {
        const oriDateSec = Math.ceil((new Date(value).getTime()-new Date().getTime())/1000)
        const oriDay = Math.floor(oriDateSec/60/60/24)
        const oriHour = Math.floor((oriDateSec-oriDay*24*60*60)/60/60)
        const oriMinute =  Math.ceil((oriDateSec-oriDay*24*60*60-oriHour*60*60)/60)
        const resolvedDate = {
          'dayst': oriDay>10?oriDay.toString().slice(0,1):0,
          'daynd': oriDay>10?oriDay.toString().slice(1,):oriDay,
          'hourst': oriHour>10?oriHour.toString().slice(0,1):0,
          'hournd':  oriHour>10?oriHour.toString().slice(1,):oriHour,
          'minutest': oriMinute>10?oriMinute.toString().slice(0,1):0,
          'minutend': oriMinute>10?oriMinute.toString().slice(1,):oriMinute,
        }
        return Number(resolvedDate[level])
      } else {
        return 0
      }
    },
    calcLastestTime: function (value) {
      const seconds = Math.ceil((new Date() - new Date(value)) / 1000)
      const minutes = seconds > 60?Math.floor(seconds / 60): 0
      const hours = minutes > 60?Math.floor(minutes/60):0
      const day = hours > 24?Math.floor(hours/24):0
      if (day > 0) {
        return `${day}天前`
      } else if (hours > 0) {
        return `${hours}小时前`
      } else if (minutes > 0) {
        return `${minutes}分钟前`
      } else if (seconds > 0) {
        return `${seconds}秒前`
      } else {
        return `刚刚发布`
      }
    },
    formateDate: function (value, format) {
      return formatDate(new Date(value), format)
    }
  },
  methods: {
    /**
     * @param {chartIns} echarts实例
     * @param {chartDate} echarts数据
     * @param {curIndex} 当前要展示的数据index
     */
    autoPlayTool (chartIns, chartDate, curIndex, seriesIndex = 0, speed = 1000) {
      clearInterval(chartsTimer)
      let setpfun,chartsTimer;
      setpfun = () => {
        chartIns.dispatchAction({
          type: 'downplay',
          seriesIndex: seriesIndex,
          dataIndex: curIndex-1<0?chartDate.length-1:curIndex-1
        })
        chartIns.dispatchAction({
          type: 'highlight',
          seriesIndex: seriesIndex,
          dataIndex: curIndex
        })
        chartIns.dispatchAction({
          type: 'showTip',
          seriesIndex: seriesIndex,
          dataIndex: curIndex
        })
        curIndex === chartDate.length-1?curIndex = 0:curIndex++
      }
      chartsTimer = setInterval(setpfun, speed)
      chartIns.on('mouseover', function () {
        clearInterval(chartsTimer)
      })
      chartIns.on('mouseout', function () {
        clearInterval(chartsTimer)
        chartsTimer = setInterval(setpfun, speed)
      })
    },
  }
}
