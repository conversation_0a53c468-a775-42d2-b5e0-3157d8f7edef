<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>
<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  name: 'AlarmChart',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    propData: {
      require: false,
      type: Object,
      default: () => {}
    }
  },
  created() {
    this.doNotRedraw = true
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.chart.setOption({
        tooltip: {
          show: true,
          trigger: 'axis'
        },
        grid: {
          top: '20%',
          bottom: '12%',
          left: '7%',
          right: '3%'
        },
        legend: {
          right: 0,
          textStyle: {
            color: '#98BCEC'
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#143E91'
            }
          },
          axisLabel: {
            color: '#A6BAE0'
          },
          data: this.propData.dateList
        },
        yAxis: {
          type: 'value',
          name: '单位:(件)',
          nameTextStyle: {
            color: '#9AACD1'
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#143E91'
            }
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            color: '#A6BAE0'
          },
          minInterval: 1
        },
        series: [
          {
            name: '穿戴告警',
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#24B2E3'
            },
            itemStyle: {
              color: '#24B2E3'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0, 192, 243, 0.7)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 192, 243, 0.1)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            },
            data: this.propData.wearWarningList
          },
          {
            name: '鼠患告警',
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#DCD208'
            },
            itemStyle: {
              color: '#DCD208'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(220, 210, 8, 0.7)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(220, 210, 8, 0.1)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            },
            data: this.propData.ratWarningList
          },
          {
            name: '行为',
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#37E1CD'
            },
            itemStyle: {
              color: '#37E1CD'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(55, 225, 205, 0.7)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(55, 225, 205, 0.1)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            },
            data: this.propData.actionWarningList
          }
        ]
      })
      // this.autoPlayTool(this.chart, this.propData.actionWarningList, 0)
    }
  }
}
</script>
<style scoped>
</style>
