<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  name: 'TrainingExaminationCharts',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    progress: {
      require: false,
      type: Number,
      default: () => 0
    },
    bgColor: {
      require: true,
      type: String,
      default: ''
    }
  },
  watch: {
    progress: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.option = {
        animation: true,
        animationDuration: 3000,
        grid: {
          top: '0%',
          left: '0%',
          bottom: '0%',
          right: '0'
        },
        tooltip: {
          show: false,
          formatter: '{a} <br/>{b} : {c}%'
        },
        series: [
          {
            name: '业务指标',
            type: 'gauge',
            startAngle: 215,
            endAngle: -35,
            radius: '100%',
            detail: {
              formatter: '{value}%',
              offsetCenter: ['7%', '-10%'],
              fontSize: 30,
              color: '#fff'
            },
            data: [{ value: this.progress }],
            axisLine: {
              lineStyle: {
                color: [[this.progress / 100, this.bgColor], [1, 'rgba(6, 28, 72, 1)']],
                width: 20
              }
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            pointer: {
              show: false
            }
          }
        ]
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
