<template>
  <div id="main">
    <div class="main-content subjectSupervision">
      <div
        id="focus_toolTip"
        class="special_focus_toolTip"
        v-html="toolTopbody"
      />
      <div v-if="showWindow" class="list-window">
        <div class="window-quxiao" @click="showWindow = false" />
        <div class="window-title">任务详情</div>
        <div class="window-week">
          <!-- <div class="weekrow">
            <span>任务标题</span>
            <span>{{windowInfo.title}}</span>
          </div> -->
          <div class="weekrow">
            <span>任务内容</span>
            <span>{{ windowInfo.content }}</span>
          </div>
          <div class="weekrow">
            <span>结束时间</span>
            <span>{{ windowInfo.endTime }}</span>
          </div>
          <div class="weekrow">
            <span>任务对象</span>
            <span>{{ windowInfo.taskObject }}</span>
          </div>
        </div>
        <div class="window-tab">
          <div
            v-for="(item, index) in tabs"
            :key="index"
            class="tab-buttom"
            :class="{ active: tabIndex === index ? 'active' : '' }"
            @click="changeTab(index)"
          >
            {{ item.title }}：{{ item.number }}
          </div>
        </div>
        <div class="window-list">
          <div class="list-left">任务反馈</div>
          <div v-if="!tabIndex" class="list-right">
            <div
              v-for="(items, index) in windowInfo.completedCenter"
              :key="index"
              class="right-item"
            >
              <div />
              <div>{{ items.centerName }}</div>
              <div>{{ items.personName }}:</div>
              <div>{{ items.recovery }}</div>
              <div>{{ items.recoveryTime }}</div>
              <div
                :class="
                  windowInfo.completedCenter.length > index + 1 ? 'rowline' : ''
                "
              />
            </div>
            <div
              v-if="windowInfo.completedCenter.length == 0"
              class="no-center"
            >
              <img src="../../assets/img/<EMAIL>" alt="" />
              <span>该任务暂无项目点处理反馈</span>
            </div>
          </div>
          <div v-if="tabIndex" class="list-right">
            <div
              v-for="(items, index) in windowInfo.undoneCenter"
              :key="index"
              class="right-item"
            >
              <div />
              <div>{{ items.centerName }}</div>
              <div>{{ items.personName }}:</div>
              <div>{{ items.recovery }}</div>
              <div>{{ items.recoveryTime }}</div>
              <div
                :class="
                  windowInfo.undoneCenter.length > index + 1 ? 'rowline' : ''
                "
              />
            </div>
            <div v-if="windowInfo.undoneCenter.length == 0" class="no-center">
              <img src="../../assets/img/<EMAIL>" alt="" />
              <span>该任务所有项目点均已反馈</span>
            </div>
          </div>
        </div>
      </div>
      <div id="map_container" ref="map_container" />
      <!-- 左边图表 -->
      <div
        class="left-main left-side"
        :class="{ 'left-hide': animationIndex == 0 && readingMode }"
      >
        <!-- 自查汇总 -->
        <div class="left-main-one">
          <div class="title">
            <img src="../../assets/img/multipleGovernance/bt.png" alt />
            <div>自查汇总</div>
          </div>
          <div class="contents box">
            <!-- <div class="one">
              <span>周期自查</span>
              <span class="nums">
                <span class="num-item">1</span>
                <span class="num-item">1</span>
                <span class="words">月</span>
                <span class="num-item">0</span>
                <span class="num-item">1</span>
                <span class="words">日</span>
                <span class="num-item">0</span>
                <span class="num-item">0</span>
                <span class="words">时</span>
              </span>
              <span class="end">结束</span>
            </div>-->
            <div class="two">
              <div class="two-one">
                <div>未提交</div>
                <div>
                  <countTo :start-val="0" :end-val="selfCheckGather.notCheck" />
                </div>
              </div>
              <div class="two-two">
                <div>待审核</div>
                <div>
                  <countTo
                    :start-val="0"
                    :end-val="selfCheckGather.alreadyCheck"
                  />
                </div>
              </div>
              <div class="two-three">
                <div>合格</div>
                <div>
                  <countTo
                    :start-val="0"
                    :end-val="selfCheckGather.qualified"
                  />
                </div>
              </div>
              <el-popover
                v-if="unqualifiedinfo.length != 0"
                placement="top"
                :title="'不合格项目点数：' + unqualifiedinfo.length"
                width="320"
                trigger="hover"
              >
                <div class="list-box">
                  <div v-for="(item, index) in unqualifiedinfo" :key="index">
                    <div class="list-icon" />
                    <div class="list-item">{{ item.centerName }}</div>
                  </div>
                </div>
                <div slot="reference" class="two-four">
                  <div>不合格</div>
                  <div>
                    <countTo
                      :start-val="0"
                      :end-val="selfCheckGather.unQualified"
                    />
                  </div>
                </div>
              </el-popover>
              <div v-else class="two-four">
                <div>不合格</div>
                <div>
                  <countTo
                    :start-val="0"
                    :end-val="selfCheckGather.unQualified"
                  />
                </div>
              </div>
            </div>
            <div class="three">
              历史自查次数：{{ selfCheckGather.checkCount }}
            </div>
            <div class="four">
              <div class="four-one">历史合格率</div>
              <div class="four-two">
                <passRateCharts
                  v-if="selfCheckGather.passRate"
                  :id="'accessRecordCharts'"
                  :pass-rate="selfCheckGather.passRate"
                  :width="'100%'"
                  :height="'0.1rem'"
                />
              </div>
              <div class="four-three">{{ selfCheckGather.passRate }}%</div>
            </div>
          </div>
        </div>
        <!-- 自查统计 -->
        <div class="left-main-two">
          <div class="title">
            <img src="../../assets/img/multipleGovernance/bt.png" alt />
            <div>消洗记录</div>
          </div>
          <div class="contents box">
            <div class="decontamination-area">
              <div class="center-name">{{ centerName }}</div>
              <!-- <div class="top-flex">
              <div class="item-flex">
                <div class="img-box">
                  <img src="../../assets/img/<EMAIL>" alt>
                </div>
                <div>
                  <div class="num">{{ disinfectModes.total }}</div>
                  <div class="text">消洗记录</div>
                </div>
              </div>
              <div class="item-flex">
                <div class="img-box">
                  <img src="../../assets/img/<EMAIL>" alt>
                </div>
                <div>
                  <div class="num">{{ disinfectModes.avgTime+ 'h' }}</div>
                  <div class="text">平均时长</div>
                </div>
              </div>
            </div> -->
              <div class="decontamination-chart">
                <div class="left-title">
                  <div class="item-flex">
                    <div class="img-box">
                      <!-- <img src="../../assets/img/<EMAIL>" alt> -->
                      <img src="../../assets/img/<EMAIL>" alt />
                    </div>
                    <div>
                      <div class="num">{{ disinfectModes.thingCount }}</div>
                      <div class="text">物品消毒</div>
                    </div>
                  </div>
                  <div class="item-flex">
                    <div class="img-box">
                      <img src="../../assets/img/<EMAIL>" alt />
                    </div>
                    <div>
                      <div class="num">{{ disinfectModes.siteCount }}</div>
                      <div class="text">场所消毒</div>
                    </div>
                  </div>
                </div>
                <div class="right-chart">
                  <decontaminationChart
                    :id="'decontaminationChart'"
                    :prop-data="disinfectList || []"
                    :width="'100%'"
                    :height="'100%'"
                  />
                </div>
              </div>
            </div>
            <!-- <div>
              <div>
                <div>自查项</div>
                <div>
                  <div>合格</div>
                  <div>不合格</div>
                </div>
              </div>
              <div>
                <div v-for="(item,index) in examinationList" :key="index">
                  <div>{{ item.name }}</div>
                  <div>
                    <div class="qualified" :style="{'flex':item.qualified==0?1:item.qualified}">{{ item.qualified }}</div>
                    <div class="unQualified" :style="{'flex':item.unQualified==0?1:item.unQualified}">{{ item.unQualified }}</div>
                  </div>
                </div>
              </div>
            </div>-->
          </div>
        </div>
      </div>
      <!-- 右边图表 -->
      <div
        class="right-main right-side"
        :class="{ 'right-hide': animationIndex == 1 && readingMode }"
      >
        <!-- 培训考试 -->
        <div class="right-main-one">
          <div class="title">
            <img src="../../assets/img/multipleGovernance/bt.png" alt />
            <div>培训考试</div>
          </div>
          <div class="contents box">
            <div>
              <div class="left">
                <trainingExaminationCharts
                  :id="'trainingExaminationCharts1'"
                  :bg-color="'rgba(21, 160, 213, 1)'"
                  :width="'0.7rem'"
                  :height="'0.7rem'"
                  :progress="trainingDetail.progressRate"
                />
                <div class="bottom-fix">
                  <div class="one">培训进度</div>
                  <div class="two">
                    <div>
                      <div>员工总数</div>
                      <div style="color: #f0c635">
                        {{ trainingDetail.total }}
                      </div>
                    </div>
                    <div class="lines" />
                    <div>
                      <div>完成人数</div>
                      <div style="color: #35cc75">
                        {{ trainingDetail.completePerson }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="lines" />
              <div class="right">
                <trainingExaminationCharts
                  :id="'trainingExaminationCharts2'"
                  :bg-color="'rgba(101, 166, 255, 1)'"
                  :width="'0.7rem'"
                  :height="'0.7rem'"
                  :progress="examDetail.progressRate"
                />
                <div class="bottom-fix">
                  <div class="one">考试合格率</div>
                  <div class="two">
                    <div>
                      <div>考试人数</div>
                      <div style="color: #f0c635">{{ examDetail.total }}</div>
                    </div>
                    <div class="lines" />
                    <div>
                      <div>合格人数</div>
                      <div style="color: #35cc75">
                        {{ examDetail.completePerson }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 任务分类 -->
        <div class="right-main-two">
          <div class="title">
            <img src="../../assets/img/multipleGovernance/bt.png" alt />
            <div>任务分类</div>
          </div>
          <div class="contents box">
            <eventTypeCharts
              :id="'eventTypeCharts'"
              :width="'100%'"
              :height="'100%'"
              :prop-data="eventTypes"
            />
          </div>
        </div>
        <!-- 任务分析 -->
        <div class="right-main-three">
          <div class="title">
            <img src="../../assets/img/multipleGovernance/bt.png" alt />
            <div>任务分析</div>
          </div>
          <div class="contents box">
            <div>
              <div>
                <div>任务下发数/条</div>
                <div style="color: #75fffd">
                  <countTo :start-val="0" :end-val="eventAnalysis.eventTotal" />
                </div>
              </div>
              <div>
                <div>正在进行数/条</div>
                <div style="color: #ffe905">
                  <countTo :start-val="0" :end-val="eventAnalysis.processing" />
                </div>
              </div>
            </div>
            <div>
              <div>
                <eventAnalysisCharts
                  :id="'eventAnalysisCharts1'"
                  :width="'100%'"
                  :height="'0.6rem'"
                  :proportion="eventAnalysis.qualifiedProgressRate"
                />
                <div class="text">合格率</div>
              </div>
              <div>
                <eventAnalysisCharts
                  :id="'eventAnalysisCharts2'"
                  :width="'100%'"
                  :height="'0.6rem'"
                  :proportion="eventAnalysis.rejectProgressRate"
                />
                <div class="text">驳回率</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 下边图表 -->
      <div class="bottom-main">
        <!-- 任务列表 -->
        <div class="title">
          <img src="../../assets/img/multipleGovernance/bt.png" alt />
          <div>任务追踪</div>
        </div>
        <div class="contents box">
          <div>
            <div class="thead">
              <div class="tr">
                <div class="td">序号</div>
                <div class="td">任务</div>
                <div class="td">任务来源</div>
                <div class="td">发起人</div>
                <div class="td">下发时间</div>
                <div class="td">下发对象</div>
                <div class="td">处理状态</div>
                <div class="td">更新时间</div>
              </div>
            </div>
            <Swiper class="tbody" :options="swiperOption">
              <SwiperSlide
                v-for="(item, index) in eventList"
                :id="item.id"
                :key="index"
                class="tr"
              >
                <div :id="item.id" class="td">
                  <div>{{ index + 1 }}</div>
                </div>
                <div :id="item.id" class="td">{{ item.eventName }}</div>
                <div :id="item.id" class="td">
                  {{ item.eventType | eventTypeFilter }}
                </div>
                <div :id="item.id" class="td">{{ item.creatorName }}</div>
                <div :id="item.id" class="td">{{ item.createTime }}</div>
                <div :id="item.id" class="td">{{ item.centers[0].name }}</div>
                <div :id="item.id" class="td">
                  <div>
                    <div :id="item.id" class="one">
                      <div
                        :id="item.id"
                        :style="{
                          background:
                            item.eventStatus === 0 ? '#FFE906' : '#98FDFF',
                        }"
                      >
                        <div v-if="item.eventStatus === 0" :id="item.id">
                          已下发
                        </div>
                      </div>
                      <div
                        :id="item.id"
                        :class="item.eventStatus === 0 ? 'line-dashed' : 'line'"
                      />
                      <div
                        :id="item.id"
                        :style="{
                          background:
                            item.eventStatus === 1
                              ? '#FFE906'
                              : item.eventStatus !== 0
                                ? '#98FDFF'
                                : '',
                        }"
                      >
                        <div v-if="item.eventStatus === 1" :id="item.id">
                          处理中
                        </div>
                      </div>
                      <div
                        :id="item.id"
                        :class="item.eventStatus > 1 ? 'line' : 'line-dashed'"
                      />
                      <div
                        :id="item.id"
                        :style="{
                          background:
                            item.eventStatus === 3 || item.eventStatus === 4
                              ? '#FFE906'
                              : item.eventStatus === 2
                                ? '#98FDFF'
                                : '',
                        }"
                      >
                        <div
                          v-if="
                            item.eventStatus === 3 || item.eventStatus === 4
                          "
                        >
                          {{ item.eventStatus === 3 ? '已驳回' : '待审核' }}
                        </div>
                      </div>
                      <div
                        :id="item.id"
                        :class="item.eventStatus === 2 ? 'line' : 'line-dashed'"
                      />
                      <div
                        :id="item.id"
                        :style="{
                          background: item.eventStatus === 2 ? '#FFE906' : '',
                        }"
                      >
                        <div v-if="item.eventStatus === 2">已完成</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div :id="item.id" class="td">{{ item.updateTime }}</div>
              </SwiperSlide>
            </Swiper>
          </div>
        </div>
      </div>
    </div>
    <!-- 图片预览 -->
    <preview-image ref="previewImage" :url-list="previewImageUrlList" />
    <!-- 地图筛选 -->
    <!-- <map-marker-selects @postSelectsData="gethotmap" /> -->
  </div>
</template>

<script>
import AMap from 'AMap'
import PreviewImage from '@/components/PreviewImage'
import viewMixins from '@/views/mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
// import componentTitle from '@/components/title'
import centerMarker from '@/assets/img/<EMAIL>'
import passRateCharts from '@/components/Charts/passRateCharts'
import trainingExaminationCharts from '@/components/Charts/trainingExaminationCharts'
import eventTypeCharts from '@/components/Charts/eventTypeCharts'
import eventAnalysisCharts from '@/components/Charts/eventAnalysisCharts'
import decontaminationChart from '@/components/Charts/decontaminationChart'
import MapMarkerSelects from '@/components/mapMarkerSelects'
import countTo from 'vue-count-to'
import mapImg from '@/assets/img/map.png'
import {
  fetchCenterList,
  fetchCenterInfo,
  fetchSelfCheckGather,
  fetchDisinfectRecord,
  fetchSelfCheckStatistics,
  fetchTrainingExamination,
  fetchEventSource,
  fetchEventList,
  fetchEventAnalysis,
  taskDetails,
  unqualified
} from '@/api/subjectSupervision'
import bus from '@/utils/bus'
export default {
  components: {
    PreviewImage,
    // componentTitle,
    passRateCharts,
    trainingExaminationCharts,
    eventTypeCharts,
    eventAnalysisCharts,
    decontaminationChart,
    countTo,
    MapMarkerSelects
  },
  filters: {
    eventTypeFilter(val) {
      console.log(val)
      if (!val) return '其他'
      const typeMap = {
        1: '整改',
        2: '日常',
        3: '应急',
        4: '其他'
      }
      return typeMap[val]
    }
  },
  mixins: [componentMixin, viewMixins],
  data() {
    return {
      toolTopbody: '',
      weekList: [
        {
          centername: '棠湖中学',
          cpt: '蒋宣龙',
          state: true,
          time: '01-14 10:30'
        },
        {
          centername: '彭镇初级中学',
          cpt: '周明',
          state: true,
          time: '01-14 10:30'
        }
      ],
      tabIndex: 0,
      tabs: [
        {
          title: '完成',
          number: 25
        },
        {
          title: '未完成',
          number: 12
        }
      ],
      showWindow: false,
      unqualifiedinfo: [],
      map: '',
      // mapCenter: [103.900649, 30.469532],
      centerList: [],
      currentMarker: null,
      selfCheckGather: {},
      examinationList: [],
      examDetail: {},
      trainingDetail: {},
      eventTypes: [],
      eventList: [],
      windowInfo: {},
      swiperOption: {
        direction: 'vertical',
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: false,
        spaceBetween: 10,
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        },
        on: {
          click: (v) => {
            console.log('点击了', v.target)
            const id = v.target.getAttribute('id')
            if (!id) return
            taskDetails(id).then((res) => {
              console.log('<<<<<<', res.data.data)
              this.windowInfo = res.data.data
              this.tabs[0].number = res.data.data.completed
              this.tabs[1].number = res.data.data.undone
              this.showWindow = true
            })
          }
        }
      },
      eventAnalysis: {},
      currentWeek: new Date().getDay(),
      leftNum: 0,
      rightNum: 0,
      showbox: true,
      marker: [
        {
          id: 1,
          lng: 103.956649,
          lat: 30.499132
        },
        {
          id: 2,
          lng: 103.9586649,
          lat: 30.599132
        },
        {
          id: 3,
          lng: 103.909649,
          lat: 30.596135
        }
      ],
      previewImageUrlList: [],
      delMarkerList: [],
      search: '',
      disinfectModes: {},
      centerName: '',
      disinfectList: ''
    }
  },
  created() {
    this.getunquali()
  },
  mounted() {
    this.initMap()
    this.getCenterList()
    this.getSelfCheckGather()
    this.getSelfCheckStatistics()
    this.getTrainingExamination()
    this.getEventSource()
    this.getEventList()
    this.getEventAnalysis()
    bus.$on('getSearch', (msg) => {
      this.search = msg
      this.getCenterList()
    })
  },
  methods: {
    getunquali() {
      unqualified(this.DISTRICT_CODE['大邑县']).then((res) => {
        this.unqualifiedinfo = res.data.data
        console.log('查到的数据', res.data.data)
      })
    },
    // 切换tab
    changeTab(index) {
      this.tabIndex = index
    },
    // 点击任务
    couItem() {
      this.showWindow = true
    },
    initMap() {
      const _this = this
      const map = new AMap.Map(this.$refs.map_container, {
        // center: _this.mapCenter,
        center: [106.958633, 34.43728],
        zoom: 5,
        viewMode: '3D',
        // pitch: 40,
        // rotation: -50,
        rotateEnable: true,
        zoomEnable: true,
        dragEnable: true,
        zooms: [5, 18],
        // features: [],
        mapStyle: 'amap://styles/darkblue', // 设置地图的显示样式

      })
      this.addControl(AMap, map, '1rem')
      this.map = map
      map.on('click', function(e) {
        map.clearInfoWindow()
      })
    },
    // 消洗记录
    getDisinfectRecord(id) {
      if (this.centerList.length) {
        fetchDisinfectRecord(id || this.centerList[0].id).then((res) => {
          console.log(res.data.data, '消洗记录')
          this.disinfectModes = res.data.data
          this.disinfectList = res.data.data.disinfectList
        })
      }
    },
    // 学校地图坐标
    getCenterList() {
      const obj = {
        serviceName: this.search ? this.search : undefined
      }
      fetchCenterList(obj)
        .then((res) => {
          this.centerList = res.data.data.map((item) => {
            return {
              id: item.id,
              name: item.name,
              lng: item.lng,
              lat: item.lat
            }
          })
          this.centerName = this.centerList.length && this.centerList[0].name
          this.creatMarker()
        })
        .finally(() => {
          this.getDisinfectRecord()
        })
    },
    // 添加marker
    creatMarker() {
      const map = this.map
      if (this.delMarkerList && this.delMarkerList.length > 0) {
        map.remove(this.delMarkerList)
        this.delMarkerList = []
      }
      this.centerList.forEach((marker) => {
        let stationMarker = ''
        // eslint-disable-next-line eqeqeq
        const centerIcon = new AMap.Icon({
          size: new AMap.Size(48, 55),
          image: centerMarker,
          imageSize: new AMap.Size(48, 55),
          imageOffset: new AMap.Pixel(0, 0)
        })
        stationMarker = new AMap.Marker({
          map: map,
          icon: centerIcon,
          zIndex: 999,
          position: [marker.lng, marker.lat],
          offset: new AMap.Pixel(-18, -16),
          centerId: marker.id
        })
        this.delMarkerList.push(stationMarker)
        AMap.event.addListener(stationMarker, 'click', (e) => {
          this.showInfo(stationMarker, e.target.w.centerId)
          this.getDisinfectRecord(e.target.w.centerId)
        })
      })
    },
    // 打开弹框
    showInfo(marker, centerId) {
      const findCenter = this.centerList.find((item) => item.id === centerId)
      fetchCenterInfo(centerId).then((res) => {
        const detailObj = res.data.data
        detailObj.qualified = detailObj.qualified || 0
        detailObj.qualifiedRatio =
          (detailObj.qualified / detailObj.personTotal).toFixed(2) * 100 + '%'
        const map = this.map
        const html = `<div class="marker-content">
          <div class="title"><div title="${findCenter.name}">${
  findCenter.name
}</div></div>
          <div class="one">
            <div class="name">自查周期：<span>${
  detailObj.checkCycle ? detailObj.checkCycle + '天/次' : ''
}</span></div>
            <div>${detailObj.checkType}</div>
          </div>
          <div class="name two">历史自查</div>
          <div class="three">
            <div>
              <div>合格</div>
              <div>${detailObj.pass || 0}</div>
            </div>
            <div>
              <div>不合格</div>
              <div>${detailObj.unqualified || 0}</div>
            </div>
            <div>
              <div>未核查</div>
              <div>${detailObj.noCheck || 0}</div>
            </div>
          </div>
          <div class="four">
            <div class="name">培训进度：</div>
            <div><div style="width:${
  detailObj.trainProgressRate
    ? detailObj.trainProgressRate + '%'
    : '0%'
}"></div></div>
            <div>${detailObj.trainProgressRate + '%'} </div>
          </div>
          <div class="five">
            <div class="name">考试合格：</div>
            <div><div style="width:${
  detailObj.qualified && detailObj.personTotal
    ? detailObj.qualifiedRatio
    : '0%'
}"></div></div>
            <div>${detailObj.qualified || 0}人</div>
          </div>
          <div class="six">
            <div>
              <div class="name">未完成任务：</div>
              <div>${detailObj.eventUndone}件</div>
            </div>
            <div>
              <div class="name">已完成任务：</div>
              <div>${detailObj.eventComplete}件</div>
            </div>
          </div>
        </div>`
        const infoWindow = new AMap.InfoWindow({
          offset: new AMap.Pixel(0, 0),
          isCustom: true,
          content: html
        })
        infoWindow.open(map, marker.getPosition())
      })
    },
    // 自查汇总
    getSelfCheckGather() {
      fetchSelfCheckGather().then((res) => {
        console.log(res.data.data)
        this.selfCheckGather = res.data.data
      })
    },
    // 自查统计
    getSelfCheckStatistics() {
      fetchSelfCheckStatistics().then((res) => {
        this.examinationList = res.data.data
      })
    },
    // 培训考试
    getTrainingExamination() {
      fetchTrainingExamination().then((res) => {
        this.examDetail = res.data.data.exam
        this.trainingDetail = res.data.data.training
      })
    },
    // 任务分类
    getEventSource() {
      fetchEventSource().then((res) => {
        this.eventTypes = res.data.data.map((item) => {
          const obj = {}
          switch (item.eventType) {
            case 0:
              obj.name = '其他'
              break
            case 1:
              obj.name = '自查'
              break
            case 2:
              obj.name = '告警'
              break
            case 3:
              obj.name = '投诉'
              break
            case 4:
              obj.name = '整改'
              break
            case 5:
              obj.name = '抽查'
              break
            default:
              obj.name = '环境'
              break
          }
          obj.value = item.total
          return obj
        })
      })
    },
    // 任务列表
    getEventList() {
      fetchEventList().then((res) => {
        console.log(res.data.data, '>>>>>>>>>>>>')
        this.eventList = res.data.data
      })
    },
    // 任务分析
    getEventAnalysis() {
      fetchEventAnalysis().then((res) => {
        res.data.data.eventTotal = Number(res.data.data.eventTotal)
        res.data.data.processing = Number(res.data.data.processing)
        res.data.data.qualifiedProgressRate =
          res.data.data.qualifiedProgressRate || 0
        res.data.data.rejectProgressRate = res.data.data.rejectProgressRate || 0
        this.eventAnalysis = res.data.data
      })
    }
  }
}
</script>

<style lang="scss">
.subjectSupervision {
  .circle-one {
    width: 0.18rem;
    height: 0.16rem;
    border: 0.01rem solid #3fd4ff;
    border-radius: 50%;
    // transform: rotateX(15deg) rotateY(15deg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    animation: oneanimation 0.8s ease infinite;
    .circle-two {
      width: 0.13rem;
      height: 0.11rem;
      border: 0.03rem solid #50f9ff;
      border-radius: 50%;
      box-sizing: border-box;
      // transform: rotateX(15deg) rotateY(15deg);
      display: flex;
      align-items: center;
      justify-content: center;
      animation: twoanimation 0.8s ease infinite;
      .circle-three {
        width: 0.03rem;
        height: 0.03rem;
        background: #fdffec;
        border-radius: 50%;
        // transform: rotateX(15deg) rotateY(15deg);
        animation: threeanimation 0.8s ease infinite;
      }
    }
  }
  .circle-one-active {
    border-color: #da4648;
    .circle-two-active {
      border-color: #da4648;
      .circle-three-active {
        background-color: #da4648;
      }
    }
  }
  @keyframes oneanimation {
    from {
      transform: scale(0);
    }
    to {
      transform: scale(1);
    }
  }
  @keyframes twoanimation {
    from {
      transform: scale(0);
    }
    to {
      transform: scale(1);
    }
  }
  @keyframes threeanimation {
    from {
      transform: scale(0) rotateX(15deg) rotateY(15deg);
    }
    to {
      transform: scale(1) rotateX(15deg) rotateY(15deg);
    }
  }
  .amap-info-content,
  .amap-info-outer {
    padding: 0;
  }
  .amap-info-content {
    background: transparent !important;
  }
  .amap-info-outer,
  .amap-menu-outer {
    box-shadow: none !important;
  }
  .amap-info-sharp {
    display: none !important;
  }
  .marker-content {
    width: 1.74rem;
    height: 1.41rem;
    background-image: url('../../assets/img/marker-bg.png');
    background-size: 100% 100%;
    position: relative;
    box-sizing: border-box;
    padding-left: 0.1rem;
    padding-right: 0.1rem;
    .closeInfoWindow {
      position: absolute;
      top: 0.1rem;
      right: 0.05rem;
      width: 0.15rem;
      height: 0.15rem;
      cursor: pointer;
    }
    .title {
      width: 1.24rem;
      height: 0.2rem;
      line-height: 0.2rem;
      box-sizing: border-box;
      padding-bottom: 0.01rem;
      background-image: url('../../assets/img/marker-title.png');
      background-size: 100% 100%;
      text-align: center;
      margin: 0 auto;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      > div {
        width: 0.65rem;
        height: 0.2rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .name {
      font-size: 0.08rem;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }
    .one {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 0.05rem;
      > div:nth-of-type(1) {
        margin-right: 0.07rem;
      }
      > div:nth-of-type(2) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 0.4rem;
        height: 0.13rem;
        background: #021443;
        border: 1px solid #13ecff;
        border-radius: 0.02rem;
        background: rgba(4, 5, 26, 0.3);
        box-shadow: inset 0 0 0.1rem #13ecff;
      }
    }
    .two {
      margin-top: 0.05rem;
    }
    .three {
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin-top: 0.05rem;
      text-align: center;
      > div {
        > div:nth-of-type(1) {
          font-size: 0.08rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #a8bee7;
        }
        > div:nth-of-type(2) {
          font-size: 0.11rem;
          font-family: PingFang SC;
          font-weight: bold;
        }
      }
      > div:nth-of-type(1) {
        > div:nth-of-type(2) {
          color: #32b6a6;
        }
      }
      > div:nth-of-type(2) {
        > div:nth-of-type(2) {
          color: #f94849;
        }
      }
      > div:nth-of-type(3) {
        > div:nth-of-type(2) {
          color: #dde67b;
        }
      }
    }
    .four {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 0.05rem;
      > div:nth-of-type(2) {
        flex: 1;
        height: 0.04rem;
        background: #000f40;
        > div {
          width: 79%;
          height: 0.04rem;
          background: #90e1ff;
        }
      }
      > div:nth-of-type(3) {
        font-size: 0.08rem;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        margin-left: 0.08rem;
        min-width: 0.25rem;
      }
    }
    .five {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 0.05rem;
      > div:nth-of-type(2) {
        flex: 1;
        height: 0.04rem;
        background: #000f40;
        > div {
          width: 75%;
          height: 0.04rem;
          background: #3bcd5d;
        }
      }
      > div:nth-of-type(3) {
        font-size: 0.08rem;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        margin-left: 0.08rem;
        min-width: 0.25rem;
      }
    }
    .six {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 0.05rem;
      > div {
        display: flex;
        align-items: center;
      }
      > div:nth-of-type(1) {
        > div:nth-of-type(2) {
          color: #ff4243;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
@import url('../../assets/css/index.css');
@import './index.scss';
</style>

