<template>
  <div id="main">
    <div class="main-content">
      <div v-if="showWindow" class="list-window">
        <div class="window-quxiao" @click="closewindow" />
        <div class="window-title">
          {{ windowInfo.earlyWarningTypeName ? windowInfo.earlyWarningTypeName : '异常活动告警' }}
        </div>
        <div class="window-week">
          <img v-if="getWarning" :src="earlyimage" alt="" class="leftimg" @click="showPreviewImage(earlyimage)" />
          <img v-else :src="windowInfo.image" alt="" class="leftimg" @click="showPreviewImage(windowInfo.image)" />
          <div class="rightinfo">
            <div class="infotop">
              <div class="infoname">项目点: {{ windowInfo.centerName || windowInfo.canteenName }}</div>
              <div class="infotime">
                {{ windowInfo.earlyWarningTime ? windowInfo.earlyWarningTime : '' }}
              </div>
            </div>
            <div class="infofoot">详情：{{ windowInfo.earlyWarningContent || windowInfo.alarmContent || '暂无详情' }}</div>
          </div>
        </div>
        <div class="window-video">
          <LivePlayer
            v-if="windowInfo.videoUrl && !getWarning"
            class="player"
            :video-url="windowInfo.videoUrl"
            loop
            autoplay
            fluent
            :hasaudio="false"
            live
            stretch
          />
          <LivePlayer v-if="getWarning" class="player" :video-url="warningVideoUrl" loop autoplay fluent :hasaudio="false" live stretch />
          <img v-if="!windowInfo.videoUrl && !getWarning" class="player-img" :src="windowInfo.image" alt="" @click="showPreviewImage(windowInfo.image)" />
        </div>
      </div>
      <div id="map_container" ref="map_container" />
      <!-- 地图图例 -->
      <div class="legend">
        <div class="legend-item">
          <img src="../../assets/img/<EMAIL>" alt="" />
          <span>营业</span>
        </div>
        <div class="legend-item">
          <img src="../../assets/img/<EMAIL>" alt="" />
          <span>已歇业</span>
        </div>
      </div>
      <div class="left-main left-side" :class="{ 'left-hide': animationIndex == 0 && readingMode }">
        <div class="left-top">
          <div class="box-title">
            <img src="../../assets/img/<EMAIL>" alt />
            <div>设备在线</div>
          </div>
          <div class="camera-main">
            <div>
              <div style="color: #e94a48">
                <countTo :start-val="0" :end-val="equipmentData.total" :duration="3000" />
              </div>
              <div>摄像头总数</div>
            </div>
            <el-popover v-if="offline.length != 0" placement="top" :title="'离线摄像头数：' + offline.length" width="320" trigger="hover">
              <div class="list-box">
                <div v-for="(item, index) in offline" :key="index">
                  <div class="list-icon" />
                  <div class="list-item">{{ item.name }}</div>
                </div>
              </div>

              <div slot="reference">
                <div style="color: #f5ca35">
                  <countTo class="num" :start-val="0" :end-val="equipmentData.line" :duration="3000" />
                </div>
                <div class="title">在线数量</div>
              </div>
            </el-popover>
            <div v-else>
              <div style="color: #f5ca35">
                <countTo class="num" :start-val="0" :end-val="equipmentData.line" :duration="3000" />
              </div>
              <div class="title">在线数量</div>
            </div>

            <div>
              <div style="color: #32b6a6">{{ equipmentData.onlineRatio }}</div>
              <div>在线率</div>
            </div>
          </div>
        </div>
        <!-- 新增选择警告 -->
        <div class="left-select">
          <div class="box-title">
            <img src="../../assets/img/<EMAIL>" alt />
            <div>现场回放</div>
          </div>
          <div class="select-main">
            <SelectFrom />
          </div>
        </div>
        <div class="left-center">
          <div class="box-title">
            <img src="../../assets/img/<EMAIL>" alt />
            <div>AI告警</div>
          </div>
          <div class="warning-chart">
            <AlarmChart v-if="aiWarningObj" :id="'alarmChart'" :width="'100%'" :height="'100%'" :prop-data="aiWarningObj" />
          </div>
        </div>
        <div class="left-bottom">
          <div class="box-title">
            <img src="../../assets/img/<EMAIL>" alt />
            <div>最新告警</div>
          </div>
          <div class="dangetab">
            <div
              v-for="(item, index) in tabs"
              :key="index"
              class="buttom"
              :class="{ active: tabIndex === index ? 'active' : '' }"
              @click="changeTab(index, item.typeid)"
            >
              {{ item.title }}
            </div>
          </div>
          <div v-if="alarmList.length > 0" class="warning-list" @mouseenter="stopSwiper('swiper')" @mouseleave="startSwiper('swiper')">
            <swiper ref="swiper" :options="swiperOption" class="warning-swiper">
              <swiper-slide
                v-for="(item, index) in alarmList"
                :key="index"
                class="alarm-item"
                :early-warning-type-name="item.earlyWarningTypeName"
                :early-warning-type="item.earlyWarningType"
                :image="item.image"
                :center-name="item.centerName"
                :early-warning-time="item.earlyWarningTime"
                :early-time="item.earlyTime"
                :device="item.device"
                :channel="item.channel"
                :video-url="item.videoUrl"
                :early-warning-content="item.earlyWarningContent"
              >
                <div
                  class="target-box"
                  :earlyWarningTypeName="item.earlyWarningTypeName"
                  :earlyWarningType="item.earlyWarningType"
                  :image="item.image"
                  :centerName="item.centerName"
                  :earlyWarningTime="item.earlyWarningTime"
                  :earlyTime="item.earlyTime"
                  :device="item.device"
                  :channel="item.channel"
                  :videoUrl="item.videoUrl"
                  :earlyWarningContent="item.earlyWarningContent"
                >
                  <div class="item-header">
                    <img src="../../assets/img/lightning.png" alt />
                    <div
                      class="title target-box"
                      :earlyWarningTypeName="item.earlyWarningTypeName"
                      :earlyWarningType="item.earlyWarningType"
                      :image="item.image"
                      :centerName="item.centerName"
                      :earlyWarningTime="item.earlyWarningTime"
                      :earlyTime="item.earlyTime"
                      :device="item.device"
                      :channel="item.channel"
                      :videoUrl="item.videoUrl"
                      :earlyWarningContent="item.earlyWarningContent"
                    >
                      {{ item.earlyWarningTypeName }}
                    </div>
                    <div
                      class="header-time target-box"
                      :earlyWarningTypeName="item.earlyWarningTypeName"
                      :earlyWarningType="item.earlyWarningType"
                      :image="item.image"
                      :centerName="item.centerName"
                      :earlyWarningTime="item.earlyWarningTime"
                      :earlyTime="item.earlyTime"
                      :device="item.device"
                      :videoUrl="item.videoUrl"
                      :earlyWarningContent="item.earlyWarningContent"
                      :channel="item.channel"
                    >
                      {{ item.earlyWarningTime }}
                    </div>
                  </div>
                  <div
                    class="item-main target-box"
                    :earlyWarningTypeName="item.earlyWarningTypeName"
                    :earlyWarningType="item.earlyWarningType"
                    :image="item.image"
                    :centerName="item.centerName"
                    :earlyWarningTime="item.earlyWarningTime"
                    :earlyTime="item.earlyTime"
                    :device="item.device"
                    :channel="item.channel"
                    :videoUrl="item.videoUrl"
                    :earlyWarningContent="item.earlyWarningContent"
                  >
                    <img
                      class="target-box"
                      :src="item.image"
                      alt
                      :earlyWarningTypeName="item.earlyWarningTypeName"
                      :earlyWarningType="item.earlyWarningType"
                      :image="item.image"
                      :centerName="item.centerName"
                      :earlyWarningTime="item.earlyWarningTime"
                      :earlyTime="item.earlyTime"
                      :device="item.device"
                      :channel="item.channel"
                      :videoUrl="item.videoUrl"
                      :earlyWarningContent="item.earlyWarningContent"
                    />
                    <div
                      class="item-right-main target-box"
                      :earlyWarningTypeName="item.earlyWarningTypeName"
                      :earlyWarningType="item.earlyWarningType"
                      :image="item.image"
                      :centerName="item.centerName"
                      :earlyWarningTime="item.earlyWarningTime"
                      :earlyTime="item.earlyTime"
                      :device="item.device"
                      :channel="item.channel"
                      :videoUrl="item.videoUrl"
                      :earlyWarningContent="item.earlyWarningContent"
                    >
                      <div
                        class="target-box"
                        :earlyWarningTypeName="item.earlyWarningTypeName"
                        :earlyWarningType="item.earlyWarningType"
                        :image="item.image"
                        :centerName="item.centerName"
                        :earlyWarningTime="item.earlyWarningTime"
                        :earlyTime="item.earlyTime"
                        :device="item.device"
                        :videoUrl="item.videoUrl"
                        :earlyWarningContent="item.earlyWarningContent"
                        :channel="item.channel"
                      >
                        项目点:{{ item.centerName }}
                      </div>
                      <div
                        class="target-box"
                        :earlyWarningTypeName="item.earlyWarningTypeName"
                        :earlyWarningType="item.earlyWarningType"
                        :image="item.image"
                        :centerName="item.centerName"
                        :earlyWarningTime="item.earlyWarningTime"
                        :earlyTime="item.earlyTime"
                        :device="item.device"
                        :videoUrl="item.videoUrl"
                        :channel="item.channel"
                        :earlyWarningContent="item.earlyWarningContent"
                      >
                        详情:{{ item.earlyWarningContent }}
                      </div>
                    </div>
                  </div>
                </div>
              </swiper-slide>
            </swiper>
          </div>
          <div v-else class="warning-list" style="display: flex; justify-content: center; align-items: center">暂无数据</div>
        </div>
      </div>
      <div class="right-main right-side" :class="{ 'right-hide': animationIndex == 1 && readingMode }">
        <div class="box-title">
          <img src="../../assets/img/<EMAIL>" alt />
          <div>实时监控</div>
        </div>
        <div class="right-container">
          <div>
            <div class="center-bg">
              <img src="../../assets/img/school_icon.png" alt />
              <div>{{ randomName }}</div>
            </div>
            <div class="player-box">
              <div v-for="(item, index) in monitorList" :key="index" class="player-item">
                <div class="monitor">
                  <img v-if="item.number" src="../../assets/img/<EMAIL>" alt class="left-top-img" />
                  <player
                    v-if="item.videoUrl"
                    :class="'liveplayer' + index"
                    :video-url="item.videoUrl"
                    :has-audio="false"
                    height="112px"
                    style="width: 0.95rem"
                    :is-full-resize="true"
                    :autoplay="true"
                  />
                  <div v-if="item.isHoliday" class="isHoliday">
                    <div class="icon" />
                    <div>已歇业</div>
                  </div>
                  <div v-if="!item.isHoliday && !item.type" class="isHoliday">
                    <div class="icon" />
                    <div>设备离线</div>
                  </div>
                  <div
                    v-if="!item.videoUrl"
                    v-loading="item.loading"
                    element-loading-background="rgba(0, 0, 0, 0.8)"
                    element-loading-text="加载中"
                    element-loading-spinner="el-icon-loading"
                  >
                    <!-- 封面图 -->
                    <img v-if="(item.url && !item.videoUrl) || (item.url && !item.type)" :src="item.url" alt class="poster" />
                    <!-- 无封面图占位图 -->
                    <img v-else src="../../assets/img/<EMAIL>" class="noVideoPoster" />
                  </div>
                </div>

                <!-- 播放按钮 -->
                <img
                  v-if="!item.videoUrl && item.type && !item.loading"
                  src="../../assets/img/video_play.png"
                  alt
                  class="video-play"
                  @click="startPlay(index)"
                />
                <div class="monitor-name">{{ item.videoName }}</div>
              </div>
            </div>
          </div>
          <img src="../../assets/img/<EMAIL>" alt class="random-img" @click="clickRandomSampling" />
        </div>
      </div>
      <div v-if="videoShow" class="video-main">
        <LivePlayer v-if="curVideoUrl" :video-url="curVideoUrl" autoplay fluent live :hasaudio="false" stretch />
        <img v-else src="../../assets/img/no_video.png" alt style="width: 30%; margin: 0.4rem 0 0 1.2rem" />
      </div>
    </div>
    <!-- 图片预览 -->
    <preview-image ref="previewImage" :url-list="previewImageUrlList" />
    <!-- 地图筛选 -->
    <map-marker-selects @postSelectsData="getCenterMarker" />
  </div>
</template>

<script>
import AMap from 'AMap'
import PreviewImage from '@/components/PreviewImage'
import SelectFrom from '@/components/Selectfrom'
import viewMixins from '@/views/mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
import AlarmChart from '@/components/Charts/alarmChart'
import MapMarkerSelects from '@/components/mapMarkerSelects'
import echarts from 'echarts'
import warningIcon from '@/assets/img/<EMAIL>'
import greenIcon from '@/assets/img/<EMAIL>'
import yfj from '@/assets/img/<EMAIL>'
import countTo from 'vue-count-to'
import LivePlayer from '@liveqing/liveplayer'
import moment from 'moment'
import Player from '@/components/jessibucaPlayer/jessibuca'

import {
  fetchCenterMarker,
  fetchEquipment,
  fetchAiWarning,
  fetchNewWarning,
  randomSampling,
  startLive,
  fetchPlayback,
  keepPlayback,
  offline,
} from '@/api/brightKitchen'
import bus from '@/utils/bus'

export default {
  components: {
    PreviewImage,
    Player,
    AlarmChart,
    countTo,
    LivePlayer,
    SelectFrom,
    MapMarkerSelects,
  },
  mixins: [componentMixin, viewMixins],
  data() {
    return {
      windowInfo: {},
      showWindow: false,
      liveArr: [],
      streamId: '',
      form: {
        xzxx: '',
        sxt: '',
        starttime: '',
        overtime: '',
      },
      tabIndex: 0,
      reciprocal: 90,
      tabs: [
        {
          title: '穿戴告警',
          typeid: 4,
        },
        {
          title: '行为分析',
          typeid: 5,
        },
        {
          title: '鼠患告警',
          typeid: 7,
        },
      ],
      previewImageUrlList: [],
      map: '',
      markerName: '',
      markerList: [],
      equipmentData: {},
      aiWarningObj: undefined,
      alarmList: [],
      swiperOption: {
        direction: 'vertical',
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: true,
        preventLinksPropagation: false,
        autoplay: {
          delay: 3000,
          disableOnInteraction: false,
        },
        on: {
          click: (v) => {
            const className = v.target.className
            if (!className.includes('target-box')) return

            const obj = {
              earlyWarningTypeName: v.target.getAttribute('earlyWarningTypeName'),
              earlyWarningType: Number(v.target.getAttribute('earlyWarningType')),
              image: v.target.getAttribute('image'),
              centerName: v.target.getAttribute('centerName'),
              earlyWarningTime: v.target.getAttribute('earlyWarningTime'),
              earlyTime: v.target.getAttribute('earlyTime'),
              device: v.target.getAttribute('device'),
              channel: v.target.getAttribute('channel'),
              earlyWarningContent: v.target.getAttribute('earlyWarningContent'),
              videoUrl: v.target.getAttribute('videoUrl'),
            }
            this.showWindow = true
            this.windowInfo = obj
          },
        },
      },
      offline: [],
      randomName: '全国随机项目点',
      monitorList: [],
      delMarkerList: [],
      search: '',
      randomMarker: {},
      canteenName: '',
      curVideoUrl: '',
      getWarning: false,
      videoShow: false,
      timer: null,
      lastIndex: undefined,
      warningShow: false,
      warningData: undefined,
      warningVideoUrl: '',
      reciprocalInterval: null,
      earlyWarningType: '',
      earlyimage: '',
      centerName: undefined,
    }
  },
  created() {
    this.getQueryMsg()
    this.getoffline()
  },
  mounted() {
    this.initMap()
    this.getCenterMarker()
    this.alarmLatest()
    this.getEquipment()
    this.getAiWarning()
    bus.$on('getSearch', (msg) => {
      this.search = msg
      this.getCenterMarker(false)
    })
    bus.$on('getKitchenWarning', (msg) => {
      this.windowInfo = msg
      this.earlyimage = msg.imageUrl
      this.warningData = msg
      this.getWarningUrl()
    })
  },
  beforeDestroy() {
    this.videoShow = false
    clearInterval(this.reciprocalInterval)
  },
  methods: {
    // 获取离线摄像头
    getoffline() {
      offline(this.DISTRICT_CODE['大邑县']).then((res) => {
        this.offline = res.data.data
      })
    },
    // 关闭弹窗
    closewindow() {
      this.getWarning = false
      this.showWindow = false
      this.warningVideoUrl = ''
    },
    // 打开回放
    openPlayer(obj) {
      this.warningVideoUrl = ''
      this.showWindow = true
      this.earlyWarningType = obj.earlyWarningType
      this.earlyimage = obj.image
      this.markerName = obj.centerName
      this.windowInfo = obj
      const startTime = moment(new Date(obj.earlyTime).getTime() - 5 * 1000).format('YYYY-MM-DD HH:mm:ss')
      let endTime = moment(new Date(obj.earlyTime).getTime() + 25 * 1000).format('YYYY-MM-DD HH:mm:ss')
      if (new Date(obj.earlyWarningTime).getTime() + 25 * 1000 > new Date().getTime()) {
        endTime = moment(new Date().getTime() - 2 * 1000).format('YYYY-MM-DD HH:mm:ss')
      }
      const data = {
        deviceSerial: obj.device,
        channelSerial: obj.channel,
        startTime,
        endTime,
      }
      fetchPlayback(data)
        .then((res) => {
          this.warningVideoUrl = res.data.data.flv
          this.warningShow = true
          this.streamId = res.data.data.streamId
          keepPlayback(this.streamId)
        })
        .catch(() => {
          // 回放获取失败时的处理
        })
    },
    // tab切换
    changeTab(val, id) {
      this.tabIndex = val
      const data = {
        districtCode: this.DISTRICT_CODE['大邑县'],
        type: id || 4,
      }
      fetchNewWarning(data).then((res) => {
        this.alarmList = res.data.data
      })
    },
    // 初始化tab数据
    alarmLatest() {
      const data = {
        districtCode: this.DISTRICT_CODE['大邑县'],
        type: 4,
      }
      fetchNewWarning(data).then((res) => {
        this.alarmList = res.data.data
      })
    },
    // 获取推送告警数据
    getQueryMsg() {
      const { data } = this.$route.query
      if (data) {
        this.warningData = data
        this.earlyimage = data.imageUrl
        this.windowInfo = data
        this.tabIndex = data.type
        this.getWarningUrl()
      }
    },
    getWarningUrl() {
      this.getWarning = true
      const obj = {
        device: this.warningData ? this.warningData.monitor.device : undefined,
        channel: this.warningData ? this.warningData.monitor.channel : undefined,
      }
      this.reciprocalInterval = setInterval(() => {
        this.reciprocal--
        if (this.reciprocal < 0) {
          clearInterval(this.reciprocalInterval)
        }
      }, 1000)
      startLive(obj).then((res) => {
        this.warningVideoUrl = res.data.data.flv
        this.showWindow = true
        this.warningShow = true
      })
    },
    // 点击回放
    getPlayback() {
      const createAt = new Date(this.warningData.createAt.split('.')[0]).getTime()
      const startTime = moment(createAt - 5 * 1000).format('YYYY-MM-DD HH:mm:ss')
      let endTime = moment(createAt + 30 * 1000).format('YYYY-MM-DD HH:mm:ss')
      if (createAt + 30 * 1000 > new Date().getTime()) {
        endTime = moment(new Date().getTime() - 5 * 1000).format('YYYY-MM-DD HH:mm:ss')
      }

      const obj = {
        deviceSerial: this.warningData.monitor.device,
        channelSerial: this.warningData.monitor.channel,
        startTime,
        endTime,
      }
      // 获取报警回放
      fetchPlayback(obj).then((res) => {
        this.warningVideoUrl = res.data.data.flv
      })
      const data = {
        districtCode: this.DISTRICT_CODE['大邑县'],
        type: this.tabIndex,
      }
      // 最新告警
      setTimeout(() => {
        fetchNewWarning(data).then((res) => {
          this.alarmList = res.data.data
        })
        this.swiperOption.autoplay.delay = 5000
      }, 3000)
    },
    initMap() {
      this.map = new AMap.Map(this.$refs.map_container, {
        center: [106.958633, 34.43728],
        zoom: 5,
        viewMode: '3D',
        rotateEnable: true,
        zoomEnable: true,
        dragEnable: true,
        zooms: [5, 18],
        mapStyle: 'amap://styles/darkblue', // 设置地图的显示样式
      })

      this.map.on('click', () => {
        this.map.clearInfoWindow()
        this.warningShow = false
        this.earlyWarningType = 1
      })
      this.addControl(AMap, this.map)
    },
    // 学校地图坐标
    getCenterMarker(selectsData) {
      this.map.clearInfoWindow()
      const obj = {
        streetCode: selectsData ? selectsData.streetCode : undefined,
        centerName: this.search ? this.search : undefined,
        centerType: selectsData ? selectsData.centerType : undefined,
      }
      fetchCenterMarker(obj).then((res) => {
        this.markerList = res.data.data.map((item) => {
          return {
            isHoliday: item.isHoliday,
            lng: item.lng,
            lat: item.lat,
            id: item.centerId,
            centerName: item.centerName,
            earlyWarningDTOS: item.earlyWarningDTOS,
          }
        })
        this.renderMarker()
        this.clickRandomSampling()
      })
    },
    getEquipment() {
      fetchEquipment(this.DISTRICT_CODE['大邑县']).then((res) => {
        const data = res.data.data
        data.onlineRatio = parseInt((data.line / data.total) * 100) + '%'
        this.equipmentData = data
      })
    },
    // AI告警
    getAiWarning() {
      fetchAiWarning(this.DISTRICT_CODE['大邑县']).then((res) => {
        const warningObj = {
          dateList: [],
          wearWarningList: [],
          ratWarningList: [],
          actionWarningList: [],
          popUpdatalist: [],
          warningsNumbox: [],
          warningsNum: [], // 共有多少警告
        }
        res.data.data.forEach((item) => {
          let date = item.warningTime.slice(8, 10).toString()
          if (date[0] === '0') {
            date = date[1]
          }
          const datatime = item.warningTime.replace(/-/g, '/')
          const warntnum = item.wearWarning + item.ratWarning + item.actionWarning
          warningObj.warningsNumbox.push(warntnum)
          warningObj.warningsNum.push(warntnum)
          warningObj.popUpdatalist.push(datatime)
          warningObj.dateList.push(date + '日')
          warningObj.wearWarningList.push(item.wearWarning || 0)
          warningObj.ratWarningList.push(item.ratWarning || 0)
          warningObj.actionWarningList.push(item.actionWarning || 0)
        })
        this.aiWarningObj = warningObj
      })
    },
    // 点击随机抽查
    clickRandomSampling() {
      this.randomName = '全国随机项目点'
      const centerIds = this.markerList.map((item) => item.id)
      this.getRandomSampling(centerIds)
    },
    // 随机抽查
    getRandomSampling(ids, text) {
      this.liveArr = []
      this.monitorList = []
      randomSampling(ids).then((res) => {
        res.data.data.records.forEach((item, index) => {
          if (text === 'marker') {
            item.videoName = item.userName
          } else {
            item.videoName = item.centerName + item.userName
          }
        })
        this.monitorList = res.data.data.records.map((item) => {
          this.$set(item, 'loading', false)
          return item
        })
      })
    },
    // 坐标渲染
    renderMarker() {
      const map = this.map
      const icon1 = new AMap.Icon({
        size: new AMap.Size(48, 55), // 图标尺寸
        image: warningIcon, // Icon的图像
        imageOffset: new AMap.Pixel(0, 0), // 图像相对展示区域的偏移量，适于雪碧图等
        imageSize: new AMap.Size(48, 55), // 根据所设置的大小拉伸或压缩图片
      })
      const icon2 = new AMap.Icon({
        size: new AMap.Size(48, 55), // 图标尺寸
        image: greenIcon, // Icon的图像
        imageOffset: new AMap.Pixel(0, 0), // 图像相对展示区域的偏移量，适于雪碧图等
        imageSize: new AMap.Size(48, 55), // 根据所设置的大小拉伸或压缩图片
      })
      const icon3 = new AMap.Icon({
        size: new AMap.Size(48, 55), // 图标尺寸
        image: yfj, // Icon的图像
        imageOffset: new AMap.Pixel(0, 0), // 图像相对展示区域的偏移量，适于雪碧图等
        imageSize: new AMap.Size(48, 55), // 根据所设置的大小拉伸或压缩图片
      })
      if (this.delMarkerList && this.delMarkerList.length > 0) {
        map.remove(this.delMarkerList)
        this.delMarkerList = []
      }
      this.markerList.forEach((item) => {
        let marker
        if (item.isHoliday) {
          marker = new AMap.Marker({
            map: this.map,
            icon: icon3,
            position: [item.lng, item.lat],
            offset: new AMap.Pixel(-24, -27.5),
            centerName: item.centerName,
          })
        } else if (item.isWarning) {
          marker = new AMap.Marker({
            map: this.map,
            icon: icon1,
            position: [item.lng, item.lat],
            offset: new AMap.Pixel(-24, -27.5),
            centerName: item.centerName,
          })
          new AMap.Marker({
            map: this.map,
            content: "<div class='dot'></div>",
            position: [item.lng, item.lat],
            offset: new AMap.Pixel(-3, -5),
          })
        } else {
          marker = new AMap.Marker({
            map: this.map,
            icon: icon2,
            position: [item.lng, item.lat],
            offset: new AMap.Pixel(-24, -27.5),
            centerName: item.centerName,
          })
        }
        this.randomMarker = marker
        this.delMarkerList.push(marker)
        AMap.event.addListener(marker, 'click', (e) => {
          this.showInfo(marker, e.target.w.centerName, this.warningVideoUrl, this.warningShow)
        })
      })
    },
    // 地图弹窗
    showInfo(marker, centerName, warningVideoUrl) {
      const findCenter = this.markerList.find((item) => item.centerName === centerName)
      this.warningVideoUrl = warningVideoUrl
      this.centerName = centerName
      this.randomName = findCenter.centerName
      this.getRandomSampling([findCenter.id], 'marker')
      const content = `
          <div v-if="markerName" class="marker-info">
            <div class="center-name"><div title="${centerName}">${centerName}</div></div>
            <div id="center-chart"></div>
          </div>
       `
      // <div id="center-chart"></div>
      const infoWindow = new AMap.InfoWindow({
        isCustom: true, // 使用自定义窗体

        content: content,
        offset: new AMap.Pixel(0, -25),
      })
      infoWindow.open(this.map, marker.getPosition())
      setTimeout(() => {
        // eslint-disable-next-line no-undef
        initChart(findCenter.earlyWarningDTOS)
      }, 100)
      this.warningShow = false
      this.earlyWarningType = ''
    },
    startPlay(index) {
      const findVideo = this.monitorList[index]
      const obj = {
        device: findVideo.title, // 设备序列号
        channel: findVideo.content, // 	通道号
      }
      findVideo.loading = true
      startLive(obj)
        .then((res) => {
          this.monitorList[index].videoUrl = res.data.data.wsFlv
          this.monitorList = this.monitorList.splice(0)
        })
        .finally(() => {
          this.monitorList[index].loading = false
        })
    },
  },
}
window.initChart = (warningList) => {
  let typeList = []
  let handleList = []
  let unHandleList = []
  if (warningList.length > 0) {
    const type4 = warningList.findIndex((item) => {
      return item.type === 4
    })
    const type5 = warningList.findIndex((item) => {
      return item.type === 5
    })
    const type6 = warningList.findIndex((item) => {
      return item.type === 6
    })
    const type10 = warningList.findIndex((item) => {
      return item.type === 10
    })
    if (type4 === -1) {
      typeList.push('穿戴')
      handleList.push(0)
      unHandleList.push(0)
    } else {
      warningList.forEach((item) => {
        if (item.type === 4) {
          typeList.push('穿戴')
          handleList.push(item.handle)
          unHandleList.push(item.unHandle)
        }
      })
    }
    if (type5 === -1) {
      typeList.push('行为')
      handleList.push(0)
      unHandleList.push(0)
    } else {
      warningList.forEach((item) => {
        if (item.type === 5) {
          typeList.push('行为')
          handleList.push(item.handle)
          unHandleList.push(item.unHandle)
        }
      })
    }
    if (type6 === -1) {
      typeList.push('掉线')
      handleList.push(0)
      unHandleList.push(0)
    } else {
      warningList.forEach((item) => {
        if (item.type === 6) {
          typeList.push('掉线')
          handleList.push(item.handle)
          unHandleList.push(item.unHandle)
        }
      })
    }
    if (type10 === -1) {
      typeList.push('异常')
      handleList.push(0)
      unHandleList.push(0)
    } else {
      warningList.forEach((item) => {
        if (item.type === 10) {
          typeList.push('异常')
          handleList.push(item.handle)
          unHandleList.push(item.unHandle)
        }
      })
    }
  } else {
    typeList = ['穿戴', '行为', '掉线', '异常']
    handleList = [0, 0, 0, 0]
    unHandleList = [0, 0, 0, 0]
  }
  const chart = echarts.init(document.getElementById('center-chart'))
  chart.setOption({
    legend: {
      right: 0,
      itemWidth: 10,
      itemHeight: 5,
      textStyle: {
        color: '#98BCEC',
      },
    },
    grid: {
      top: '20%',
      bottom: '18%',
      left: '10%',
      right: '3%',
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#143E91',
        },
      },
      axisLabel: {
        color: '#A6BAE0',
        interval: 0,
      },
      data: typeList,
    },
    yAxis: {
      type: 'value',
      name: '单位:(件)',
      nameTextStyle: {
        color: '#9AACD1',
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: '#143E91',
        },
      },
      axisLabel: {
        color: '#A6BAE0',
      },
    },
    series: [
      {
        name: '已处理',
        type: 'bar',
        barWidth: '8',
        itemStyle: {
          color: '#18C4B4',
          barBorderRadius: [5, 5, 0, 0],
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
        },
        data: handleList,
      },
      {
        name: '未处理',
        type: 'bar',
        barWidth: '8',
        itemStyle: {
          color: '#B83A3A',
          barBorderRadius: [5, 5, 0, 0],
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
        },
        data: unHandleList,
      },
    ],
  })
}
</script>

<style lang="scss">
@keyframes scaleDraw {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1);
  }
  100% {
    display: none;
  }
}
.warning-main {
  width: 1.75rem;
  height: 1rem;
}
.close {
  width: 0.15rem;
  height: 0.15rem;
  position: absolute;
  top: 0.2rem;
  right: 0.05rem;
  border: none;
  cursor: pointer;
}
.marker-info {
  width: 1.84rem;
  background: url('../../assets/img/marker-bg.png') no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0.05rem;
  .center-name {
    width: 1.24rem;
    height: 0.2rem;
    line-height: 0.2rem;
    box-sizing: border-box;
    padding-bottom: 0.01rem;
    background-image: url('../../assets/img/marker-title.png');
    background-size: 100% 100%;
    text-align: center;
    margin: 0 auto;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    > div {
      width: 0.65rem;
      height: 0.2rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  #center-chart {
    width: 100%;
    height: 1rem;
  }
}
.video-main {
  .video-wrapper .video-js .vjs-control-bar {
    font-size: 10px !important;
  }
}
.player-box {
  .video-wrapper .video-js .vjs-control-bar {
    font-size: 10px !important;
  }
  .vjs-big-play-button {
    display: none !important;
  }
}
</style>

<style lang="scss" scoped>
@import url('../../assets/css/index.css');
@import './index.scss';
</style>
