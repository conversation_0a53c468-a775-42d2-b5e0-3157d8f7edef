import AMap from 'AMap'
import { dayi<PERSON>son } from '@/assets/dayiJson'

export default {
  data() {
    return {
      readingMode: false,
      animationIndex: 0,
      mapMoveTimer: null,
      mapAnimationMoveTimer: null,
      mapCenter: [117.066055, 35.512714]

    }
  },
  beforeDestroy() {
    clearInterval(this.mapMoveTimer)
    clearInterval(this.mapAnimationMoveTimer)
    this.mapMoveTimer = null
    this.mapAnimationMoveTimer = null
  },
  methods: {
    // 巡阅模式切换
    toggleReaddingMode() {
      this.animationIndex = 0
      if (!this.readingMode) {
        this.readingMode = true
        this.map.setZoom(11.5)
        this.map.panBy(-300, 0)
        this.mapMoveTimer = setInterval(() => {
          this.animationIndex = !this.animationIndex
          if (this.animationIndex) {
            this.map.panBy(500, 0)
          } else {
            this.map.panBy(-500, 0)
          }
        }, 5000)
      } else {
        this.map.panTo(new AMap.LngLat(this.mapCenter[0], this.mapCenter[1]))
        this.readingMode = false
        this.map.setZoom(11)
        clearInterval(this.mapMoveTimer)
        clearInterval(this.mapAnimationMoveTimer)
      }
    },
    // 鼠标悬停停止swiper
    stopSwiper(name) {
      this.$refs[name].$swiper.autoplay.stop()
    },
    startSwiper(name) {
      this.$refs[name].$swiper.autoplay.start()
    },
    // 图片预览
    showPreviewImage(url) {
      console.log(666666666)
      this.$refs.previewImage.showViewer = true
      this.previewImageUrlList = [url]
    },
    // 添加地图街道名称
    drawStreetsName(AMap, map) {
      const streetList = [{
        name: '怡心',
        point: [104.03279, 30.492716]
      },
      {
        name: '西航港',
        point: [104.01264, 30.553432]
      },
      {
        name: '黄甲',
        point: [103.976194, 30.499726]
      },
      {
        name: '永安',
        point: [103.991142, 30.410067]
      },
      {
        name: '黄龙溪',
        point: [103.946438, 30.36881]
      },
      {
        name: '黄水',
        point: [103.886978, 30.527603]
      },
      {
        name: '彭镇',
        point: [103.855584, 30.592285]
      },
      {
        name: '东升',
        point: [103.936072, 30.581291]
      },
      {
        name: '九江',
        point: [103.9194, 30.639029]
      }
      ]
      for (const item of streetList) {
        // console.log(item.point[0], item.point[1])
        const street = new AMap.Text({
          text: item.name,
          anchor: 'center', // 设置文本标记锚点
          cursor: 'pointer',
          position: [item.point[0], item.point[1]],
          style: {
            'fontSize': '14px',
            'background': 'transparent',
            'border': 'none',
            'color': '#D1FDFF',
            'textShadow': '0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;'
          }
        })
        street.setMap(map)
      }
    },
    // 添加地图控件
    addControl(AMap, map, bottom) {
      AMap.plugin(['AMap.ControlBar'], () => {
        map.addControl(
          new AMap.ControlBar({
            position: {
              bottom: bottom || '-0.2rem',
              left: '2.3rem',
              zIndex: 9,
              transition: '1.5s'
            }
          })
        )
      })
    },
    // 添加geoJson
    addGeoJson(map) {
      dayiJson.features.forEach((item) => {
        const lngLats = item.geometry.coordinates
        /** 绘制区块*/
        const polygon = new AMap.Polygon({
          map: this.map,
          path: lngLats,
          fillOpacity: 0, // 面积越大透明度越高
          strokeColor: 'white',
          fillColor: '#21edea',
          strokeOpacity: 0
        })
        // 给Polygon添加选中效果
        polygon.on('mouseover', function(e) {
          polygon.setOptions({
            fillOpacity: 0.5,
          })
        })
        // 离开后取消选中效果
        polygon.on('mouseout', function(e) {
          polygon.setOptions({
            fillOpacity: 0,
          })
        })

        /** 绘制区域名称*/
        const name = item.properties.name
        const centroid = item.properties.centroid

        new AMap.Text({
          map: map,
          text: name,
          anchor: 'center', // 设置文本标记锚点
          cursor: 'pointer',
          position: centroid,
          style: {
            'fontSize': '14px',
            'background': 'transparent',
            'border': 'none',
            'color': '#D1FDFF',
            'textShadow': '0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;'
          }
        })
      })
    }
  }
}
