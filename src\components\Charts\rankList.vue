<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import resize from './mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
export default {
  name: 'GapCharts',
  mixins: [resize, componentMixin],
  props: {
    id: {
      require: false,
      type: String,
      default: 'GapCharts'
    },
    width: {
      require: false,
      type: String,
      default: '2rem'
    },
    height: {
      require: false,
      type: String,
      default: '2rem'
    },
    propData: {
      require: false,
      type: Array,
      default: () => [
        {
          name: 'Mon',
          value: 0
        },
        {
          name: '<PERSON><PERSON>',
          value: 0
        },
        {
          name: 'Wed',
          value: 0
        }
      ]
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      const data = [{
        'name': '棠湖中学',
        'fundPost': '21.987691'
      },
      {
        'name': '棠湖中学',
        'fundPost': '20.377176'
      },
      {
        'name': '棠湖中学',
        'fundPost': '19.127404'
      },
      {
        'name': '棠湖中学',
        'fundPost': '18.40882'
      },
      {
        'name': '棠湖中学',
        'fundPost': '17.980597'
      },
      {
        'name': '棠湖中学',
        'fundPost': '16.957898'
      },
      {
        'name': '棠湖中学',
        'fundPost': '15.099577'
      },
      {
        'name': '棠湖中学',
        'fundPost': '14.76103'
      },
      {
        'name': '棠湖中学',
        'fundPost': '13.002175'
      },
      {
        'name': '棠湖中学',
        'fundPost': '12.133536'
      }
      ]

      function contains(arr, dst) {
        var i = arr.length
        while ((i -= 1)) {
          if (arr[i] === dst) {
            return i
          }
        }
        return false
      }

      var attackSourcesColor = [
        new echarts.graphic.LinearGradient(0, 1, 1, 1, [{
          offset: 0,
          color: '#EB3B5A'
        },
        {
          offset: 1,
          color: '#FE9C5A'
        }
        ]),
        new echarts.graphic.LinearGradient(0, 1, 1, 1, [{
          offset: 0,
          color: '#FA8231'
        },
        {
          offset: 1,
          color: '#FFD14C'
        }
        ]),
        new echarts.graphic.LinearGradient(0, 1, 1, 1, [{
          offset: 0,
          color: '#F7B731'
        },
        {
          offset: 1,
          color: '#FFEE96'
        }
        ]),
        new echarts.graphic.LinearGradient(0, 1, 1, 1, [{
          offset: 0,
          color: '#395CFE'
        },
        {
          offset: 1,
          color: '#2EC7CF'
        }
        ])
      ]
      var attackSourcesColor1 = [
        '#EB3B5A',
        '#FA8231',
        '#F7B731',
        '#3860FC',
        '#1089E7',
        '#F57474',
        '#56D0E3',
        '#1089E7',
        '#F57474',
        '#1089E7',
        '#F57474',
        '#F57474'
      ]
      var attaData = []
      var attaName = []
      var topName = []
      data.forEach((it, index) => {
        attaData[index] = parseFloat(it.fundPost).toFixed(2)
        attaName[index] = index + 1
        topName[index] = it.name
      })
      var salvProMax = [] // 背景按最大值
      for (let i = 0; i < attaData.length; i++) {
        salvProMax.push(attaData[0])
      }

      function attackSourcesDataFmt(sData) {
        var sss = []
        sData.forEach(function(item, i) {
          const itemStyle = {
            color: i > 3 ? attackSourcesColor[3] : attackSourcesColor[i]
          }
          sss.push({
            value: item,
            itemStyle: itemStyle
          })
        })
        return sss
      }
      this.option = {
        animationDuration: 3000,

        tooltip: {
          show: false,
          backgroundColor: 'rgba(3,169,244, 0.5)', // 背景颜色（此时为默认色）
          textStyle: {
            fontSize: 16
          }
        },
        color: ['#F7B731'],
        grid: {
          left: '5%',
          right: '2%',
          bottom: '2%',
          top: '8%',
          containLabel: true
        },
        xAxis: {
          show: false
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisPointer: {
              label: {
                show: true
                // margin: 30
              }
            },
            pdaaing: [5, 0, 0, 0],
            postion: 'right',
            data: attaName,
            axisLabel: {
              margin: 30,
              fontSize: 14,
              align: 'left',
              padding: [2, 0, 0, 0],
              color: '#000',
              rich: {
                nt1: {
                  color: '#fff',
                  backgroundColor: attackSourcesColor1[0],
                  width: 13,
                  height: 13,
                  fontSize: 14,
                  align: 'center',
                  borderRadius: 100,
                  lineHeight: '5',
                  padding: [0, 1, 2, 1]
                  // padding:[0,0,2,0],
                },
                nt2: {
                  color: '#fff',
                  backgroundColor: attackSourcesColor1[1],
                  width: 13,
                  height: 13,
                  fontSize: 14,
                  align: 'center',
                  borderRadius: 100,
                  padding: [0, 1, 2, 1]
                },
                nt3: {
                  color: '#fff',
                  backgroundColor: attackSourcesColor1[2],
                  width: 13,
                  height: 13,
                  fontSize: 14,
                  align: 'center',
                  borderRadius: 100,
                  padding: [0, 1, 2, 1]
                },
                nt: {
                  color: '#fff',
                  backgroundColor: attackSourcesColor1[3],
                  width: 13,
                  height: 13,
                  fontSize: 14,
                  align: 'center',
                  lineHeight: 3,
                  borderRadius: 100,
                  padding: [0, 1, 2, 1]
                }
              },
              formatter: function(value, index) {
                index = contains(attaName, value) + 1
                if (index - 1 < 3) {
                  return ['{nt' + index + '|' + index + '}'].join('\n')
                } else {
                  return ['{nt|' + index + '}'].join('\n')
                }
              }
            }
          },
          {
            // 名称
            type: 'category',
            offset: -10,
            position: 'left',
            axisLine: {
              show: false
            },
            inverse: false,
            axisTick: {
              show: false
            },
            axisLabel: {
              interval: 0,
              color: ['#fff'],
              align: 'left',
              verticalAlign: 'bottom',
              lineHeight: 32,
              fontSize: 14
            },
            data: topName
          }
        ],
        series: [
          {
            zlevel: 1,
            type: 'bar',
            barWidth: '15px',
            animationDuration: 1500,
            data: attackSourcesDataFmt(attaData),
            align: 'center',
            itemStyle: {
              normal: {
                barBorderRadius: 10
              }
            },
            label: {
              show: true,
              fontSize: 14,
              color: '#fff',
              textBorderWidth: 2,
              padding: [2, 0, 0, 0]
            }
          },
          {
            type: 'bar',
            barWidth: 15,
            barGap: '-100%',
            margin: '20',
            data: salvProMax,
            textStyle: {
              // 图例文字的样式
              fontSize: 14,
              color: '#fff'
            },
            itemStyle: {
              normal: {
                color: '#05325F',
                // width:"100%",
                fontSize: 14,
                barBorderRadius: 30
              }
            }
          }
        ]
      }
      this.chart.setOption(this.option)
      this.autoPlayTool(this.chart, data, 0)
    }
  }
}
</script>
