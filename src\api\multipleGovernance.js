import request from '@/utils/request'
// 查询膳食结构分析
// export function getRose(data) {
//   return request({
//     url: '/api/v1/web/nutritional/meals/dietaryStructure',
//     method: 'post',
//     data
//   })
// }
export function getRoseData(data) {
  return request({
    url: '/api/v1/web/shuangliu/bigdata/dietaryCountriesStructure',
    method: 'post',
    data
  })
}
// 投诉
export function getGap(data) {
  return request({
    url: '/api/v1/web/shuangliu/bigdata/complainStat',
    method: 'post',
    data
  })
}
// 投诉与建议
export function getStatistics(data) {
  return request({
    url: '/api/v1/web/shuangliu/bigdata/complaintAndSuggest',
    method: 'post',
    data
  })
}
// 词云
export function getWords(data) {
  return request({
    url: '/api/v1/web/bigData/wordCloud',
    method: 'post',
    data
  })
}

// 词云
export function getmyCanlendar(data) {
  return request({
    url: '/api/v1/web/shuangliu/bigdata/allAccompany',
    method: 'post',
    data
  })
}

//
export function getSameMonth(data) {
  return request({
    url: '/api/v1/web/shuangliu/bigdata/accompanyCurrent',
    method: 'post',
    data
  })
}

export function getHotMap(data) {
  return request({
    url: '/api/v1/web/shuangliu/bigdata/complainHandle',
    method: 'post',
    data
  })
}

export function getMonthAccompany(code) {
  return request({
    url: '/api/v1/web/shuangliu/bigdata/monthAccompany?code=' + code,
    method: 'get'
  })
}

export function getReplySatisfaction(code) {
  return request({
    url: '/api/v1/web/shuangliu/bigdata/replySatisfaction?code=' + code,
    method: 'get'
  })
}
