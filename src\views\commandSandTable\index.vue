<template>
  <div id="main">
    <img src="@/assets/img/turn-off.svg" class="logoutBtn" @click="logout" />
    <img style="position: absolute;left: 50%;transform: translateX(-50%)" src="../../assets/video/bg-text.png" />
    <video src="@/assets/video/bg.mp4" autoplay loop muted />
    <!-- <div class="headers" /> -->
    <!-- <div class="main-contents">
      <div class="content-div"> -->
    <div class="one" @click="checkNavigation('数智食安','/wisdomFoodSafety')">
      <div>
        <div>数智食安</div>
        <div>Smart Food Safety</div>
      </div>
      <img src="../../assets/img/commandSandTable/<EMAIL>" alt="">
    </div>
    <div class="two" @click="checkNavigation('智慧监测','/smartMonitor')">
      <div>
        <div>智慧监测</div>
        <div>Smart monitoring</div>
      </div>
      <img src="../../assets/img/commandSandTable/<EMAIL>" alt="">
    </div>
    <div class="three" @click="checkNavigation('明厨亮灶','/brightKitchen')">
      <div>
        <div>明厨亮灶</div>
        <div>Bright kitchen</div>
      </div>
      <img src="../../assets/img/commandSandTable/<EMAIL>" alt="">
    </div>
    <div class="four" @click="checkNavigation('主体监管','/subjectSupervision')">
      <img src="../../assets/img/commandSandTable/<EMAIL>" alt="">
      <div>
        <div>主体监管</div>
        <div>Subject supervision</div>
      </div>
    </div>
    <div class="five" @click="checkNavigation('多元共治','/multipleGovernance')">
      <img src="../../assets/img/commandSandTable/<EMAIL>" alt="">
      <div>
        <div>多元共治</div>
        <div>Multiple governance</div>
      </div>
    </div>
    <div class="six" @click="checkNavigation('全域总览','/globalOverview')">
      <img src="../../assets/img/commandSandTable/<EMAIL>" alt="">
      <div>
        <div>全域总览</div>
        <div>Global overview</div>

      </div>
    </div>
    <!-- </div>
    </div> -->
  </div>
</template>

<script>
import AMap from 'AMap'
import AMapUI from 'AMapUI'
import { getCode } from '@/utils/index'
export default {
  components: {
  },
  methods: {
    checkNavigation(text, router) {
      localStorage.router = JSON.stringify({ value: router, name: text })
      this.$router.push(router)
    },
    logout() {
      localStorage.clear()
      sessionStorage.clear()
      this.$router.push('/login')
    }
  }
}
</script>

<style lang="scss">
</style>

<style lang="scss" scoped>
@import url('../../assets/css/index.css');
@import './index.scss';

.logoutBtn {
  position: absolute;
  top: 15px;
  right: 30px;
  width: 35px;
  height: 35px;
  z-index: 999;
  cursor: pointer;
}
</style>

