#main {
  overflow: hidden !important;
  width: 100%;
  height: 100vh;
  // background-image: url('../../assets/img/commandSandTable/<EMAIL>');
  // background-size: 100% 100%;
  position: relative;

  >div {
    position: absolute;
    display: flex;
    align-items: center;
    height: 0.3rem;
    cursor: pointer;

    >div {
      height: 0.25rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-width: 0.65rem;

      >div:nth-of-type(1) {
        font-size: 0.14rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: #E7F1FF;
      }

      >div:nth-of-type(2) {
        font-size: 0.06rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: #5D6F8E;
      }
    }

    img {
      width: 0.3rem;
      height: 0.3rem;
    }
  }

  >div:hover {
    transform: scale(1.2);
  }

  .one {
    top: 34.5%;
    left: 2.1rem;
    text-align: right;
  }

  .two {
    top: 47.5%;
    left: 2.1rem;
    text-align: right;
  }

  .three {
    top: 60.5%;
    left: 2.1rem;
    text-align: right;
  }

  .four {
    top: 34.5%;
    right: 2.1rem;
    text-align: left;
  }

  .five {
    top: 47.5%;
    right: 2.1rem;
    text-align: left;
  }

  .six {
    top: 60.5%;
    right: 2.1rem;
    text-align: left;
  }
}

video {
  width: 100%;
  height: 100vh;
  object-fit: fill;
}

.headers {
  height: 0.46rem;
  width: 100%;
  background-image: url('../../assets/img/commandSandTable/command-header.png');
  background-size: 100% 100%;
}

.main-contents {
  position: absolute;
  top: 20%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;

  .content-div {
    position: relative;
    width: 6rem;
    height: 3rem;

    >div {
      position: absolute;
      display: flex;
      align-items: center;
      height: 0.3rem;
      cursor: pointer;

      >div {
        height: 0.25rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        min-width: 0.65rem;

        >div:nth-of-type(1) {
          font-size: 0.14rem;
          font-family: PingFang SC;
          font-weight: bold;
          color: #E7F1FF;
        }

        >div:nth-of-type(2) {
          font-size: 0.06rem;
          font-family: PingFang SC;
          font-weight: bold;
          color: #5D6F8E;
        }
      }

      img {
        width: 0.3rem;
        height: 0.3rem;
      }
    }

    >div:hover {
      transform: scale(1.2);
    }

    .one {
      top: 22%;
      left: 0.3rem;
      margin-left: 0.05rem;
      text-align: right;
    }

    .two {
      top: 43%;
      left: 0.3rem;
      margin-left: 0.05rem;
      text-align: right;
    }

    .three {
      top: 64%;
      left: 0.3rem;
      margin-left: 0.05rem;
      text-align: right;
    }

    .four {
      top: 22%;
      right: 0.3rem;
      margin-right: 0.05rem;
      text-align: left;
    }

    .five {
      top: 43%;
      right: 0.3rem;
      margin-right: 0.05rem;
      text-align: left;
    }

    .six {
      top: 64%;
      right: 0.3rem;
      margin-right: 0.05rem;
      text-align: left;
    }
  }
}