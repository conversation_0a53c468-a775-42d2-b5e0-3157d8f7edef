<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import 'echarts-liquidfill'
import chartsMixIn from './mixins'
export default {
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    propData: {
      require: false,
      type: Number,
      default: () => { return 0 }
    },
    colors: {
      require: true,
      type: Array,
      default: () => { return [] }
    },
    title: {
      require: false,
      type: String,
      default: '事件汇总'
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.option = {
        animationDuration: 3000,

        series: [{
          radius: '85%',
          center: ['50%', '50%'],
          type: 'liquidFill',
          data: [
            {
              value: 0.6,
              itemStyle: {
                color: this.colors[1]
              }
            },
            {
              value: 0.5,
              direction: 'left',
              itemStyle: {
                color: this.colors[1]
              }
            }],
          waveAnimation: 5,
          label: {
            normal: {
              textStyle: {
                color: '#fff',
                insideColor: '#fff',
                fontSize: 14,
                fontWeight: 400,
                lineHeight: 20
              },
              formatter: (param) => {
                return this.title + '\n' + this.propData + '件'
              }
            }
          },
          backgroundStyle: {
            // shadowBlur: 10,
            // shadowColor: '#30829c',
            color: 'rgba(0,0,0,0.2)'
          },
          outline: {
            borderDistance: 0,
            itemStyle: {
              borderWidth: 2,
              borderColor: this.colors[0],
              shadowBlur: 10,
              shadowColor: this.colors[0]
            }
          }
        }]
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
