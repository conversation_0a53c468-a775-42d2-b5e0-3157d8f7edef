<template>
  <!-- 上级页面部分弹框 -->
  <div v-if="showBox1 || showBox2" class="tree-popup">
    <div v-if="showBox1" v-loading="popupLoading" class="box1">
      <img src="../../../assets/img/<EMAIL>" class="close" alt @click="showBox1=false">
      <div class="left">
        <div v-for="(item,index) in boxOneData.mongoList" :key="index" class="left-item">
          <div class="item-box">
            <div class="name">{{ item.ingredientsName }}</div>
            <div class="type-name">食材名称</div>
          </div>
          <div class="line">
            <div class="state">
              <img :src="item.sourceName.length>0?gou:cha" alt>
              产地
            </div>
            <div>
              <img src="../../../assets/img/wisdomFoodSafety/line@2x(2).png" alt>
            </div>
          </div>
          <div class="item-box">
            <el-image class="image" :preview-src-list="item.quarantineImage.length>0?[item.quarantineImage]:[]" :src="item.quarantineImage.length>0?item.quarantineImage:noImg1" alt />
            <div class="type-name">
              <!-- <span>产地：</span> -->
              <span>{{ item.sourceName || '暂无产地数据' }}</span>
            </div>
          </div>
          <div class="line">
            <div class="state">
              <img :src="item.supplierName.length>0?gou:cha" alt>
              供应商
            </div>
            <div>
              <img src="../../../assets/img/wisdomFoodSafety/line@2x(2).png" alt>
            </div>
          </div>
          <div class="item-box">
            <el-image class="image" :preview-src-list="item.supplierImage.length>0?[item.supplierImage]:[]" :src="item.supplierImage.length>0?item.supplierImage:noImg1" alt />
            <div class="type-name">
              <!-- <span>供应商：</span> -->
              <span>{{ item.supplierName || '暂无数据' }}</span>
            </div>
          </div>
          <div class="line">
            <div class="state">
              <img :src="item.inDate.length>0?gou:cha" alt>
              入库
            </div>
            <div>
              <img src="../../../assets/img/wisdomFoodSafety/line@2x(2).png" alt>
            </div>
          </div>
          <div class="item-box">
            <el-image class="image" :preview-src-list="item.inImage.length>0?[item.inImage]:[]" :src="item.inImage.length>0?item.inImage:noImg1" alt />
            <div class="type-name">
              <!-- <span>入库时间：</span> -->
              <span>{{ item.inDate || '暂无数据' }}</span>
            </div>
          </div>
          <div class="line">
            <div class="state">
              <img :src="item.outDate.length>0?gou:cha" alt>
              出库
            </div>
            <div>
              <img src="../../../assets/img/wisdomFoodSafety/line@2x(2).png" alt>
            </div>
          </div>
          <div class="item-box">
            <el-image class="image" :preview-src-list="item.outImage.length>0?[item.outImage]:[]" :src="item.outImage.length>0?item.outImage:noImg1" alt />
            <div class="type-name">
              <!-- <span>出库时间：</span> -->
              <span>{{ item.outDate || '暂无数据' }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <img class="left-image" src="../../../assets/img/wisdomFoodSafety/lianjie.png" alt>
        <div class="finished">
          <img :src="boxOneData.imageUrl.length>0?gou:cha" alt>
          成品
        </div>
        <div class="item-box">
          <el-image class="image" :preview-src-list="boxOneData.imageUrl.length>0?[boxOneData.imageUrl]:[]" :src="boxOneData.imageUrl.length>0?boxOneData.imageUrl:noImg1" alt />
          <div class="type-name">
            <!-- <span>成品：</span> -->
            <span>{{ boxOneData.imageDate }}</span>
          </div>
        </div>
        <div class="line">
          <div class="state">
            <img :src="boxOneData.retentionImageUrl.length>0?gou:cha" alt>
            留样
          </div>
          <div>
            <img src="../../../assets/img/wisdomFoodSafety/line@2x(2).png" alt>
          </div>
        </div>
        <div class="item-box">
          <el-image class="image" :preview-src-list="boxOneData.retentionImageUrl.length>0?[boxOneData.retentionImageUrl]:[]" :src="boxOneData.retentionImageUrl.length>0?boxOneData.retentionImageUrl:noImg1" alt />
          <div class="type-name">
            <!-- <span>留样：</span> -->
            <span>{{ boxOneData.retentionDate }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showBox2" v-loading="popupLoading" class="box2">
      <img src="../../../assets/img/<EMAIL>" class="close" alt @click="showBox2=false">
      <div class="title">供应商信息</div>
      <div class="box2-content">
        <img class="left" :src="(boxTwoData.supplierQualifications.length>0 && boxTwoData.supplierQualifications[0].image.length>0)?boxTwoData.supplierQualifications[0].image:noImg2" alt @click="showPreviewImage(boxTwoData.supplierQualifications[0].image)">
        <div class="right">
          <div class="item">
            <div class="label">供应商名</div>
            <div class="info">{{ boxTwoData.name }}</div>
          </div>
          <div class="item">
            <div class="label">负责人</div>
            <div class="info">{{ boxTwoData.principalName }}</div>
          </div>
          <div class="item">
            <div class="label">联系电话</div>
            <div class="info">{{ boxTwoData.telephone }}</div>
          </div>
          <div class="item">
            <div class="label">证件有效期</div>
            <div class="info">{{ boxTwoData.expiredDate }}</div>
          </div>
        </div>
      </div>
    </div>
    <preview-image ref="previewImage" :url-list="previewImageUrlList" />
  </div>
</template>

<script>
import viewMixins from '@/views/mixins'
import { selectTraceabilityV2, getSupplierDetails } from '@/api/wisdomFoodSafety'
import PreviewImage from '@/components/PreviewImage'

export default {
  components: {
    PreviewImage
  },
  mixins: [viewMixins],
  props: {
    source: {
      require: false,
      type: Object,
      default: () => {}
    },
    suplier: {
      require: false,
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showBox1: false,
      showBox2: false,
      popupLoading: false,
      previewImageUrlList: [],
      noImg1: require('../../../assets/img/wisdomFoodSafety/<EMAIL>'),
      noImg2: require('../../../assets/img/wisdomFoodSafety/<EMAIL>'),
      gou: require('../../../assets/img/wisdomFoodSafety/gou@2x(1).png'),
      cha: require('../../../assets/img/wisdomFoodSafety/cha@2x(2).png'),
      boxOneData: {},
      boxTwoData: {
        supplierQualifications: []
      }
      // boxOneData: undefined
    }
  },
  watch: {
    source: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (newValue) {
          this.getSelectTraceabilityV2(Number(newValue.bid), newValue.dishForDay)
          // this.getSelectTraceabilityV2(11, '2021-01-11')
        }
      },
      deep: true // true 深度监听
    },
    suplier: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (newValue) {
          this.getSupplierDetails(newValue)
        }
      },
      deep: true // true 深度监听
    }
  },
  methods: {
    // 获取食材溯源
    async getSelectTraceabilityV2(bid, dishForDay) {
      this.popupLoading = true
      const params = {
        bid,
        dishForDay
      }
      try {
        const res = await selectTraceabilityV2(params)
        this.boxOneData = res.data.data
      } finally {
        this.popupLoading = false
      }
    },
    // 获取供应商详情
    async getSupplierDetails(id) {
      this.popupLoading = true
      try {
        const res = await getSupplierDetails({ id })
        console.log(res, 7776)
        this.boxTwoData = res.data.data
      } finally {
        this.popupLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.hide {
  pointer-events: none;
}
.tree-popup {
  z-index: 666;
  width: 8rem;
  height: 3.9rem;
  position: absolute;
  transform: translateX(-50%) translateY(-50%);
  top: 50%;
  left: 50%;
  .box1 {
    width: 100%;
    height: 100%;
    background: url('../../../assets/img/<EMAIL>');
    background-size: 100% 100%;
    padding: 0.25rem;
    box-sizing: border-box;
    display: flex;
    .left {
      height: 100%;
      overflow-y: scroll;
      .left-item {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 5.05rem;
        height: 1.12rem;
      }
      .item-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        .name {
          width: 0.55rem;
          height: 0.55rem;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #096fed;
          box-shadow: inset 0 0 30px #164a9d90;
        }
        .type-name {
          margin-top: 0.03rem;
          font-size: 0.07rem;
          color: #baceeb;
        }
      }
      .line {
        position: relative;
        top: -0.1rem;
      }
      .image {
        object-fit: cover;
        width: 0.55rem;
        height: 0.55rem;
        border-radius: 50%;
      }
      .state {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .right {
      display: flex;
      align-items: center;
      position: relative;
      .left-image {
        position: relative;
        top: -0.08rem;
      }
      .finished {
        display: flex;
        align-items: center;
        position: absolute;
        top: 1.475rem;
        left: 0.225rem;
      }
      .item-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        .name {
          width: 0.55rem;
          height: 0.55rem;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #096fed;
          box-shadow: inset 0 0 30px #164a9d90;
        }
        .type-name {
          margin-top: 0.03rem;

          color: #baceeb;
        }
      }
      .line {
        position: relative;
        top: -0.1rem;
      }
      .image {
        object-fit: cover;
        width: 0.55rem;
        height: 0.55rem;
        border-radius: 50%;
      }
      .state {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .box2 {
    position: absolute;
    transform: translateX(-50%) translateY(-50%);
    top: 50%;
    left: 50%;
    width: 5.1rem;
    height: 2.8rem;
    background: url('../../../assets/img/<EMAIL>');
    background-size: 100% 100%;
    padding: 0.25rem;
    box-sizing: border-box;
    .title {
      width: 100%;
      font-size: 0.16rem;
      color: #13e5f9;
      text-align: center;
      margin-bottom: 0.32rem;
    }
    .box2-content {
      display: flex;
      .left{
        width: 1.9rem;
        height: 1.7rem;
        object-fit: cover;
      }
      .right {
        flex:1;
        margin-left: 0.35rem;
        .item {
          font-size: 0.12rem;
          margin-bottom: 0.12rem;
          .label {
            // float: left;
            white-space: nowrap;
            width: 115px !important;
            color: #1fc8ff;
            // margin-right: 0.15rem;
          }
          .info {
            color: #abc9ff;
          }
        }
      }
    }
  }
}
::-webkit-scrollbar {
  display: none;
}
.close {
  width: 0.3rem;
  height: 0.3rem;
  cursor: pointer;
  position: absolute;
  top: 0.2rem;
  right: 0.2rem;
}
</style>
<style lang="css">
.el-image-viewer__wrapper{
  margin-top: 0 !important;
  height: 100% !important;
}
</style>
