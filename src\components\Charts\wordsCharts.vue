<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>

<script>
import echarts from 'echarts'
import resize from './mixins'
export default {
  name: 'WordsCharts',
  mixins: [resize],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '2rem'
    },
    height: {
      require: false,
      type: String,
      default: '2rem'
    },
    propData: {
      require: false,
      type: Array,
      default: () => { }
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.option = {
        animationDuration: 3000,
        // 'backgroundColor': '#031739',
        'tooltip': {
          'show': true,
          'textStyle': {
            'fontSize': '16',
            'color': '#3c3c3c'
          },
          'transitionDuration': 0,
          'backgroundColor': '#fff',
          'borderColor': '#ddd',
          'borderWidth': 1
        },
        'series': [{
          // 'name': '积分排行',
          'type': 'wordCloud',
          'gridSize': 20,
          'sizeRange': [12, 30],
          'rotationRange': [0, 0],
          'shape': 'circle',
          'autoSize': {
            'enable': true,
            'minSize': 18
          },
          'data': this.propData
        }]
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
