<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  name: 'EventAnalysisCharts',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    proportion: {
      require: false,
      type: Number,
      default: () => 0
    },
    bgColor: {
      require: true,
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      timer: null,
      option: {},
      angle: 0
    }
  },
  watch: {
    proportion: {
      handler(newValue, oldVal) {
        this.setOption()
        this.draw()
      },
      // deep: true // true 深度监听
    }
  },
  created() {
    // 无需重复动画，不重绘制
    this.doNotRedraw = true
  },
  mounted() {
    clearInterval(this.timer)
    setTimeout(() => {
      this.initChart()
      this.timer = setInterval(() => {
        this.draw()
      }, 100)
    }, 500)
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
    clearInterval(this.timer)
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.setOption()
    },
    setOption() {
      this.angle = 0// 角度，用来做简单的动画效果的
      this.option = {
        title: {
          text: '{a|' + this.proportion + '}{c|%}',
          x: 'center',
          y: 'center',
          textStyle: {
            rich: {
              a: {
                fontSize: 18,
                color: '#29EEF3'
              },

              c: {
                fontSize: 16,
                color: '#ffffff'
                // padding: [5,0]
              }
            }
          }
        },
        series: [{
          name: 'ring5',
          type: 'custom',
          coordinateSystem: 'none',
          renderItem: (params, api) => {
            return {
              type: 'arc',
              shape: {
                cx: api.getWidth() / 2,
                cy: api.getHeight() / 2,
                r: Math.min(api.getWidth(), api.getHeight()) * 0.45,
                startAngle: (0 + this.angle) * Math.PI / 180,
                endAngle: (90 + this.angle) * Math.PI / 180
              },
              style: {
                stroke: '#0CD3DB',
                fill: 'transparent',
                lineWidth: 1.5
              },
              silent: true
            }
          },
          data: [0]
        }, {
          name: 'ring5',
          type: 'custom',
          coordinateSystem: 'none',
          renderItem: (params, api) => {
            return {
              type: 'arc',
              shape: {
                cx: api.getWidth() / 2,
                cy: api.getHeight() / 2,
                r: Math.min(api.getWidth(), api.getHeight()) * 0.45,
                startAngle: (180 + this.angle) * Math.PI / 180,
                endAngle: (270 + this.angle) * Math.PI / 180
              },
              style: {
                stroke: '#0CD3DB',
                fill: 'transparent',
                lineWidth: 1.5
              },
              silent: true
            }
          },
          data: [0]
        }, {
          name: 'ring5',
          type: 'custom',
          coordinateSystem: 'none',
          renderItem: (params, api) => {
            return {
              type: 'arc',
              shape: {
                cx: api.getWidth() / 2,
                cy: api.getHeight() / 2,
                r: Math.min(api.getWidth(), api.getHeight()) * 0.49,
                startAngle: (270 + -this.angle) * Math.PI / 180,
                endAngle: (40 + -this.angle) * Math.PI / 180
              },
              style: {
                stroke: '#0CD3DB',
                fill: 'transparent',
                lineWidth: 1.5
              },
              silent: true
            }
          },
          data: [0]
        }, {
          name: 'ring5',
          type: 'custom',
          coordinateSystem: 'none',
          renderItem: (params, api) => {
            return {
              type: 'arc',
              shape: {
                cx: api.getWidth() / 2,
                cy: api.getHeight() / 2,
                r: Math.min(api.getWidth(), api.getHeight()) * 0.49,
                startAngle: (90 + -this.angle) * Math.PI / 180,
                endAngle: (220 + -this.angle) * Math.PI / 180
              },
              style: {
                stroke: '#0CD3DB',
                fill: 'transparent',
                lineWidth: 1.5
              },
              silent: true
            }
          },
          data: [0]
        }, {
          name: 'ring5',
          type: 'custom',
          coordinateSystem: 'none',
          renderItem: (params, api) => {
            const x0 = api.getWidth() / 2
            const y0 = api.getHeight() / 2
            const r = Math.min(api.getWidth(), api.getHeight()) * 0.49
            const point = getCirlPoint(x0, y0, r, (90 + -this.angle))
            return {
              type: 'circle',
              shape: {
                cx: point.x,
                cy: point.y,
                r: 4
              },
              style: {
                stroke: '#0CD3DB', // 粉
                fill: '#0CD3DB'
              },
              silent: true
            }
          },
          data: [0]
        }, {
          name: 'ring5', // 绿点
          type: 'custom',
          coordinateSystem: 'none',
          renderItem: (params, api) => {
            const x0 = api.getWidth() / 2
            const y0 = api.getHeight() / 2
            const r = Math.min(api.getWidth(), api.getHeight()) * 0.49
            const point = getCirlPoint(x0, y0, r, (270 + -this.angle))
            return {
              type: 'circle',
              shape: {
                cx: point.x,
                cy: point.y,
                r: 4
              },
              style: {
                stroke: '#0CD3DB', // 绿
                fill: '#0CD3DB'
              },
              silent: true
            }
          },
          data: [0]
        }, {
          type: 'pie',
          radius: ['85%', '65%'],
          silent: true,
          clockwise: true,
          startAngle: 90,
          z: 0,
          zlevel: 0,
          label: {
            normal: {
              position: 'center'

            }
          },
          data: [{
            value: this.proportion,
            name: '',
            itemStyle: {
              normal: {
                color: { // 完成的圆环的颜色
                  colorStops: [{
                    offset: 0,
                    color: '#4FADFD' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#28E8FA' // 100% 处的颜色
                  }]
                }
              }
            }
          },
          {
            value: 100 - this.proportion,
            name: '',
            label: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              normal: {
                color: '#173164'
              }
            }
          }
          ]
        },

        {
          name: '',
          type: 'gauge',
          radius: '85%',
          center: ['50%', '50%'],
          startAngle: 0,
          endAngle: 359.9,
          splitNumber: 8,
          hoverAnimation: true,
          axisTick: {
            show: false
          },
          splitLine: {
            length: 60,
            lineStyle: {
              width: 5,
              color: '#04072D'
            }
          },
          axisLabel: {
            show: false
          },
          pointer: {
            show: false
          },
          axisLine: {
            lineStyle: {
              opacity: 0
            }
          },
          detail: {
            show: false
          },
          data: [{
            value: 0,
            name: ''
          }]
        }

        ]
      }

      // 获取圆上面某点的坐标(x0,y0表示坐标，r半径，angle角度)
      const getCirlPoint = (x0, y0, r, angle) => {
        const x1 = x0 + r * Math.cos(angle * Math.PI / 180)
        const y1 = y0 + r * Math.sin(angle * Math.PI / 180)
        return {
          x: x1,
          y: y1
        }
      }
    },
    draw() {
      this.angle = this.angle + 3
      // window.requestAnimationFrame(draw);
      this.chart.setOption(this.option, true)
    }
  }
}
</script>
