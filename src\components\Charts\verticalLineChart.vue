<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    propData: {
      require: false,
      type: [Object, Array],
      default: () => {}
    },
    colors: {
      require: false,
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  created() {
    this.doNotRedraw = true
  },
  mounted() {
    this.$nextTick(() => {
      console.log(this.propData)
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      const barBottomColor = [
        '#FF877C',
        '#FFCE8C',
        '#F7FFA4',
        '#43E9A9',
        '#96FFB0'
      ]
      const barTopColor = [
        '#FD1547',
        '#FD9D15',
        '#FFD12D',
        '#9EFFD8',
        '#1ED94F'
      ]
      const _this = this
      this.option = {
        animationDuration: 1000,
        color: ['rgba(0, 191, 243, 1)'],
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '5%',
          right: 0,
          left: 0,
          bottom: '18%'
        },
        xAxis: {
          type: 'category',
          data: this.propData.bottomList,
          axisLine: {
            lineStyle: {
              color: 'transparent'
            }
          },
          axisLabel: {
            color: '#e2e9ff',
            textStyle: {
              fontSize: 12
            }
          },
          axisTick: {
            inside: true
          }
        },
        yAxis: {
          show: false
        },
        series: [
          {
            type: 'line',
            name: '晨检人数',
            data: this.propData.check,
            symbol: 'none',
            smooth: true,
            lineStyle: {
              normal: {
                width: 1
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 191, 243, 0.8)'
                    },
                    {
                      offset: 0.8,
                      color: 'rgba(0, 191, 243, 0.1)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            }
          },
          {
            name: '到岗人数',
            type: 'line',
            smooth: true,
            symbol: 'none',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: 'rgba(137, 189, 27, 0.3)'
                }, {
                  offset: 0.8,
                  color: 'rgba(137, 189, 27, 0)'
                }], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            lineStyle: {
              normal: {
                width: 1
              }
            },
            itemStyle: {
              normal: {
                color: '#22DDB8'
              }
            },
            data: this.propData.work
          },
          {
            data: this.propData.abnormal,
            name: '异常人数',
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
              normal: {
                width: 1
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: 'rgba(219, 50, 51, 0.3)'
                }, {
                  offset: 0.8,
                  color: 'rgba(219, 50, 51, 0)'
                }], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            itemStyle: {
              normal: {
                color: 'rgb(219,50,51)'
              }
            }
          }
        ]
      }
      this.chart.setOption(this.option)
      this.chart.on('click', function() {

      })
      console.log(this.propData)
      // if (this.propData.data && this.propData.data.length > 0) {
      this.autoPlayTool(this.chart, this.propData.bottomList, 0)
      // }
    }
  }
}
</script>
