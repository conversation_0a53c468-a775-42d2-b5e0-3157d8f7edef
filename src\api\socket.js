import Stomp from 'stompjs'
import SockJS from 'sockjs-client'
// const baseUrl = 'https://gd-bigdata.vankeytech.com'
const baseUrl = 'https://smart-canteen.vankeytech.com'
// const baseUrl = process.env.VUE_APP_BASE_API || 'http://192.168.0.62:8081'

// 'http://data.eighth.space:9901'

// 'http://192.168.0.92:8081'
// const baseUrl = 'http://canteen-data.vankeytech.com'

export function connectSocket(callback) {
  const token = JSON.parse(localStorage.getItem('cateenBigDataUserInfo')).userToken || localStorage.getItem('token')
  const userId = JSON.parse(localStorage.getItem('cateenBigDataUserInfo')).id
  const socket = new SockJS(`${baseUrl}/stompEndpoint?x-auth-token=${token}`)
  const stomp = Stomp.over(socket)
  stomp.connect({}, function() {
    stomp.subscribe(`/queue/${userId}/deviceAlarm`, function(res) {
      callback(JSON.parse(res.body))
    })
    stomp.subscribe(`/queue/${userId}/monitorPlayback`, function(res) {
      callback(JSON.parse(res.body))
    })
    stomp.send('/vanKeyTech/sLBigData', {}, '')
  })
}

