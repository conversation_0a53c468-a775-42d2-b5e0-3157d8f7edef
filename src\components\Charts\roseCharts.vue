<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>

<script>
import echarts from 'echarts'
import resize from './mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
export default {
  name: '<PERSON><PERSON>hart<PERSON>',
  mixins: [resize, componentMixin],
  props: {
    id: {
      require: false,
      type: String,
      default: 'RoseCharts'
    },
    width: {
      require: false,
      type: String,
      default: '2rem'
    },
    height: {
      require: false,
      type: String,
      default: '2rem'
    },
    propData: {
      require: false,
      type: Array,
      default: () => ['0%', '10%', '20%', '30%', '40%', '50%', '60%', '70%', '80%', '90%', '100%']
    }
  },
  data() {
    return {
      chartData: []
    }
  },
  watch: {
    propData: { // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        this.chartData = [
          { name: '谷薯类', countryValue: 32, value: newValue[0] },
          { name: '蔬菜类', countryValue: 32, value: newValue[1] },
          { name: '水果类', countryValue: 13, value: newValue[2] },
          { name: '畜肉类', countryValue: 6, value: newValue[3] },
          { name: '水产类', countryValue: 3, value: newValue[4] },
          { name: '奶制品', countryValue: 6, value: newValue[5] },
          { name: '豆制品', countryValue: 3, value: newValue[6] },
          { name: '油脂类', countryValue: 5, value: newValue[7] }
        ]
        this.chartData = this.chartData.sort(this.compare('countryValue'))
        console.log(this.chartData, 88888)
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  created() {
    this.doNotRedraw = true
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    compare(value) {
      return function(a, b) {
        var aaa = a[value]
        var bbb = b[value]
        return bbb - aaa
      }
    },
    initChart() {
      this.chart = echarts.init(
        document.getElementById(this.id)
      )
      // let total = 0
      // for (const item of this.propData) {
      //   total += item.value
      // }
      const data = this.propData
      const currentIndex = 0
      this.option = {
        animationDuration: 3000,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
          // formatter(params) {
          //   var value = params[0].value + '%'
          //   var value1 = data[params[0].dataIndex] + '%'
          //   var name = params[0].name
          //   var color = '#3DE2D8 ' // 图例颜色
          //   var color1 = '#32B6EF'
          //   var htmlStr = '<div style="padding: 10px 5px">'

          //   // 为了保证和原来的效果一样，这里自己实现了一个点的效果
          //   htmlStr += '<div style="display: flex;align-items: center">' +
          //     '<span style="width: 12px;height: 8px;font-size: 12px;margin-right: 3px;background-color: ' + color + '"></span>' +
          //     '<span style="font-size: 12px;margin-right: 5px">国家标准:</span>' +
          //     '<span>' + value + '</span>' +
          //     '</div>'
          //   htmlStr += '<div style="display: flex;align-items: center">' +
          //     '<span style="width: 12px;height: 8px;font-size: 12px;margin-right: 3px;background-color: ' + color1 + '"></span>' +
          //     '<span style="font-size: 12px;margin-right: 5px">' + name + ':</span>' +
          //     '<span>' + value1 + '</span>' +
          //     '</div>'
          //   // htmlStr += '</span>';

          //   htmlStr += '</div>'

          //   return htmlStr
          // }
        },
        legend: {
          left: 'right',
          top: 0,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#fff',
            padding: [5, 0, 0, 0]
          },
          borderRadius: 0,
          data: ['国家标准', '平台监测']
        },
        grid: [{
          top: 40,
          bottom: 20,
          left: '16%',
          width: '39%',
          containLabel: true
        },
        {
          top: 40,
          bottom: 30,
          left: 45,
          width: 0
        },
        {
          top: 40,
          bottom: 20,
          right: '6%',
          width: '39%',
          containLabel: true
        }
        ],
        xAxis: [{
          position: 'bottom',
          type: 'value',
          inverse: true,
          axisLabel: {
            show: true,
            color: '#fff',
            margin: 8,
            formatter: '{value}%'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#3364BD',
              type: 'dotted'
            }
          },
          splitNumber: 2
        },
        {
          gridIndex: 1,
          show: false
        },
        {
          position: 'bottom',
          gridIndex: 2,
          type: 'value',
          axisLabel: {
            show: true,
            color: '#fff',
            margin: 8,
            formatter: '{value}%'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#3364BD',
              type: 'dotted'
            }
          },
          splitNumber: 2
        }
        ],
        yAxis: [{
          position: 'right',
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          },
          type: 'category',
          inverse: false,
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          data: this.chartData.map((currentValue) => {
            return currentValue.name
          })
        },
        {
          gridIndex: 1,
          // position: "left",
          type: 'category',
          axisLabel: {
            show: true,
            color: '#fff',
            fontSize: 12,
            align: 'center',
            verticalAlign: 'top',
            lineHeight: -20
          },
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          data: this.chartData.map((currentValue) => {
            return currentValue.name
          })
        },
        {
          gridIndex: 2,
          position: 'left',
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          },
          type: 'category',
          inverse: false,
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          data: this.chartData.map((currentValue) => {
            return currentValue.name
          })
        }
        ],
        title: {
          text: '数据统计：本月',
          textStyle: {
            fontSize: 12,
            color: '#fff'
          }
        },
        series: [{
          type: 'bar',
          name: '国家标准',
          // label: {
          //   fontSize: 10,
          //   color: '#fff',
          //   lineHeight: 20,
          //   show: true,
          //   fontFamily: 'Rubik-Medium',
          //   distance: 10
          // },
          itemStyle: {
            color: '#3FE5DA'

          },
          data: this.chartData.map((currentValue) => {
            return currentValue.countryValue
          }),
          barWidth: 12
        },
        {
          type: 'bar',
          name: '平台监测',
          // label: {
          //   fontSize: 10,
          //   color: '#fff',
          //   lineHeight: 20,
          //   show: true,
          //   fontFamily: 'Rubik-Medium',
          //   distance: 10
          // },
          itemStyle: {
            color: '#028CCA'

          },
          data: this.chartData.map((currentValue) => {
            return currentValue.value
          }),
          barWidth: 12,
          xAxisIndex: 2,
          yAxisIndex: 2
        }
        ]
      }
      this.chart.setOption(this.option)
      // this.chart.setOption(this.option)
      // this.autoPlayTool(this.chart, this.propData, currentIndex)
    }
  }
}
</script>
