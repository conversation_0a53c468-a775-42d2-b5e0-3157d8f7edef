@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.score-detail {
  position: absolute;
}

.main-content {
  color: #B9CDEA;
  box-sizing: border-box;

  .no-data-img {
    animation: fadeIn .5s linear;
    color: #ABC9FF;
    width: 100%;
    height: 70%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      margin-bottom: 0.03rem;
    }
  }

  .box-title {
    font-size: 0.1rem;
    margin-bottom: 0.05rem;
    display: flex;
    align-items: center;
    color: #CBD9EF;

    .select {
      margin-left: auto;
      width: 1rem;
      height: 0.155rem;
      background: rgba(14, 139, 255, 0.32);
      background: url("../../assets/img/shaixuanc.png") no-repeat;
      background-size: 100% 100%;
      text-align: center;
      line-height:0.155rem;
      font-size: 0.1rem;
    }

    .el-input .el-input__inner {
      color: #3ADBFF !important;
    }

    >div {
      margin-left: 0.045rem;
    }
  }

  .left-side,
  .right-side {
    width: 2.25rem;
    height: 3.67rem;
    right: 0.2rem;
    top: 0.6rem;
  }

  .left-side,
  .right-side,
  .bottom-side {
    position: absolute;
    z-index: 999999999;
    font-size: 0.08rem;
  }

  .left-side {
    left: 0.2rem;

    .box1 {
      margin-bottom: 0.1rem;
      padding: 0 0.2rem;
      box-sizing: border-box;
      width: 2.25rem;
      height: calc(1rem - (calc(1080px - 100vh) / 4));
      display: flex;
      align-items: center;

      >div:last-child {
        margin-left: 0.1rem;

        >div {
          >div:first-child {
            display: flex;
            align-items: center;

            >img {
              width: 0.08rem;
              height: 0.08rem;
              margin-right: 0.05rem;
            }
          }

          >div:last-child {
            font-size: 0.16rem;
          }
        }
      }

      // >div {
      //   line-height: 0.128rem;
      //   display: flex;
      //   flex-direction: column;
      //   align-items: center;
      // }

      // .canteen-count {
      //   font-size: 0.12rem;
      //   font-weight: bold;
      //   color: #E94A48;
      // }

      // .system-count {
      //   font-size: 0.12rem;
      //   font-weight: bold;
      //   color: #F5CA34;
      // }

      // .percent {
      //   font-size: 0.12rem;
      //   font-weight: bold;
      //   color: #32B6A6;
      // }
    }

    .box2 {
      margin-bottom: 0.1rem;
      box-sizing: border-box;
      height: calc(1rem - (calc(1080px - 100vh) / 4));
      padding: 0.07rem 0.15rem;

      .box-main {
        position: relative;
        width: 100%;
        height: 100%;
        background: url('../../assets/img/xxfl.png');
        background-size: 100% 100%;

        >div {
          position: absolute;
        }

        .type-name {
          font-size: 14px;
          color: #B9CDEA;
        }

        .type-num {
          color: #7DFFFA;
          font-size: 18px;
        }

        .type-com {
          color: #7DFFFA;
          font-size: 14px;

        }

        .center {
          width: 0.2rem;
          text-align: center;
          top: 50%;
          left: 50%;
          transform: translateX(-50%) translateY(-50%);
          line-height: 0.09rem;
        }

        .top-left {
          left: 0.3rem;
          width: 1rem;
          height: calc(0.25rem - (calc(200px - 165px) / 2));
        }

        .top-right {
          top: 0;
          right: 0.15rem;
        }

        .bottom-left {
          bottom: 0;
          left: 0.3rem;
        }

        .bottom-right {
          right: 0.15rem;
          bottom: 0;
        }
      }
    }

    .box3 {
      margin-bottom: 0.1rem;
      padding: 0.1rem;
      box-sizing: border-box;
      height: calc(1rem - (calc(1080px - 100vh) / 4));
    }

    .box4 {
      background: url(../../assets/img/<EMAIL>) no-repeat 100%;
      padding: 0.05rem 0.2rem;
      box-sizing: border-box;
      height: calc(0.6rem - (calc(1080px - 100vh) / 4));

      .theme {
        height: 100%;
        text-align: center;
        font-size: 0.09rem;
        color: #ABC9FF;
        columns: #CCDAF0;
        line-height: 0.2rem;

        .line1,
        .line2 {
          display: flex;
          height: 40%;
          line-height: auto;
        }

        .line1 {
          div {
            width: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        .line2 {
          div {
            width: 33%;
            justify-content: center;
            align-items: center;
          }

          .center {
            font-size: 0.12rem;
            color: #25F7FF;
            text-shadow: 0 0.02rem 0.03rem #25F7FF;
          }
        }
      }
    }

  }

  .bottom-side {
    display: flex;
    justify-content: space-between;
    width: calc(100vw - 0.4rem);
    height: calc(0.65rem - (calc(1080px - 100vh) / 4));
    bottom: 0.2rem;
    left: 0.2rem;

    >div {
      padding: 0 0.15rem;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: calc((100vw - 0.4rem) / 4 - 0.2rem);

      >div {
        width: 0.45rem;
        height: 0.35rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .sub-title {
          font-size: 0.07rem;
          font-weight: bold;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 0.45rem;
          height: 0.15rem;
        }

        .sub-title::after {
          content: '';
          position: absolute;
          top: 0;
          width: 0.45rem;
          height: 1px;
          background: linear-gradient(to right, transparent, #195BC6, transparent)
        }

        .sub-title::before {
          content: '';
          position: absolute;
          bottom: 0;
          width: 0.45rem;
          height: 1px;
          background: linear-gradient(to right, transparent, #195BC6, transparent)
        }

        .value {
          text-align-last: center;
          font-size: 0.1rem;
          font-weight: bold;
        }

        .color1 {
          color: #8FD9C8
        }

        .color2 {
          color: #23D9F4
        }

        .color3 {
          color: #CFB588
        }

        .company {
          font-size: 0.07rem;
        }
      }
    }
  }

  .right-side {
    .risk-ranking {
      padding: 0.23rem 0.2rem;
      box-sizing: border-box;
      background: url(../../assets/img/especially.png) no-repeat;
      height: calc(4.5rem - calc(1080px - 100vh));
      background-size: 100% 100%;

      .result {
        width: 1.8rem;
        height: 0.66rem;
        line-height: 0.15rem;
        text-align: center;
        font-size: 0.1rem;
        font-weight: 500;
        color: #76F0FC;
        background: url(../../assets/img/<EMAIL>) no-repeat;
        background-size: contain;
        overflow: hidden;

        .title {
          width: 1rem;
          margin: 0.15rem auto 0;
        }
      }

      .tabs {
        display: flex;
        margin-top: 0.2rem;

        .active {
          color: #00CCFF;
        }

        .button {
          font-size: 0.1rem;
        }

        >div {
          cursor: pointer;
          width: 0.64rem;
          height: 0.18rem;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #1C44AA;
          box-shadow: inset 0px 0px 5px 0px #005088;
        }

        >div:nth-child(1) {
          border-top-left-radius: 5px;
          border-bottom-left-radius: 5px;
        }

        >div:nth-child(3) {
          border-top-right-radius: 5px;
          border-bottom-right-radius: 5px;
        }

        .selected {
          color: #00CCFF;
        }
      }

      @keyframes mymove {
        0% {
          transform: translateX(200px);
        }

        15% {
          transform: translateX(-10px);
        }

        23% {
          transform: translateX(0px);
        }

        100% {
          transform: translateX(0px);
        }
      }

      .listCentent {
        cursor: pointer;
        position: relative;
        height: calc(5 * calc(0.626rem - (calc(1080px - 100vh) / 5)));
        //margin-top: 0.1rem;
        overflow-y: scroll;
        overflow-x: hidden;

        .dangeImg {
          position: absolute;
          left: 30px;
        }

        .cententItem {
          width: 100%;
          height: calc(0.53rem - (calc(1080px - 100vh) / 5));
          background-color: #05195D;
          margin-top: 0.08rem;
          box-sizing: border-box;
          border-radius: 10px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          padding: 0.06rem 0;
          //transform: translateX(500px);
          animation: mymove 5s;

          .indexrisk {
            margin-left: 50px;

            .risks {
              margin-left: 10px;
            }
          }

          .center {
            margin-left: 30px;
          }

          .days {
            margin: 0 20px 0 30px;
            display: flex;
            justify-content: space-between;
          }
        }
      }

      // 弃用排行
      // .thead{
      //   background: linear-gradient(to right,#0c237805,#0c237870,#0c237805);        color: #1DBBF1;
      //   font-size: 0.08rem;
      //   font-weight: bold;
      //   height: 0.3rem;
      //   display: flex;
      //   justify-content: center;
      //   align-items: center;
      //   >div{
      //     text-align: center;
      //   }
      //   >div:nth-child(1){
      //     flex: 0.2;
      //   }
      //   >div:nth-child(2){
      //     flex: 0.6;
      //   }
      //   >div:nth-child(3){
      //     flex: 0.2;
      //   }
      // }
      // .tbody {
      //   height: calc(100% - 0.4rem);
      //   overflow: hidden;
      //   font-size: 18px;
      //   .tr:nth-child(odd){
      //     background: linear-gradient(to right,#0c237805,#0c237870,#0c237805);
      //   }
      //   .tr{
      //     height: 0.24rem;
      //     display: flex;
      //     align-items: center;
      //     >div{
      //       text-align: center;
      //     }
      //     >div:nth-child(1){
      //       flex: 0.2;
      //     }
      //     >div:nth-child(2){
      //       flex: 0.6;
      //     }
      //     >div:nth-child(3){
      //       flex: 0.2;
      //     }
      //     .td {
      //       font-size: 0.08rem;
      //       overflow: hidden;
      //       text-overflow: ellipsis;
      //       white-space: nowrap;
      //     }
      //     .td:last-child{
      //       color:#F94749
      //     }
      //   }
      // }
    }
  }
}
