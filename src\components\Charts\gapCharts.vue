<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>

<script>
import echarts from 'echarts'
import resize from './mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
export default {
  name: 'GapCharts',
  mixins: [resize, componentMixin],
  props: {
    id: {
      require: false,
      type: String,
      default: 'GapCharts'
    },
    width: {
      require: false,
      type: String,
      default: '2rem'
    },
    height: {
      require: false,
      type: String,
      default: '2rem'
    },
    propData: {
      require: false,
      type: Array,
      default: () => { return [] }
    }
  },
  watch: {
    propData: { // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      const series = []
      const arrName = []
      let sumValue = 0
      for (let i = 0; i < this.propData.length; i++) {
        sumValue += this.propData[i].value
        arrName.push(this.propData[i].name)
      }
      for (let i = 0; i < this.propData.length; i++) {
        series.push({
          name: '',
          type: 'pie',
          clockWise: false, // 顺时加载
          hoverAnimation: false, // 鼠标移入变大
          radius: [90 - i * 16 + '%', 83 - i * 16 + '%'],
          center: ['20%', '49%'],
          label: {
            show: false
          },
          itemStyle: {
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          },
          data: [{
            value: this.propData[i].value,
            name: this.propData[i].name
          }, {
            value: sumValue - this.propData[i].value,
            name: '',
            itemStyle: {
              color: 'rgba(0,0,0,0)',
              borderWidth: 0
            },
            tooltip: {
              show: false
            },
            hoverAnimation: false
          }]
        })
        series.push({
          name: '',
          type: 'pie',
          silent: true,
          z: 1,
          clockWise: false, // 顺时加载
          hoverAnimation: false, // 鼠标移入变大
          radius: [90 - i * 16 + '%', 83 - i * 16 + '%'],
          center: ['20%', '49%'],
          label: {
            show: false
          },
          itemStyle: {
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            borderWidth: 5
          },
          data: [{
            value: 7.5,
            itemStyle: {
              color: '#042F5F',
              borderWidth: 0
            },
            tooltip: {
              show: false
            },
            hoverAnimation: false
          }, {
            value: 2.5,
            name: '',
            itemStyle: {
              color: 'rgba(0,0,0,0)',
              borderWidth: 0
            },
            tooltip: {
              show: false
            },
            hoverAnimation: false
          }]
        })
      }
      this.chart = echarts.init(
        document.getElementById(this.id)
      )
      this.option = {
        animationDuration: 3000,
        color: ['#0E81FE', '#17EBE9', '#FFBC00'],
        legend: {
          icon: 'circle',
          top: '45%',
          left: '50%',
          itemGap: 5,
          itemWidth: 7,
          textStyle: {
            color: '#CDDFFF',
            rich: {
              name: {
                align: 'left',
                width: 60
              }
            }
          },
          formatter: (data) => {
            for (const item of this.propData) {
              if (item.name === data) {
                return '{name|' + item.name + '}' + item.value + '条'
              }
            }
          }
        },
        xAxis: [{
          show: false
        }],
        series
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
