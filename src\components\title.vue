<template>
  <div v-if="hasHeader" class="header">
    <div class="left">
      <div
        :class="{ 'left-active': navigationText == '数智食安' }"
        @click="checkNavigation('数智食安', '/wisdomFoodSafety')"
      >
        数智食安
      </div>
      <div
        :class="{ 'left-active': navigationText == '智慧监测','active-warning':smartWarning }"
        @click="checkNavigation('智慧监测', '/smartMonitor')"
      >
        智慧监测
      </div>
      <div
        :class="{ 'left-active': navigationText == '明厨亮灶','active-warning':kitchenWarning }"
        @click="checkNavigation('明厨亮灶', '/brightKitchen')"
      >
        明厨亮灶
      </div>
    </div>
    <div class="center" @click="toHome">山东能源发展服务集团智慧食安监管平台</div>
    <div class="right">
      <div
        :class="{ 'right-active': navigationText == '主体监管' }"
        @click="checkNavigation('主体监管', '/subjectSupervision')"
      >
        主体监管
      </div>
      <div
        :class="{ 'right-active': navigationText == '多元共治' }"
        @click="checkNavigation('多元共治', '/multipleGovernance')"
      >
        多元共治
      </div>
      <div
        :class="{ 'right-active': navigationText == '全域总览' }"
        @click="checkNavigation('全域总览', '/globalOverview')"
      >
        全域总览
      </div>
    </div>
    <div class="time">
      <span><b>{{ year }}</b>/<b>{{ month }}</b>/<b>{{ date }}</b></span>
      <span>{{ timeRight }}</span>
    </div>
    <!--    <div v-if="hasButtons" class="bottom">-->
    <!--      <div-->
    <!--        :class="['nochecked1', commandActive ? 'checked' : '']"-->
    <!--        @click="toggleReaddingMode"-->
    <!--      >-->
    <!--        &lt;!&ndash; <img src="../assets/img/multipleGovernance/zhsp.png">-->
    <!--        <div>阅览模式</div> &ndash;&gt;-->
    <!--      </div>-->
    <!--      <div class="nochecked2" @click="toWisdomFoodSafety">-->
    <!--        &lt;!&ndash; <img src="../assets/img/multipleGovernance/yuelan.png">-->
    <!--        <div>指挥沙盘</div> &ndash;&gt;-->
    <!--      </div>-->
    <!--      &lt;!&ndash; <div-->
    <!--        class="nochecked searchNotActive"-->
    <!--        :class="['nochecked', searchActive ? 'checked' : '']"-->
    <!--        @click="searchActive = !searchActive"-->
    <!--      >-->
    <!--        <img src="../assets/img/multipleGovernance/sousuo.png">-->
    <!--        <div>搜索学校</div>-->
    <!--      </div> &ndash;&gt;-->
    <!--    </div>-->
    <transition name="fade">
      <div
        v-if="hasButtons && searchActive"
        class="search-box"
      >
        <input v-model="search" type="text" placeholder="输入项目点名称查询">
        <img src="../assets/img/multipleGovernance/sousuo.png" @click="getSearch">
      </div>
    </transition>
    <div v-if="hasBackButton" class="back-button" @click="jumpBack"><i class="el-icon-back" /> 返回导航页</div>
  </div>
</template>

<script>
import bus from '@/utils/bus'
import { connectSocket } from '@/api/socket'
export default {
  name: 'Title',
  data() {
    return {
      navigationText: '数智食安',
      isbimg: true,
      searchActive: false,
      commandActive: false,
      path: '',
      search: '',
      hasButtons: false,
      hasHeader: true,
      readdingModeTimer: null,
      smartWarning: false,
      kitchenWarning: false,
      routerMsg: undefined,
      timeLeft: '',
      timeRight: '',
      year: '',
      month: '',
      date: '',
      time: undefined,
      hasBackButton: undefined
    }
  },
  watch: {
    $route(to, from) {
      this.hasButtons = to.meta.hasButtons
      this.hasHeader = to.meta.hasHeader
      if (localStorage.router) {
        console.log(JSON.parse(localStorage.router))
        this.navigationText = JSON.parse(localStorage.router).name
      }
      // 三方跳转下，显示返回按钮
      if (this.hasHeader) {
        this.hasBackButton = sessionStorage.getItem('hasBackButton')
      }
    }
  },
  mounted() {
    this.navigationText = '数智食安'
    this.connectSocket()
    this.showTime()
  },
  beforeDestroy() {
    clearInterval(this.readdingModeTimer)
    clearInterval(this.time)
    this.readdingModeTimer = null
  },
  methods: {
    toHome() {
      this.$router.push('/commandSandTable')
    },
    // 链接socket
    connectSocket() {
      connectSocket(res => {
        if (res.type) { // 摄像头
          if (this.navigationText !== '明厨亮灶') {
            this.kitchenWarning = true
            this.routerMsg = res
          } else {
            bus.$emit('getKitchenWarning', res)
          }
        } else { // 设备
          if (this.navigationText !== '智慧监测') {
            this.smartWarning = true
            this.routerMsg = res
          } else {
            bus.$emit('getSmartWarning', res)
          }
        }
      })
    },
    // 三方登录情况下，显示返回按钮
    jumpBack() {
      window.location.href = 'http://10.39.235.37:8095/html/homepage/index.html'
    },
    showTime() {
      clearInterval(this.time)
      var today, hour, second, minute, year, month, date
      var strDate
      today = new Date()
      year = today.getYear() + 1900
      month = today.getMonth() + 1
      date = today.getDate() < 10 ? ('0' + today.getDate()) : today.getDate()
      hour = today.getHours() < 10 ? ('0' + today.getHours()) : today.getHours()
      minute = today.getMinutes() < 10 ? ('0' + today.getMinutes()) : today.getMinutes()
      second = today.getSeconds() < 10 ? ('0' + today.getSeconds()) : today.getSeconds()
      this.year = year
      this.month = month < 10 ? ('0' + month) : month
      this.date = date
      this.timeRight = hour + ':' + minute + ':' + second
      this.time = setInterval(() => {
        this.showTime()
      }, 1000)
    },
    // 切换巡阅模式
    toggleReaddingMode() {
      // this.$emit('toggleReaddingMode')
      this.commandActive = !this.commandActive
      if (this.commandActive) {
        const routesList = this.$router.options.routes.filter((item) => {
          return item.meta.hasButtons
        })
        let routerIndex = 1
        this.$router.push(routesList[routerIndex].path)
        this.navigationText = routesList[routerIndex].meta.title
        localStorage.router = JSON.stringify({ value: routesList[routerIndex].path, name: routesList[routerIndex].meta.title })
        this.readdingModeTimer = setInterval(() => {
          routerIndex++
          if (routerIndex > routesList.length - 1) {
            routerIndex = 0
          }
          this.navigationText = routesList[routerIndex].meta.title
          localStorage.router = JSON.stringify({ value: routesList[routerIndex].path, name: routesList[routerIndex].meta.title })
          this.$router.push(routesList[routerIndex].path)
        }, 5000)
      } else {
        clearInterval(this.readdingModeTimer)
      }
    },
    checkNavigation(text, router) {
      if (this.navigationText === text) {
        return false
      }
      this.navigationText = text
      if (text === '智慧监测' || text === '明厨亮灶') {
        this.$router.push({ path: router, query: { data: this.routerMsg }})
      } else {
        this.$router.push(router)
      }
      this.routerMsg = undefined
      localStorage.router = JSON.stringify({ value: router, name: text })
      text === '智慧监测' ? this.smartWarning = false : ''
      text === '明厨亮灶' ? this.kitchenWarning = false : ''
      this.search = ''
      this.searchActive = false
      this.commandActive = false
      clearInterval(this.readdingModeTimer)
      this.readdingModeTimer = null
    },
    toWisdomFoodSafety() {
      this.$router.push('/commandSandTable')
      this.commandActive = false
      clearInterval(this.readdingModeTimer)
      this.readdingModeTimer = null
    },
    getSearch() {
      bus.$emit('getSearch', this.search)
    }
  }
}
</script>

<style scoped lang="scss">
.header {
  height: 0.42rem;
  color: #39d6fe;
  z-index: 99;
  display: flex;
  justify-content: space-between;
  .time {
    width: 1.4rem;
    height: 0.1rem;
    z-index: 110;
    position: absolute;
    top: 0.18rem;
    color: #85cef0;
    font-size: 0.12rem;
    right: 4.2rem;
    span {
      &:first-child {
        font-family: HYZongYiJ;
      }
      b {
        font-family: myFont;
      }
      &:last-child {
        font-family: myFont;
        margin-left: 0.12rem;
      }
    }
  }
  .left {
    margin-left: 0.16rem;
    display: flex;
    align-items: center;
    height: 0.28rem;
    margin-top: 0.08rem;
    > div {
      width: 0.91rem;
      height: 0.28rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: url('../assets/img/left-menu.png');
      background-size: 100% 100%;
      font-size: 0.1rem;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #90a6cf;
      cursor: pointer;
    }
    .left-active {
      background-image: url('../assets/img/left-menu-active.png');
      color: #ffffff;
    }
    .active-warning {
      background-image: url('../assets/img/warning_bg.png');
      animation: shadow .8s infinite;
    }
    @keyframes shadow {
      0%{
        opacity: 0.6;
      }
      50%{
        opacity: 1;
      }
      100%{
        opacity: 0.6;
      }
    }
  }
  .center {
    background: url('../assets/img/<EMAIL>') no-repeat;
    background-size: 100% 100%;
    width: 3.9rem;
    height: 0.45rem;
    font-size: 0.17rem;
    font-family: HYZongYiJ;
    font-weight: 400;
    color: #75f1fc;
    text-shadow: 0 0.01rem 0.02rem #000000;
    display: flex;
    line-height: 0.25rem;
    // align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding-bottom: 0.025rem;
    cursor: pointer;
  }
  .right {
    margin-right: 0.16rem;
    display: flex;
    align-items: center;
    height: 0.28rem;
    margin-top: 0.08rem;
    > div {
      width: 0.91rem;
      height: 0.28rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: url('../assets/img/right-menu.png');
      background-size: 100% 100%;
      font-size: 0.1rem;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #90a6cf;
      cursor: pointer;
    }
    .right-active {
      background-image: url('../assets/img/right-menu-active.png');
      color: #ffffff;
    }
  }
  .bottom {
    width: 1.1rem;
    position: absolute;
    display: flex;
    justify-content: space-between;
    bottom: -60%;
    left: 19.5%;
    transform: translateX(-50%);
    cursor: pointer;
    .searchNotActive {
      transition: all 0.5s linear;
    }
    // > div {
    //   cursor: pointer;
    // }
    .nochecked1:hover{
      background-image: url('../assets/img/<EMAIL>');
      color: #D0E7E9;
    }
    .nochecked2:hover{
      background-image: url('../assets/img/<EMAIL>');
      color: #D0E7E9;
    }
    .nochecked1 {
      // display: flex;
      // align-items: center;
      // justify-content: center;
      background-image: url('../assets/img/<EMAIL>');
      width: 0.5rem;
      height: 0.15rem;
      // border-radius: 0.5rem;
      // border: 1px solid;
      font-size: 0.07rem;
      color: #1CE2FD;
      // img {
      //   width: 0.08rem;
      //   height: 0.08rem;
      //   margin-right: 0.04rem;
      // }
    }
    .nochecked2 {
      width: 0.5rem;
      height: 0.15rem;
      font-size: 0.07rem;
      color: #1CE2FD;
      // border: 1px solid;
      background-image: url('../assets/img/<EMAIL>');
      // img {
      //   width: 0.08rem;
      //   height: 0.08rem;
      //   margin-right: 0.04rem;
      // }
    }

    .checked {
      // background-image: url('../assets/img/multipleGovernance/xzms.png');
      // background-size: 100% 100%;
      background-image: url('../assets/img/<EMAIL>');
    }
  }
  .search-box {
    position: absolute;
    bottom: -150%;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    border: 1px solid #216dfd;
    background: rgba(1, 18, 71, 0.6);
    box-shadow: 0px 0px 30px 0px rgba(1, 9, 96, 0.5);
    height: 0.2rem;
    width: 2rem;
    font-size: 0.0765rem;
    > input {
      width: calc(100% - 0.25rem);
      color: #d0e7e9;
      height: 100%;
      outline: none;
      border: none;
      padding: 0 0.05rem;
      background: transparent;
    }
    > img {
      width: 0.1rem;
      cursor: pointer;
    }
  }
  .back-button{
    color: #fff;
    position: absolute;
    top: 0.38rem;
    left: -0.52rem;
    background: rgb(0, 0, 0);
    cursor: pointer;
    padding: 0.05rem 0.08rem;
    font-size: 0.07rem;
    border-radius: 5px;
    transition: all .5s;
  }
  .back-button:hover{
    left: 0;
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}
</style>
