<template>
  <div class="marker-info">
    <div class="center-name">
      <div title="centerName">{{ centerName }}</div>
    </div>
    <div class="warning-main">
      <LivePlayer :video-url="warningVideoUrl" autoplay :hasaudio="false" fluent live stretch />
    </div>
  </div>
</template>

<script>
import LivePlayer from '@liveqing/liveplayer'
export default {
  name: '',
  components: {
    LivePlayer
  },
  props: ['centerName', 'warningVideoUrl'],
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="scss">
.warning-main{
  width: 1.75rem;
  height: 1rem;
}
.close {
      width: 0.15rem;
      height: 0.15rem;
      position: absolute;
      top: 0.2rem;
      right: 0.05rem;
      border: none;
      cursor: pointer;
    }
.marker-info {
  width: 1.84rem;
  background: url('../assets/img/marker-bg.png') no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0.05rem;
}
</style>
