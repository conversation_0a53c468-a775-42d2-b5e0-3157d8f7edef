<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  name: 'AccessRecordCharts',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    propData: {
      require: false,
      type: Object,
      default: () => { }
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  created() {
    this.doNotRedraw = true
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.option = {
        animationDuration: 3000,
        color: ['#193E8D', '#2473EF'],
        tooltip: {
          show: false,
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        title: {
          text: this.propData.percentage,
          textStyle: {
            fontSize: 20,
            color: '#fff'
          },

          textAlign: 'center',
          left: '50%',
          top: '40%'
        },
        series: [
          {
            name: '本月出勤率',
            hoverAnimation: false,
            type: 'pie',
            radius: ['65%', '80%'],
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false,
                fontSize: '30',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: this.propData.workDay - this.propData.normal, name: '未出勤' },
              { value: this.propData.normal, name: '出勤' }
            ]
          }
        ]
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
