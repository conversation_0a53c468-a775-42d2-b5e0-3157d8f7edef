<template>
  <div id="main">
    <!-- 左边 -->
    <div
      class="main-left left-side"
      :class="{ 'left-hide': animationIndex == 0 && readingMode }"
    >
      <div>
        <div class="main-left-top">
          <img
            style="width: 0.16rem; height: 0.145rem"
            src="../../assets/img/multipleGovernance/bt.png"
          />
          <div style="color: #cbd9ef; margin-left: 0.02rem">本月陪餐</div>
        </div>
        <div
          class="box rankList"
          @mouseenter="stopSwiper('swiper')"
          @mouseleave="startSwiper('swiper')"
        >
          <Swiper
            v-if="monthAccompanyList.length > 0"
            ref="swiper"
            :options="swiperOption"
            class="calendar-top"
          >
            <SwiperSlide
              v-for="(item, index) in monthAccompanyList"
              :key="index"
            >
              <div class="single">
                <div class="title">
                  <span class="left">{{ item.name }}</span>
                  <span class="right">最近陪餐：{{
                    item.recentTime === 0 ? '今日' : item.recentTime + '天前'
                  }}</span>
                </div>
                <div class="content" style="flex-direction: row">
                  <!--                  <div class="content" style="display: flex;flex-direction: column;justify-content: center">-->

                  <img
                    :src="item.images[0]"
                    class="left"
                    alt=""
                    @click="imageBtn(item.images[0])"
                  >
                  <div class="right">
                    <span>餐次：{{
                      item.time
                        ? item.time.substr(5, item.time.length - 1)
                        : undefined
                    }}
                      {{ item.description }}</span>
                    <span>职务：{{ item.job }}</span>
                    <span>姓名：
                      <el-tooltip
                        style="cursor: pointer"
                        effect="dark"
                        :content="item.accompanyingName"
                        placement="top-start"
                      >
                        <span>{{ item.accompanyingName }}</span>
                      </el-tooltip>
                    </span>
                  </div>
                </div>
              </div>
            </SwiperSlide>
          </Swiper>
          <div v-else class="no-data">暂无数据</div>
        </div>
      </div>
      <div>
        <div class="main-left-top">
          <img
            style="width: 0.16rem; height: 0.145rem"
            src="../../assets/img/multipleGovernance/bt.png"
          />
          <div style="color: #cddeff; margin-left: 0.02rem">投诉分析</div>
        </div>
        <div class="box left-two">
          <div class="totalnum">
            <div class="complaint_div">已处理投诉</div>
            <img src="../../assets/img/multipleGovernance/line.png" />
            <div style="color: #6cebec; margin-left: 0.03rem">
              <span style="font-size: 0.12rem; font-weight: bold">{{
                gapcountNum
              }}</span>
              <span>条</span>
            </div>
          </div>
          <div class="data-right-bottom">
            <gap-charts
              id="multip_gapCharts"
              width="2.05rem"
              height="0.78rem"
              :prop-data="gapList"
            />
          </div>
        </div>
      </div>
      <div>
        <div class="main-left-top">
          <img
            style="width: 0.16rem; height: 0.145rem"
            src="../../assets/img/multipleGovernance/bt.png"
          />
          <div style="color: #cbd9ef; margin-left: 0.02rem">食安词云</div>
        </div>
        <div class="box words">
          <words-charts
            v-if="wordsList.length>0"
            id="multip_wordsCharts"
            :width="'100%'"
            :height="'100%'"
            :prop-data="wordsList"
          />
        </div>
      </div>
    </div>
    <!-- 右边 -->
    <div
      class="main-right"
      :class="{ 'right-hide': animationIndex == 1 && readingMode }"
    >
      <!-- 膳食结构 -->
      <div>
        <div class="main-left-top dietaryCountriesStructure">
          <div class="left">
            <img
              style="width: 0.16rem; height: 0.145rem"
              src="../../assets/img/multipleGovernance/bt.png"
            />
            <div style="color: #cddeff; margin-left: 0.02rem">膳食结构</div>
          </div>
          <div class="right">
            <el-select v-model="currCenter" placeholder="请选择">
              <el-option
                v-for="item in heatmapData"
                :key="item.id"
                :label="
                  item.isHoliday
                    ? item.centerName + '（放假）'
                    : item.centerName
                "
                :value="item.id"
              />
            </el-select>
          </div>
        </div>
        <div class="data-right-top">
          <rose-charts
            id="multip_roseCharts"
            :prop-data="propData"
            :width="'100%'"
            :height="'100%'"
          />
        </div>
      </div>
      <!-- 营养评价 -->
      <div>
        <div class="main-left-top dietaryCountriesStructure">
          <div class="left">
            <img
              style="width: 0.16rem; height: 0.145rem"
              src="../../assets/img/multipleGovernance/bt.png"
            />
            <div style="color: #cddeff; margin-left: 0.02rem">营养评价</div>
          </div>
          <div class="right">
            <el-select v-model="currCenter" placeholder="请选择">
              <el-option
                v-for="item in heatmapData"
                :key="item.id"
                :label="
                  item.isHoliday
                    ? item.centerName + '（放假）'
                    : item.centerName
                "
                :value="item.id"
              />
            </el-select>
          </div>
        </div>
        <div class="data-right-Nutritional">
          <div class="nutr-top">
            <div class="top-title">
              <div class="title-right">
                {{ evaluation.center }}
              </div>
            </div>
            <div class="top-branch">
              <div class="branch-name">系统评分:</div>
              <div class="branch-num">
                <img
                  v-for="i in evaluation.branch"
                  :key="i"
                  src="../../assets/img/xing.png"
                  alt=""
                  style="width: 0.1rem; height: 0.1rem"
                  class="startImg"
                />
                <img
                  v-for="i in emptyStar"
                  :key="i"
                  src="../../assets/img/emptyStar.png"
                  alt=""
                  style="width: 0.11rem; height: 0.1rem"
                  class="startImg"
                />
              </div>
            </div>
          </div>
          <div class="nutr-foot">
            <span>系统评价:</span>
            <span style="margin-left: 0.05rem">{{ evaluation.pingjia }}</span>
          </div>
        </div>
      </div>
      <!-- 数据统计 -->
      <div>
        <div class="main-left-top">
          <img
            style="width: 0.16rem; height: 0.145rem"
            src="../../assets/img/multipleGovernance/bt.png"
          />
          <div style="color: #cddeff; margin-left: 0.02rem">回复概况</div>
        </div>
        <div class="data-right-content">
          <statisticsCharts
            id="multip_statisticsCharts1"
            width="1rem"
            height="0.9rem"
            :title="'回复率'"
            :start-color="'#052956'"
            :end-color="'#05B2FF'"
            :value="replySatisfaction.replyRate"
          />
          <statisticsCharts
            id="multip_statisticsCharts2"
            width="1rem"
            height="0.9rem"
            :title="'满意率'"
            :start-color="'#0A304D'"
            :end-color="'#45F8E6'"
            :value="replySatisfaction.satisfactionRate"
          />
        </div>
      </div>
    </div>
    <div class="main-content">
      <div id="map_container" ref="map_container" />
    </div>
    <!-- 地图图例 -->
    <div class="legend">
      <div class="legend-item">
        <img src="../../assets/img/<EMAIL>" alt="" />
        <span>营业</span>
      </div>

      <div class="legend-item">
        <img src="../../assets/img/<EMAIL>" alt="" />
        <span>歇业</span>
      </div>
    </div>
    <!-- 图片预览 -->
    <preview-image
      ref="previewImage"
      :show-viewer="showViewer"
      :url-list="previewImageUrlList"
    />
    <!-- 地图筛选 -->
    <map-marker-selects @postSelectsData="gethotmap" />
  </div>
</template>
<script>
import AMap from 'AMap'
import PreviewImage from '@/components/PreviewImage'
import viewMixins from '@/views/mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
import MapMarkerSelects from '@/components/mapMarkerSelects'
// import componentTitle from '@/components/title'
import mapImg from '@/assets/img/map.png'
import wordsCharts from '@/components/Charts/wordsCharts'
import RoseCharts from '@/components/Charts/roseCharts'
import statisticsCharts from '@/components/Charts/statisticsCharts'
import gapCharts from '@/components/Charts/gapCharts'
import icon1 from '@/assets/img/<EMAIL>'
import icon2 from '@/assets/img/<EMAIL>'
import moment from 'moment'
import {
  getRoseData,
  getGap,
  getStatistics,
  getWords,
  getmyCanlendar,
  getSameMonth,
  getHotMap,
  getMonthAccompany,
  getReplySatisfaction
} from '@/api/multipleGovernance'
import bus from '@/utils/bus'
// import Loca from 'Loca'
var time = new Date()
var arr = [time.getFullYear(), time.getMonth() + 1, time.getDate()]
var dateStr = arr.join('-').toString()
var infoWindow
let _that
import image1 from '../../assets/img/xing.png'
import image2 from '../../assets/img/unxing.png'
export default {
  components: {
    PreviewImage,
    // componentTitle,
    wordsCharts,
    RoseCharts,
    statisticsCharts,
    gapCharts,
    MapMarkerSelects
  },
  mixins: [componentMixin, viewMixins],
  data() {
    return {
      total: 0,
      emptyStar: '',
      evaluation: {},
      currCenter: undefined,
      heatmapData: [],
      previewImageUrlList: [],
      isShow: false,
      data: '',
      party: [2, 3, 4, 4, 4],
      selectTime: null,
      week: [],
      format: 'YYYY-MM-DD',
      html: '',
      htmls: '',
      map: '',
      oldlng: '',
      oldlat: '',
      caterNum: [],
      curCenterId: '',
      showViewer: false,
      search: '',
      delMarkerList: [],
      monthAccompanyList: [],
      monthAccompanyLists: [{}, {}],
      swiperOption: {
        direction: 'vertical',
        slidesPerView: 1,
        slidesPerGroup: 1,
        loop: true,
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        }
      },
      replySatisfaction: {},
      accompanyNum: [],
      score: [],
      propData: [],
      description: '',
      dietaryStructures: [],
      courMon: '', // 查看的当前月
      monDays: [], // 该月有哪些陪餐
      wordsList: [], // 词云
      statisticsList: {}, // 折线图
      roseList: [], // 膳食结构
      gapList: [], // 投诉列表
      gapcountNum: '', // 投诉总数
      heatmap: null
    }
  },
  watch: {
    currCenter(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.getAccompanyCurrent()
        this.getrose()
      }
    }
  },
  mounted() {
    console.log(JSON.parse(localStorage.getItem('cateenBigDataUserInfo')), 'xxxxxx')
    this.currCenter = JSON.parse(localStorage.getItem('cateenBigDataUserInfo')).serviceIds[0]
    _that = this
    this.initMap()
    this.formatDate()
    this.getMonthAccompany()
    this.getgap()
    this.getstatistics()
    this.getwords()
    this.getReplySatisfaction()
    bus.$on('getSearch', (msg) => {
      this.search = msg
      this.gethotmap()
    })
    this.emptySnum()
  },
  created() {
    window.preWeek = this.preWeek // 解决字符串模板@click无效的问题
    window.nextWeek = this.nextWeek
    window.imageBtn = this.imageBtn
  },
  methods: {
    //
    clearfire() {
      const _this = this
      setTimeout(() => {
        _this.heatmap.hide()
      }, 3000)
    },
    // 得到空星数量
    emptySnum() {
      this.emptyStar = 5 - this.evaluation.branch
    },
    async initMap() {
      const _this = this
      const map = new AMap.Map(this.$refs.map_container, {
        // center: _this.mapCenter,
        center: [106.958633, 34.43728],
        resizeEnable: true,
        zoom: 5,
        viewMode: '3D',
        // pitch: 40,
        // rotation: -50,
        rotateEnable: false,
        zoomEnable: true,
        dragEnable: true,
        zooms: [5, 18],
        enableMapClick: false,
        // features: ['bg', 'road'],
        // viewMode: '2D'
        mapStyle: 'amap://styles/darkblue', // 设置地图的显示样式
      })
      this.addControl(AMap, map)

      map.on('click', () => {
        map.clearInfoWindow()
      })
      this.map = map
      this.gethotmap()
    },
    // 获取陪餐排名
    getMonthAccompany() {
      getMonthAccompany(this.DISTRICT_CODE['大邑县']).then((res) => {
        this.monthAccompanyList = res.data.data
      })
    },
    // 获取回复满意度
    getReplySatisfaction() {
      getReplySatisfaction(this.DISTRICT_CODE['大邑县']).then((res) => {
        this.replySatisfaction = res.data.data
      })
    },
    creatMarker() {
      const map = this.map
      const _this = this
      this.getrose()
      this.getAccompanyCurrent()
      // var _thisheatmap
      // this.clearfire()
      var heatmapData = _this.heatmapData
      console.log(heatmapData, 123)
      // -------------------------------设置marker-------------
      const marker1 = new AMap.Icon({
        size: new AMap.Size(48, 55),
        image: icon1,
        imageSize: new AMap.Size(48, 55),
        imageOffset: new AMap.Pixel(0, 0)
      })
      const marker2 = new AMap.Icon({
        size: new AMap.Size(48, 55),
        image: icon2,
        imageSize: new AMap.Size(48, 55),
        imageOffset: new AMap.Pixel(0, 0)
      })
      if (this.delMarkerList && this.delMarkerList.length > 0) {
        map.remove(this.delMarkerList)
        this.delMarkerList = []
      }
      for (const item of heatmapData) {
        const arr = []
        arr.push(item.lng)
        arr.push(item.lat)
        var marker = new AMap.Marker({
          icon: item.isHoliday ? icon2 : icon1,
          position: arr,
          clickable: true,
          extData: item,
          zIndex: 9999,
          visible: true
        })
        this.marker = marker
        AMap.event.addListener(marker, 'click', async function(e) {
          _this.dietaryStructures = []
          _this.score = []
          _this.description = ''
          const extData = e.target.w.extData
          _this.curCenterId = extData.id
          await _this.formatDate()
          const month = new Date().getMonth() + 1 + '月'
          const week = _this.getMonthWeek(
            new Date().getFullYear(),
            new Date().getMonth() + 1,
            new Date().getDate()
          )
          const dietaryStructures = _this.dietaryStructures
          const score = _this.score
          const description = _this.description
          const oldmarker = marker
          map.remove([marker])
          // 构建信息窗体中显示的内容
          var info = `<div class='popup'>
            <div class='popup_title'><div title="${extData.centerName}">${
  extData.centerName
}</div></div>
            <div id="accompany" class="accompany">
              <div class="title">
                 <span>膳食结构</span>
                 <span>${month} 第${week.getWeek}周</span>
              </div>
              <div class="structure">
                <div class="list">
                  <span></span>
                  <span>谷薯类 ${dietaryStructures[0]}</span>
                </div>
                 <div class="list">
                  <span></span>
                  <span>蔬菜类 ${dietaryStructures[1]}</span>
                </div>
                 <div class="list">
                  <span></span>
                  <span>水果类 ${dietaryStructures[2]}</span>
                </div>
                 <div class="list">
                  <span></span>
                  <span>畜肉类 ${dietaryStructures[3]}</span>
                </div>
                 <div class="list">
                  <span></span>
                  <span>水产类 ${dietaryStructures[4]}</span>
                </div>
                 <div class="list">
                  <span></span>
                  <span>奶制品 ${dietaryStructures[5]}</span>
                </div>
                 <div class="list">
                  <span></span>
                  <span>豆制品 ${dietaryStructures[6]}</span>
                </div>
                 <div class="list">
                  <span></span>
                  <span>油脂类 ${dietaryStructures[7]}</span>
                </div>
              </div>
              <div class="score">
               <div class="score-title">
                 <span>系统评分</span>
                 <img src="${score[0] ? image1 : undefined}" alt="">
                 <img src="${
  score[1] ? (score[1] ? image1 : image2) : undefined
}" alt="">
                 <img src="${
  score[2] ? (score[2] ? image1 : image2) : undefined
}" alt="">
                 <img src="${
  score[3] ? (score[3] ? image1 : image2) : undefined
}" alt="">
                 <img src="${
  score[4] ? (score[4] ? image1 : image2) : undefined
}" alt="">
               </div>
               <div class="score-content" title="${description}">${description}</div>
              </div>
            </div>
            <div class='popup_conter' >
              <div>陪餐打卡</div>
              <div class='popup_conter_time' >${_this.courMon}</div>
            </div>
            <div class='popup_back'>
              <div class='popup_dirleft' onClick="preWeek()" >
              </div>
              <div style="flex:2" >
                <div class='popup_week popup_one' >
                 ${_this.html}
                </div>
                <div class='popup_week popup_ones' >
                 ${_this.htmls}
                </div>
                <div class='popup_week popup_ones popup_img' >
                 ${_this.htmlimage}
                </div>
              </div>
              <div class='popup_dirright' onClick="nextWeek()" >
              </div>
            </div>
            <div class="popup_bottom">
              <div class="popup_bottom_div" >
                <div>未处理投诉：</div>
                <div style="color:red" >${extData.unHandle}条</div>
              </div>
              <div class="popup_bottom_div" >
                <div>已处理投诉：</div>
                <div>${extData.handle}条</div>
              <div>
            </div>
          </div>`

          infoWindow = new AMap.InfoWindow({
            offset: new AMap.Pixel(0, 0),
            isCustom: true,
            content: info,
            autoMove: true
            // anchor: 'bottom-center',
          })
          _this.oldlng = e.lnglat.lng
          _this.oldlat = e.lnglat.lat
          infoWindow.open(map, [_this.oldlng, _this.oldlat])
          map.add(oldmarker)
        })
        map.add(marker)
        _this.delMarkerList.push(marker)
      }
      map.plugin(['AMap.Heatmap'], function() {
        if (_this.heatmap) _this.heatmap.setMap(null)
        // 初始化heatmap对象
        _this.heatmap = new AMap.Heatmap(map, {
          radius: 60, // 给定半径
          opacity: [0, 0.8],
          gradient: {
            0.1: 'blue',
            0.3: 'rgb(1,111,248)',
            0.65: 'rgb(117,211,248)',
            0.7: 'rgb(0, 255, 0)',
            0.9: '#ffea00',
            1.0: 'red'
          }
        })
        console.log(heatmapData.map(item => {
          return {
            count: item.count,
            lat: item.lat,
            lng: item.lng }
        }), 'hhhhhhhhh')

        _this.heatmap.setDataSet({
          data: heatmapData.map(item => {
            return {
              count: item.count,
              lat: item.lat,
              lng: item.lng }
          }),
          max: _this.total === 0 ? 100 : _this.total
        }
        )
      })
    },
    getMonthWeek(a, b, c) {
      /**
       * a = d = 当前日期
       * b = 6 - w = 当前周的还有几天过完(不算今天)
       * a + b 的和在除以7 就是当天是当前月份的第几周
       */
      var date = new Date(a, parseInt(b) - 1, c)
      var w = date.getDay()
      var d = date.getDate()
      if (w === 0) {
        w = 7
      }
      var config = {
        getMonth: date.getMonth() + 1,
        getYear: date.getFullYear(),
        getWeek: Math.ceil((d + 6 - w) / 7)
      }
      return config
    },
    // ------------周日历-----------
    async getWeek(time) {
      const _that = this
      const weekDay = this.returnWeekNum(time)
      // 获取到当前自然周周日时间 直接往前推5天
      const end = moment(time)
        .add(weekDay === 0 ? weekDay : 7 - weekDay, 'days')
        .format(this.format) // 周日日期
      const sundayNum = this.returnWeekNum(end)
      const cnIndex = ['日', '一', '二', '三', '四', '五', '六']
      const weeks = [
        {
          time: end,
          cnTime: cnIndex[sundayNum],
          numberTime: moment(end).format('DD')
        }
      ]
      for (let i = 0; i < 7; i++) {
        const startTime = moment(weeks[weeks.length - i - 1].time)
          .subtract(6 - i, 'days')
          .format(this.format) // 周日日期
        weeks.push({
          time: startTime.substring(0, 10),
          cnTime: cnIndex[this.returnWeekNum(startTime)],
          numberTime: moment(startTime).format('DD')
        })
      }
      this.week = []
      // console.log('-------', weeks)
      weeks.splice(0, 1)
      this.courMon =
        weeks[6].time.split('-')[0] + '年' + weeks[6].time.split('-')[1] + '月'
      this.beginTime = weeks[0].time
      this.endTime = weeks[6].time
      this.week.push(...weeks)
      await this.getsamemonth()
      await this.getAccompanyCurrent()
      // console.log('----this.courMon', this.courMon)
      // console.log('-----this.accompanyNum', this.accompanyNum)
      // this.accompanyNum.forEach(ii => {
      //   this.week.forEach(ww => {
      //     if (ww.time === ii.recordTime) {
      //       console.log('--------好的')
      //     }
      //   })
      // })
      const accNum = [{}, {}, {}, {}, {}, {}, {}]
      // accNum = this.accompanyNum
      this.accompanyNum.forEach((ii) => {
        // let isShow = true
        this.week.forEach((ww, index) => {
          if (ww.time === ii.recordTime) {
            _that.previewImageUrlList.push(ii.image)
            accNum[index] = ii
            return
          } else {
            // isShow = true
          }
        })
      })
      // console.log('---previewImageUrlList', _that.previewImageUrlList)
      this.accompanyNum = accNum
      // console.log('---this.week', this.week)
      this.html = ''
      this.htmls = ''
      this.htmlimage = ''
      this.week.forEach((item) => {
        this.html += `<div>${item.cnTime}</div>`
      })
      this.week.forEach((item) => {
        this.htmls += `<div>
                        ${item.numberTime}
                       </div>`
      })
      this.accompanyNum.forEach((item) => {
        if (item.image) {
          _that.htmlimage += `
        <img class='week_image' src=${item.image} onClick="imageBtn('${item.image}')" ></img>
        `
        } else {
          _that.htmlimage += `
        <div class='week_image'>-</div>
        `
        }
      })
      // console.log('-----this.week', this.week)
      // console.log('-----this.html', this.html)
      // console.log('-----this.htmlimage', this.htmlimage)
    },
    // 查看图片
    imageBtn(url) {
      this.$refs.previewImage.showViewer = true
      this.previewImageUrlList = [url]
    },
    returnWeekNum(time) {
      const index = moment(time).format('d')
      return Number(index)
    },
    // 上一周
    async preWeek() {
      const item = this.week[0]
      const startTime = moment(item.time).format(this.format)
      const preTime = moment(startTime).subtract(1, 'days').format(this.format)
      await this.getWeek(preTime)
      // popup_img
      document.querySelector('.popup_conter_time').innerHTML = this.courMon
      document.querySelector('.popup_ones').innerHTML = this.htmls
      document.querySelector('.popup_one').innerHTML = this.html
      document.querySelector('.popup_img').innerHTML = this.htmlimage
    },
    // 下一周
    async nextWeek() {
      const arr = this.week
      const item = arr[arr.length - 1]
      const endTime = moment(item.time).format(this.format)
      const nextTime = moment(endTime).add(6, 'days').format(this.format)
      await this.getWeek(nextTime)
      document.querySelector('.popup_conter_time').innerHTML = this.courMon
      document.querySelector('.popup_ones').innerHTML = this.htmls
      document.querySelector('.popup_one').innerHTML = this.html
      document.querySelector('.popup_img').innerHTML = this.htmlimage
    },
    // 获取当前日期
    async formatDate() {
      const date = new Date()
      var y = date.getFullYear()
      var m = date.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = date.getDate()
      d = d < 10 ? '0' + d : d
      this.selectTime = y + '-' + m + '-' + d
      await this.getWeek(this.selectTime)
    },
    // ------整月日历-----------
    myCanlendar(target) {
      // console.log('---this.monDays', '2020-11-21')
      document.getElementsByTagName('tbody')[0].innerHTML = ''
      target = target.value || target
      // target.value || target
      var dateTemp = target.split('-')
      var currentDate = new Date(dateTemp[0], dateTemp[1], dateTemp[2])
      const dateArr = [
        currentDate.getFullYear(),
        currentDate.getMonth(),
        currentDate.getDate()
      ]
      // log('获取当前日期:' + dateArr.join('-').toString())
      // 获取当前月对应的最大天数
      var maxDayObj = new Date(dateArr[0], dateArr[1], 0)
      var maxDay = maxDayObj.getDate()
      // log('当前月对应的最大天数:' + maxDay)
      // 获取当前日期第一天对应周几
      var firstDayObj = new Date(dateArr[0], dateArr[1] - 1, 1)
      var firstDay = firstDayObj.getDay()
      firstDay = firstDay === 0 ? 7 : firstDay
      // log('获取当前日期第一天对应周几:' + firstDay)
      const isFirstDay = firstDay === 6 || firstDay === 7 ? '1' : false
      // 打印表格
      var spaceOnce = true
      var tab = '<tr>'
      for (var i = 1; i <= maxDay; i++) {
        let isShow = true
        // console.log('-----maxDay', maxDay)
        // 打印最前面的空格子,确保只打印一次
        if (firstDay > 0 && spaceOnce) {
          for (var j = 1; j < firstDay; j++) {
            tab += `<td class=${
              isFirstDay ? 'calendar_tds' : 'calendar_td'
            } >&nbsp</td>`
          }
          spaceOnce = false
        }
        for (const item of this.monDays) {
          if (item.recordTime === i + '') {
            tab +=
              `<td class=${isFirstDay ? 'calendar_tds' : 'calendar_td'} >` +
              '<div>' +
              i +
              '</div>' +
              '<div class=calendar_event>' +
              item.count +
              '</div>' +
              '</td>'
            isShow = false
            break
          }
        }
        if (isShow) {
          tab +=
            `<td class=${isFirstDay ? 'calendar_tds' : 'calendar_td'} >` +
            '<div>' +
            i +
            '</div>' +
            '<div class=calendar_event>' +
            0 +
            '</div>' +
            '</td>'
        }

        if ((i - (8 - firstDay)) % 7 === 0) {
          tab += '</tr><tr>'
        }
        /*
  如果某月的第一天是周日，那么格子总数会有42个，反之35个就够了，这里按42个打印
  // 打印最后几个空格子， tbody总共应有42个格子,-天数 -前面的空格子
 */
        let lastMax = 35
        if (firstDay === 7 || firstDay === 6) {
          lastMax = 42
        }
        // console.log('---isFirstDay', isFirstDay)
        // console.log('---lastMax', lastMax)
        var lastSpace = lastMax - maxDay - (firstDay - 1)
        // log('最后总共有空格:' + lastSpace + ',倒数第二行空格:' + (lastSpace - 7))
        if (i === maxDay && lastSpace > 0) {
          for (var k = 0; k < lastSpace; k++) {
            tab += `<td class=${
              isFirstDay ? 'calendar_tds' : 'calendar_td'
            } ></td>`
            // 减7是计算倒数第二行最后有几个空格,然后换下一行.
            if (lastSpace - 7 > 0 && k + 1 === lastSpace - 7) tab += '<tr></tr>'
          }
        }
      }
      tab += '</tr>'
      // console.log(tab)
      document.getElementsByTagName('tbody')[0].innerHTML = tab
    },
    // -------获取膳食结构------
    getrose() {
      const date = new Date()
      var y = date.getFullYear()
      var m = date.getMonth() + 1
      m = m < 10 ? '0' + m : m
      const d = new Date(y, m, 0).getDate()
      const startTime = y + '-' + m + '-' + '01' + ' ' + '00:00:00'
      // const startTime = '2020-09-22 00:00:00'
      const endTime = y + '-' + m + '-' + d + ' ' + '00:00:00'
      const code = JSON.parse(
        localStorage.getItem('cateenBigDataUserInfo')
      ).districtCode
      const params = {
        // level: 1,
        serviceId: this.currCenter ? this.currCenter : JSON.parse(localStorage.getItem('cateenBigDataUserInfo')).serviceIds[0],
        code,
        startTime,
        endTime
      }
      getRoseData(params).then((res) => {
        this.propData = []
        const data = res.data.data
        const total =
          data.greens +
          data.meat +
          data.fruit +
          data.fish +
          data.oil +
          data.bean +
          data.grain +
          data.milk
        const num1 = total ? parseInt((data.grain / total) * 100) : 0
        const num2 = total ? parseInt((data.greens / total) * 100) : 0
        const num3 = total ? parseInt((data.fruit / total) * 100) : 0
        const num4 = total ? parseInt((data.meat / total) * 100) : 0
        const num5 = total ? parseInt((data.fish / total) * 100) : 0
        const num6 = total ? parseInt((data.milk / total) * 100) : 0
        const num7 = total ? parseInt((data.bean / total) * 100) : 0
        const totals = num1 + num2 + num3 + num4 + num5 + num6 + num7
        const num8 =
          total === 0 ? 0 : totals > 100 || totals === 100 ? 0 : 100 - totals
        this.propData.push(num1)
        this.propData.push(num2)
        this.propData.push(num3)
        this.propData.push(num4)
        this.propData.push(num5)
        this.propData.push(num6)
        this.propData.push(num7)
        this.propData.push(num8)
      })
      // await getRose(data).then(res => {
      //   const roseList = []
      //   res.data.data.forEach(item => {
      //     roseList.push({
      //       value: item.number,
      //       name: item.name
      //     })
      //   })
      //   this.roseList = roseList
      // })
    },
    // -------获取投诉----------
    async getgap() {
      const data = {
        districtCode: JSON.parse(localStorage.getItem('cateenBigDataUserInfo'))
          .districtCode
      }
      await getGap(data).then((res) => {
        const gapList = []
        gapList.push(
          {
            name: '服务问题',
            value: res.data.data.serviceSuggestCount - 0
          },
          {
            name: '菜品问题',
            value: res.data.data.dishesSuggestCount - 0
          },
          {
            name: '其他',
            value: res.data.data.otherSuggestCount - 0
          }
        )
        this.gapList = gapList
        this.gapcountNum = res.data.data.countNum
      })
    },
    // -------获取数据统计----------
    async getstatistics() {
      const data = {
        districtCode: JSON.parse(localStorage.getItem('cateenBigDataUserInfo'))
          .districtCode
      }
      await getStatistics(data).then((res) => {
        const complaintList = []
        const times = []
        const suggestList = []
        const statisticsList = {}
        let fronts = ''
        res.data.data.forEach((item, index) => {
          complaintList.push(item.complaint)
          suggestList.push(item.suggest)
          const front = item.suggestTime.slice(0, 4) - 0
          if (index === 0) {
            fronts = front
            const after = item.suggestTime.slice(5, 7) - 0 + '月'
            const timer = after + '' + ' ' + ''
            times.push(timer)
          } else if (fronts < front) {
            fronts = front
            const after = item.suggestTime.slice(5, 7) - 0 + '月'
            const timer = after + '' + ' ' + '(' + front + '年' + ')'
            times.push(timer)
          } else {
            const after = item.suggestTime.slice(5, 7) - 0 + '月'
            const timer = after + '' + ' ' + ''
            times.push(timer)
          }
        })
        statisticsList.times = times
        statisticsList.complaintList = complaintList
        statisticsList.suggestList = suggestList
        // statisticsList.push(times, complaintList, suggestList)
        this.statisticsList = statisticsList
      })
    },
    // -------获取词云-------
    async getwords() {
      const data = {
        code: JSON.parse(localStorage.getItem('cateenBigDataUserInfo'))
          .districtCode
      }
      await getWords(data).then((res) => {
        const wordsList = []
        res.data.data.forEach((item) => {
          wordsList.push({
            name: item.value,
            value: item.score,
            textStyle: {
              normal: {
                color: `rgba(${parseInt(Math.random() * 255)}, ${parseInt(
                  Math.random() * 255
                )}, ${parseInt(Math.random() * 255)}, 1)`
              }
            }
          })
        })
        this.wordsList = wordsList
      })
    },
    // --------------获取日历--------
    async getmycanlendar() {
      const _that = this
      const data = {
        districtCode: JSON.parse(localStorage.getItem('cateenBigDataUserInfo'))
          .districtCode
      }
      await getmyCanlendar(data).then(async(res) => {
        this.myCanlendarCount = res.data.data.count + ''
        const arr = []
        res.data.data.accompanyNum.forEach((item) => {
          let recordTime = item.recordTime.split('-')[2]
          // console.log('--recordTime', recordTime)
          if (recordTime.substr(0, 1) === '0') {
            const tt = recordTime.split('0')[1]
            recordTime = tt
          }
          const count = item.count
          const obj = {
            recordTime,
            count
          }
          arr.push(obj)
        })
        this.monDays = arr
        await _that.getcount()
        this.myCanlendar(dateStr)
        // console.log('---getmycanlendar', res)
      })
    },
    // --------设置陪餐总数------
    async getcount() {
      const arr = [...this.myCanlendarCount]
      while (arr.length < 4) {
        arr.unshift(0)
      }
      this.caterNum = arr
    },
    async getsamemonth() {
      const { beginTime, endTime } = this
      const data = {
        serviceId: this.currCenter ? this.currCenter : JSON.parse(localStorage.getItem('cateenBigDataUserInfo')).serviceIds[0],
        beginTime,
        endTime
      }
      console.log(22222)
      await getSameMonth(data).then((res) => {
        const datas = res.data.data
        this.accompanyNum = datas.accompanyNum || []
        for (let i = 1; i < 6; i++) {
          if (datas.score > i || datas.score === i) {
            this.score.push(1)
          } else {
            this.score.push(0)
          }
        }
        this.description = datas.description || '暂无评分'
        const dietaryStructure = datas.dietaryDTO
        const total =
          dietaryStructure.greens +
          dietaryStructure.meat +
          dietaryStructure.fruit +
          dietaryStructure.fish +
          dietaryStructure.oil +
          dietaryStructure.bean +
          dietaryStructure.grain +
          dietaryStructure.milk
        const num1 = total
          ? parseInt((dietaryStructure.grain / total) * 100)
          : 0
        const num2 = total
          ? parseInt((dietaryStructure.greens / total) * 100)
          : 0
        const num3 = total
          ? parseInt((dietaryStructure.fruit / total) * 100)
          : 0
        const num4 = total ? parseInt((dietaryStructure.meat / total) * 100) : 0
        const num5 = total ? parseInt((dietaryStructure.fish / total) * 100) : 0
        const num6 = total ? parseInt((dietaryStructure.milk / total) * 100) : 0
        const num7 = total ? parseInt((dietaryStructure.bean / total) * 100) : 0
        const totals = num1 + num2 + num3 + num4 + num5 + num6 + num7
        const num8 =
          Number(totals) === 0 || Number(totals) === 100 || Number(totals) > 0
            ? 0
            : 100 - Number(totals)
        this.dietaryStructures.push(num1 + '%')
        this.dietaryStructures.push(num2 + '%')
        this.dietaryStructures.push(num3 + '%')
        this.dietaryStructures.push(num4 + '%')
        this.dietaryStructures.push(num5 + '%')
        this.dietaryStructures.push(num6 + '%')
        this.dietaryStructures.push(num7 + '%')
        this.dietaryStructures.push(num8 + '%')
      })
    },
    getAccompanyCurrent() {
      const { beginTime, endTime } = this
      const params = {
        serviceId: this.currCenter ? this.currCenter : JSON.parse(localStorage.getItem('cateenBigDataUserInfo')).serviceIds[0],
        beginTime,
        endTime
      }
      console.log(111111)
      // this.getrose()
      getSameMonth(params).then((res) => {
        const data = res.data.data
        this.evaluation.branch = data.score || 0
        this.evaluation.center = data.weekOfMonth || '--'
        this.emptyStar = 5 - this.evaluation.branch
        this.evaluation.pingjia = data.description || '暂无评价'
      })
    },
    // --------获取热力图--------
    gethotmap(selectsData) {
      const _this = this
      const data = {
        centerName: this.search ? this.search : undefined,
        centerType: selectsData ? selectsData.centerType : undefined,
        streetCode: selectsData ? selectsData.streetCode : undefined,
        level: selectsData ? selectsData.level : undefined
      }
      getHotMap(data).then((res) => {
        res.data.data.forEach((item) => {
          item.count = item.unHandle
        })
        this.heatmapData = res.data.data
        console.log(this.heatmapData, 'dddddddddd')
        this.heatmapData.forEach(item => {
          this.total = item.unHandle + item.handle
        })
        if (this.heatmapData.length) {
          this.currCenter = this.heatmapData[0].id
          this.currCenter = this.heatmapData[0].id
        }
        // this.clearMarker()
        this.creatMarker()
      })
    }
  }
}
// window.preWeek = () => {
//   const item = _that.data.week[0]
//   const startTime = moment(item.time).format(_that.format)
//   const preTime = moment(startTime).subtract(1, 'days').format(_that.format)
//   _that.getWeek(preTime)
// }
</script>
<style lang="scss">
.amap-info-sharp {
  display: none;
}
.amap-info-close {
  display: none;
}
.markerClass {
  width: 0.08rem;
  height: 0.08rem;
  box-sizing: border-box;
  /*transform: translate3d(0px, 0px, 0px);*/
  position: absolute;
  top: 0.1rem;
  right: -0.08rem;
  outline: none;
  background-color: rgba(255, 255, 255, 0);
  //  border: solid 0.001rem;
  box-shadow: 0.01rem 0.01rem 0.08rem 0 rgba(0, 0, 0, 0.75);
  border-radius: 100%;
  transform-origin: 0 0;
  display: block;
  opacity: 1;
}
.markerImage {
  background-image: url('../../assets/img/<EMAIL>');
  //  position: absolute;
  margin-top: -0.13rem;
  margin-left: -0.08rem;
  width: 0.24rem;
  height: 0.27rem;
  //  top:0.5rem;
  z-index: 9999999;
}

@keyframes pulsate {
  0% {
    transform: scale(0.1, 0.1);
    opacity: 0;
    filter: alpha(opacity=0);
  }
  50% {
    opacity: 1;
    filter: none;
  }
  100% {
    transform: scale(1.2, 1.2);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@keyframes pulsateimg {
  0% {
    transform: rotateY(180deg);
    //  opacity: 0;
  }
  100% {
    transform: rotateY(0deg);
    //  opacity: 0;
    filter: alpha(opacity=0);
  }
}
</style>

<style lang="scss" scope >
@import url('../../assets/css/index.css');
@import './index.scss';
.font_fiml {
  font-family: myFont;
}
</style>

