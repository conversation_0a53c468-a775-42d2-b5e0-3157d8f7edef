<template>
  <div class="centertab">
    <!-- <div class="title-buttom">
      <img src="@/assets/img/<EMAIL>" alt>
      <span>项目点筛选</span>
    </div> -->
    <div class="selects">
      <!--      <el-select-->
      <!--        v-model="street"-->
      <!--        placeholder="选择街道"-->
      <!--        @change="postSelectsData"-->
      <!--      >-->
      <!--        <el-option-->
      <!--          v-for="item in streetList"-->
      <!--          :key="item.code"-->
      <!--          :value="item.code"-->
      <!--          :label="item.name"-->
      <!--        />-->
      <!--      </el-select>-->
      <!--      <img src="../assets/img/<EMAIL>" alt="" class="jiange" />-->
      <!--      <el-select-->
      <!--        v-model="centerType"-->
      <!--        placeholder="选择项目点类型"-->
      <!--        @change="postSelectsData"-->
      <!--      >-->
      <!--        <el-option-->
      <!--          v-for="item in centerTypeList"-->
      <!--          :key="item.value"-->
      <!--          :label="item.name"-->
      <!--          :value="item.value"-->
      <!--        />-->
      <!--      </el-select>-->
      <!--      <img src="../assets/img/<EMAIL>" alt="" class="jiange" />-->
      <el-select
        v-if="path == '/globalOverview'"
        v-model="riskLevel"
        placeholder=""
        @change="postSelectsData"
      >
        <el-option
          v-for="item in riskLevelList"
          :key="item.value"
          :value="item.value"
          :label="item.name"
        />
      </el-select>
      <img
        v-if="path == '/globalOverview'"
        src="../assets/img/<EMAIL>"
        alt=""
        class="jiange"
      />
      <el-input
        v-model="searchValue"
        placeholder="请输入想要查看的项目点"
        prefix-icon="el-icon-search"
        style="width: 600px"
        select-when-unmatched="true"
        clearable
        @clear="getSearch"
        @keyup.enter.native="getSearch"
      />
    </div>
  </div>
</template>

<script>
import { fetchObtainStreet } from '@/api/common'
import { componentMixin } from '@/components/mixin/componentMixin'
import bus from '@/utils/bus'
export default {
  mixins: [componentMixin],
  data() {
    return {
      path: '',
      centerType: undefined,
      searchValue: '',
      centerTypeList: [
        { name: '全部项目点' },
        { name: '鲁南', value: 0 },
        { name: '小学', value: 1 },
        { name: '初中', value: 2 },
        { name: '高中', value: 3 }
      ],
      street: undefined,
      streetList: [],
      riskLevel: undefined,
      riskLevelList: [
        { name: '全部分类' },
        { name: '低风险', value: 1 },
        { name: '中风险', value: 2 },
        { name: '高风险', value: 3 },
      ],
    }
  },
  created() {
    this.path = this.$route.path
  },
  mounted() {
    fetchObtainStreet(this.DISTRICT_CODE['大邑县']).then((res) => {
      this.streetList = res.data.data
      console.log(res.data.data)
      this.streetList.unshift({ name: '全区' })
    })
  },
  methods: {
    postSelectsData() {
      this.$emit('postSelectsData', {
        centerType: this.centerType,
        streetCode: this.street,
        level: this.riskLevel,
      })
    },
    getSearch() {
      bus.$emit('getSearch', this.searchValue)
    },
  },
}
</script>
<style lang="scss">
.selects {
  display: flex;
  height: 100%;
  align-items: center;
  .jiange {
    margin: 0 0.05rem;
  }
  > div {
    // margin-right: 0.05rem;
  }
  .el-input {
    width: 0.8rem;
    font-size: 0.08rem;
  }
}
</style>
<style scoped lang="scss">
.centertab {
  height: 0.28rem;
  width: 4.02rem;
  position: absolute;
  top: 0.8rem;
  left: 2.8rem;
  border: 1px solid rgba(33, 109, 253, 1);
  background: RGBA(5, 26, 90, 0.5);
  // box-sizing: border-box;
  // padding: 0.05rem;
  .title-buttom {
    display: flex;
    align-items: center;
    width: fit-content;
    padding: 0.04rem;
    background-color: #053482;
    border-radius: 0.03rem;
    opacity: 0.9;
    margin-bottom: 0.05rem;
    > span {
      margin-left: 0.05rem;
    }
  }
  .tabs {
    display: flex;
    justify-content: space-between;
  }
}
</style>
