<template>
  <div id="main">
    <!-- <component-title /> -->
    <div
      class="main-content"
      :style="{ background: tabName == '一厨一档' ? 'none' : '#040B37' }"
    >
      <!-- 左侧内容 -->
      <div
        id="focus_toolTip"
        class="special_focus_toolTip"
        v-html="toolTopbody"
      />
      <div class="left-content box">
        <div class="top-height">
          <div class="top-tab">
            <div
              :class="{ 'top-tab-active': tabName == '一厨一档' }"
              @click="swichTab('一厨一档')"
            >
              一厨一档
            </div>
            <div
              :class="{ 'top-tab-active': tabName == '一人一档' }"
              @click="swichTab('一人一档')"
            >
              一人一档
            </div>
            <div
              :class="{ 'top-tab-active': tabName == '一企一档' }"
              @click="swichTab('一企一档')"
            >
              一企一档
            </div>
          </div>
          <!-- 一厨一档 -->
          <div v-if="tabName == '一厨一档'" class="top-search">
            <input
              ref="input1"
              v-model="search1"
              class="input1"
              type="text"
              placeholder="请输入想要查看的内容"
              @focus="clearinput = true"
              @keyup.enter="getCenterList"
            />
            <span v-if="clearinput" class="clearinput1" @click="Input1Remove" />
            <div @click="getCenterList">
              <img src="../../assets/img/wisdomFoodSafety/search.png" alt />
            </div>
          </div>
          <!-- 一人一档 -->
          <div v-if="tabName == '一人一档'" class="top-search">
            <input
              ref="input2"
              v-model="search2"
              class="input1"
              type="text"
              placeholder="请输入想要查看的内容"
              @focus="clearinput2 = true"
              @keyup.enter="getCanteenStaff"
            />
            <span
              v-if="clearinput2"
              class="clearinput1"
              @click="Input2Remove"
            />
            <div @click="getCanteenStaff">
              <img src="../../assets/img/wisdomFoodSafety/search.png" alt />
            </div>
          </div>
          <!-- 一企一档 -->
          <div v-if="tabName == '一企一档'" class="top-search">
            <input
              ref="input3"
              v-model="search3"
              class="input1"
              type="text"
              placeholder="请输入想要查看的内容"
              @focus="clearinput3 = true"
              @keyup.enter="getSupplierList"
            />
            <span
              v-if="clearinput3"
              class="clearinput1"
              @click="Input3Remove"
            />
            <div @click="getSupplierList">
              <img src="../../assets/img/wisdomFoodSafety/search.png" alt />
            </div>
          </div>
          <div class="line-list">
            <!-- 一厨一档 -->
            <!-- 一人一档 -->
            <template v-if="tabName == '一人一档'">
              <div class="line-item">
                <div>员工总数</div>
                <div style="width: 60%" />
                <div>{{ canteenStaff.allStaff }}人</div>
              </div>
              <div class="line-item">
                <div>管理人员</div>
                <div
                  :style="{
                    width:
                      (canteenStaff.managerStaff / canteenStaff.allStaff) * 60 +
                      '%',
                  }"
                />
                <div>{{ canteenStaff.managerStaff }}人</div>
              </div>
              <div class="line-item">
                <div>普通员工</div>
                <div
                  :style="{
                    width:
                      (canteenStaff.generalStaff / canteenStaff.allStaff) * 60 +
                      '%',
                  }"
                />
                <div>{{ canteenStaff.generalStaff }}人</div>
              </div>
            </template>
            <!-- 一企一档 -->
            <template v-if="tabName == '一企一档'">
              <div class="line-item">
                <div>供应商总数</div>
                <div />
                <div>{{ supplierList.type }}个</div>
              </div>
            </template>
          </div>
        </div>
        <div class="scrool-div" :style="{ height: `calc(100% - 0.8rem)` }">
          <!-- 一厨一档 -->
          <div v-show="tabName == '一厨一档'" class="one-div">
            <div class="one-div-top">
              <div
                :class="{ 'active-top-type': scroolOneType == '鲁南' }"
                class="no-bd-lf"
                @click="switchScroolOneType('鲁南')"
              >
                鲁南
              </div>
              <div
                :class="{ 'active-top-type': scroolOneType == '鲁西/鲁中' }"
                @click="switchScroolOneType('鲁西/鲁中')"
              >
                鲁西/鲁中
              </div>
              <div
                :class="{ 'active-top-type': scroolOneType == '新疆/西北' }"
                class="no-bd-rt"
                @click="switchScroolOneType('新疆/西北')"
              >
                新疆/西北
              </div>
            </div>
            <div v-if="scroolOneType == '鲁南'" class="one-div-bottom">
              <div v-if="centerList.filter(item => nameList1.includes(item.serviceProviceName)).length !== 0" class="one-div-height">
                <div
                  v-for="(item, index) in centerList.filter(item => nameList1.includes(item.serviceProviceName))"
                  :key="index"
                  class="one-div-item"
                  :class="{
                    'one-div-item-active':
                      item.serviceProviceId == centerListDefault,
                  }"
                  @click="checkcenterList(item)"
                >
                  <img src="../../assets/img/wisdomFoodSafety/<EMAIL>" alt />
                  <div>
                    {{
                      item.isHoliday
                        ? item.serviceProviceName + '（已歇业）'
                        : item.serviceProviceName
                    }}
                  </div>
                </div>
              </div>
              <div v-if="centerList.length === 0" class="noData">暂无数据</div>
            </div>
            <div v-if="scroolOneType == '鲁西/鲁中'" class="one-div-bottom">
              <div v-if="centerList.filter(item => nameList2.includes(item.serviceProviceName)).length !== 0" class="one-div-height">
                <div
                  v-for="(item, index) in centerList.filter(item => nameList2.includes(item.serviceProviceName))"
                  :key="index"
                  class="one-div-item"
                  :class="{
                    'one-div-item-active':
                      item.serviceProviceId == centerListDefault,
                  }"
                  @click="checkcenterList(item)"
                >
                  <img src="../../assets/img/wisdomFoodSafety/<EMAIL>" alt />
                  <div>
                    {{
                      item.isHoliday
                        ? item.serviceProviceName + '（已歇业）'
                        : item.serviceProviceName
                    }}
                  </div>
                </div>
              </div>
              <div v-if="centerList.length === 0" class="noData">暂无数据</div>
            </div>
            <div v-if="scroolOneType == '新疆/西北'" class="one-div-bottom">
              <div v-if="centerList.filter(item => nameList3.includes(item.serviceProviceName)).length !== 0" class="one-div-height">
                <div
                  v-for="(item, index) in centerList.filter(item => nameList3.includes(item.serviceProviceName))"
                  :key="index"
                  class="one-div-item"
                  :class="{
                    'one-div-item-active':
                      item.serviceProviceId == centerListDefault,
                  }"
                  @click="checkCenterList(item)"
                >
                  <img src="../../assets/img/wisdomFoodSafety/<EMAIL>" alt />
                  <div>
                    {{
                      item.isHoliday
                        ? item.serviceProviceName + '（已歇业）'
                        : item.serviceProviceName
                    }}
                  </div>
                </div>
              </div>
              <div v-if="centerList.length === 0" class="noData">暂无数据</div>
            </div>
            <div v-else class="one-div-bottom" />
          </div>
          <!-- 一人一档 -->
          <div v-show="tabName == '一人一档'" class="two-div">
            <div class="two-div-top">
              <div
                :class="{ 'active-top-type': scroolOneType == '鲁南' }"
                class="no-bd-lf"
                @click="switchScroolOneType('鲁南')"
              >
                鲁南
              </div>
              <div
                :class="{ 'active-top-type': scroolOneType == '鲁西/鲁中' }"
                @click="switchScroolOneType('鲁西/鲁中')"
              >
                鲁西/鲁中
              </div>
              <div
                :class="{ 'active-top-type': scroolOneType == '新疆/西北' }"
                class="no-bd-rt"
                @click="switchScroolOneType('新疆/西北')"
              >
                新疆/西北
              </div>
            </div>
            <div v-show="scroolOneType == '鲁南'" class="two-div-bottom">
              <el-menu
                class="two-div-height"
                :unique-opened="true"
                :default-active="defaultMenu"
                background-color="transparent"
                @select="menuSelect"
              >
                <div
                  v-for="(item, index) in canteenStaff.canteenStaffList[0].canteenStaffList"
                  :key="index"
                >
                  <div class="two-div-item">
                    <el-submenu
                      v-if="
                        item.staffInfos &&
                          item.staffInfos.length > 0
                      "
                      :index="String(item.companyId)"
                    >
                      <template slot="title">
                        <i class="el-icon-caret-right el-submenu__icon-arrow" />
                        <img
                          class="top-img"
                          src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                          alt
                        />
                        <span slot="title" class="top-text">
                          {{
                            item.isHoliday
                              ? item.companyName + '（已歇业）'
                              : item.companyName
                          }}
                        </span>
                      </template>
                      <el-menu-item-group>
                        <el-menu-item
                          v-for="(child, index1) in item.staffInfos"
                          :key="index1"
                          :index="String(child.personId)"
                        >
                          <img
                            class="top-img-child"
                            src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                            alt
                          />
                          <span
                            slot="title"
                            :style="{ color: '#ABC9FF' }"
                            class="top-text"
                          >{{ child.name }}</span>
                        </el-menu-item>
                      </el-menu-item-group>
                    </el-submenu>
                    <template v-else>
                      <el-menu-item :index="index + 'item'">
                        <i class="el-icon-caret-right el-submenu__icon-arrow" />
                        <img
                          class="top-img"
                          src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                          alt
                        />
                        <span slot="title" class="top-text">
                          {{ item.name }}
                        </span>
                      </el-menu-item>
                    </template>
                  </div>
                </div>
              </el-menu>
            </div>
            <div v-show="scroolOneType == '鲁西/鲁中'" class="two-div-bottom">
              <el-menu
                class="two-div-height"
                :unique-opened="true"
                :default-active="defaultMenu"
                background-color="transparent"
                @select="menuSelect"
              >
                <div
                  v-for="(item, index) in canteenStaff.canteenStaffList.filter(item => nameList2.includes(item.companyName))"
                  :key="index"
                >
                  <div class="two-div-item">
                    <el-submenu
                      v-if="
                        item.staffInfos &&
                          item.staffInfos.length > 0
                      "
                      :index="String(item.companyId)"
                    >
                      <template slot="title">
                        <i class="el-icon-caret-right el-submenu__icon-arrow" />
                        <img
                          class="top-img"
                          src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                          alt
                        />
                        <span slot="title" class="top-text">
                          {{
                            item.isHoliday
                              ? item.companyName + '（已歇业）'
                              : item.companyName
                          }}
                        </span>
                      </template>
                      <el-menu-item-group>
                        <el-menu-item
                          v-for="(child, index1) in item.staffInfos"
                          :key="index1"
                          :index="String(child.personId)"
                        >
                          <img
                            class="top-img-child"
                            src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                            alt
                          />
                          <span
                            slot="title"
                            :style="{ color: '#ABC9FF' }"
                            class="top-text"
                          >{{ child.name }}</span>
                        </el-menu-item>
                      </el-menu-item-group>
                    </el-submenu>
                    <template v-else>
                      <el-menu-item :index="index + 'item'">
                        <i class="el-icon-caret-right el-submenu__icon-arrow" />
                        <img
                          class="top-img"
                          src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                          alt
                        />
                        <span slot="title" class="top-text">
                          {{ item.name }}
                        </span>
                      </el-menu-item>
                    </template>
                  </div>
                </div>
              </el-menu>
            </div>
            <div v-show="scroolOneType == '新疆/西北'" class="two-div-bottom">
              <el-menu
                class="two-div-height"
                :unique-opened="true"
                :default-active="defaultMenu"
                background-color="transparent"
                @select="menuSelect"
              >
                <div
                  v-for="(item, index) in canteenStaff.canteenStaffList.filter(item => nameList3.includes(item.companyName))"
                  :key="index"
                >
                  <div class="two-div-item">
                    <el-submenu
                      v-if="
                        item.staffInfos &&
                          item.staffInfos.length > 0
                      "
                      :index="String(item.companyId)"
                    >
                      <template slot="title">
                        <i class="el-icon-caret-right el-submenu__icon-arrow" />
                        <img
                          class="top-img"
                          src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                          alt
                        />
                        <span slot="title" class="top-text">
                          {{
                            item.isHoliday
                              ? item.companyName + '（已歇业）'
                              : item.companyName
                          }}
                        </span>
                      </template>
                      <el-menu-item-group>
                        <el-menu-item
                          v-for="(child, index1) in item.staffInfos"
                          :key="index1"
                          :index="String(child.personId)"
                        >
                          <img
                            class="top-img-child"
                            src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                            alt
                          />
                          <span
                            slot="title"
                            :style="{ color: '#ABC9FF' }"
                            class="top-text"
                          >{{ child.name }}</span>
                        </el-menu-item>
                      </el-menu-item-group>
                    </el-submenu>
                    <template v-else>
                      <el-menu-item :index="index + 'item'">
                        <i class="el-icon-caret-right el-submenu__icon-arrow" />
                        <img
                          class="top-img"
                          src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                          alt
                        />
                        <span slot="title" class="top-text">
                          {{ item.name }}
                        </span>
                      </el-menu-item>
                    </template>
                  </div>
                </div>
              </el-menu>
            </div>
            <!-- <div v-show="scroolOneType != '鲁南'" class="one-div-bottom" /> -->
          </div>
          <!-- 一企一档 -->
          <div v-show="tabName == '一企一档'" class="three-div">
            <div
              v-if="supplierList.children && (supplierList.children.length !== 0)"
              class="three-div-bottom"
            >
              <div class="three-div-height">
                <div
                  v-for="(item, index) in supplierList.children"
                  :key="index"
                  class="three-div-item"
                  :class="{
                    'three-div-item-active': item.name == supplierListDefault,
                  }"
                  @click="checkSupplierList(item.name)"
                >
                  <img
                    src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                    alt
                  />
                  <div>
                    {{ item.isHoliday ? item.name + '（已歇业）' : item.name }}
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="noData">
              暂无数据
            </div>
          </div>
        </div>
      </div>
      <div class="right-content">
        <!-- 一厨一档 -->
        <div v-if="tabName == '一厨一档'" class="right-one-div">
          <div class="right-one-top">
            <!-- div1 -->
            <div>
              <img src="../../assets/img/<EMAIL>" alt />
              项目信息
            </div>
            <!-- div2 -->
            <div>
              <div class="line-item item-mation">
                <p class="sc-Name">
                  项目名称：
                  <span>{{ schoolName }}</span>
                </p>
                <p class="pri-Name">
                  负责人姓名：
                  <span>{{ principalName }}</span>
                </p>
              </div>
            </div>
            <!-- div3 -->
            <!-- <div>
              <img src="../../assets/img/wisdomFoodSafety/<EMAIL>" alt="">
              风险概览
            </div>-->
            <!-- div4 -->
            <!-- <div>
              <div>
                <div>风险等级</div>
                <div>D</div>
              </div>
              <div>
                <div>动态因素：<span>35分</span></div>
                <div>静态因素：<span>45分</span></div>
              </div>
            </div>-->
            <!-- div5 -->
            <div>
              <img src="../../assets/img/wisdomFoodSafety/<EMAIL>" alt />
              人员管理
            </div>
            <!-- div6 -->
            <div>
              <div>
                <div>总人数</div>
                <div>{{ employees.totle ? employees.totle : 0 }}</div>
              </div>
              <div>
                <div>
                  到岗人员：
                  <span>{{ employees.work ? employees.work : 0 }}人</span>
                </div>
                <div>
                  晨检：
                  <span>{{
                    employees.morningNormal ? employees.morningNormal : 0
                  }}人</span>
                </div>
              </div>
              <div>
                <div>
                  健康证临期：
                  <span>{{ employees.lmpending ? employees.lmpending : 0 }}人</span>
                </div>
                <div>
                  健康证到期：
                  <span>{{ employees.beOverdue ? employees.beOverdue : 0 }}人</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 中间内容 -->
          <div class="right-one-center">
            <!-- 动态监测 -->
            <div class="right-one-center-one">
              <div class="title">
                <img src="../../assets/img/multipleGovernance/bt.png" alt />
                <div>动态监测</div>
              </div>
              <div class="contents box">
                <div class="top">
                  <!--  -->
                  <radarCharts
                    :id="'riskCharts'"
                    :width="'1rem'"
                    :height="'1rem'"
                    :prop-data="monitoring"
                  />
                  <div>
                    <div class="one" @click="showScore2(dynamicMonitoring)">
                      <div>动态风险指数</div>
                      <div>
                        <countTo
                          :start-val="0"
                          :end-val="dynamicMonitoring.score || 0"
                        />
                      </div>
                    </div>
                    <div class="two">
                      <div>综合排行</div>
                      <div>
                        <countTo
                          :start-val="0"
                          :end-val="dynamicMonitoring.ranking || 0"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="bottom">
                  <div>
                    {{ dynamicMonitoring.score &lt; 20 ? '低风险' : dynamicMonitoring.score &lt; 60 ? '中风险' : '高风险' }}
                  </div>
                  <div
                    :style="{
                      'background-image': dynamicMonitoring.background,
                      'background-size': '0.03rem 100%',
                    }"
                  />
                  <div
                    :style="{'color': dynamicMonitoring.score &lt; 20 ? '#60E589' : dynamicMonitoring.score &lt; 60 ? '#FE9F1A' : '#FD4D61' }"
                  >
                    {{ dynamicMonitoring.score || 0 }}分
                  </div>
                  <div>
                    {{ dynamicMonitoring.score &lt; 20 ? '表现良好' : dynamicMonitoring.score &lt; 60 ? '需要改进' : '重点关注' }}
                  </div>
                </div>
              </div>
            </div>
            <!-- 待处理事件 -->
            <div class="right-one-center-two">
              <div class="title">
                <img src="../../assets/img/multipleGovernance/bt.png" alt />
                <div>待处理任务</div>
              </div>
              <div class="contents box">
                <eventCharts
                  v-if="eventData && eventData.total"
                  :id="'eventCharts'"
                  :width="'1.2rem'"
                  :height="'100%'"
                  :prop-data="eventData"
                />
                <div v-else class="no-data">暂无待处理任务</div>
                <div v-if="eventData && eventData.total" class="right-div">
                  <div class="cont-title">
                    <div />
                    未处理任务：{{ eventData.total || 0 }}件
                  </div>
                  <div
                    v-if="handlingEvents.eventLines.length <= 2"
                    class="list"
                  >
                    <div
                      v-for="(item, index) in handlingEvents.eventLines"
                      :key="index"
                      class="list-item"
                    >
                      <img
                        class="left"
                        :src="item.imageUrl ? item.imageUrl : imgmr"
                      />
                      <div class="right">
                        <div>{{ item.content }}</div>
                        <div>{{ item.eventType | eventTypeFilter }}</div>
                        <div>{{ item.createTime.slice(5, 16) }}</div>
                      </div>
                    </div>
                  </div>
                  <Swiper v-else :options="swiperOptionEvent" class="list">
                    <SwiperSlide
                      v-for="(item, index) in handlingEvents.eventLines"
                      :key="index"
                      class="list-item"
                    >
                      <img
                        class="left"
                        :src="item.imageUrl ? item.imageUrl : imgmr"
                      />
                      <div class="right">
                        <div>{{ item.content }}</div>
                        <div>{{ item.eventType | eventTypeFilter }}</div>
                        <div>{{ item.createTime.slice(5, 16) }}</div>
                      </div>
                    </SwiperSlide>
                  </Swiper>
                </div>
              </div>
            </div>
            <!-- 从业人员 -->
            <div class="right-one-center-three">
              <div class="title">
                <img src="../../assets/img/multipleGovernance/bt.png" alt />
                <div>人员工况</div>
              </div>
              <div class="contents box">
                <!-- 晨检正常 -->
                <div>
                  <div class="one">
                    <div>
                      <img
                        src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                        alt
                      />
                      晨检正常
                    </div>
                    <div>
                      {{
                        employees.morningNormal ? employees.morningNormal : 0
                      }}
                      人
                    </div>
                  </div>
                  <div class="two">
                    <div
                      class="line-one"
                      :style="{
                        width: employees.MorningNormalPercentage
                          ? employees.MorningNormalPercentage
                          : '0%',
                      }"
                    />
                  </div>
                </div>
                <!-- 晨检异常 -->
                <el-popover
                  placement="top"
                  :title="'晨检异常人数：' + AbnormalList.length"
                  width="320"
                  trigger="hover"
                >
                  <div class="list-box">
                    <div v-for="(item, index) in AbnormalList" :key="index">
                      <div class="list-icon" />
                      <div class="list-item">
                        {{ item.name + item.bodyTemperature + '℃' }}
                      </div>
                    </div>
                  </div>

                  <div slot="reference">
                    <div class="item">
                      <div class="one">
                        <div>
                          <img
                            src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                            alt
                          />
                          晨检异常
                        </div>
                        <div>
                          {{
                            employees.morningAbnormal
                              ? employees.morningAbnormal
                              : 0
                          }}
                          人
                        </div>
                      </div>
                      <div class="two">
                        <div
                          class="line-two"
                          :style="{
                            width: employees.MorningAbnormalPercentage
                              ? employees.MorningAbnormalPercentage
                              : '0%',
                          }"
                        />
                      </div>
                    </div>
                  </div>
                </el-popover>

                <!-- 培训完成 -->
                <div>
                  <div class="one">
                    <div>
                      <img
                        src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                        alt
                      />
                      培训完成
                    </div>
                    <div>{{ employees.train ? employees.train : 0 }} 人</div>
                  </div>
                  <div class="two">
                    <div
                      class="line-three"
                      :style="{
                        width: employees.trainPercentage
                          ? employees.trainPercentage
                          : '0%',
                      }"
                    />
                  </div>
                </div>
                <!-- 考试合格 -->
                <div>
                  <div class="one">
                    <div>
                      <img
                        src="../../assets/img/wisdomFoodSafety/<EMAIL>"
                        alt
                      />
                      考试合格
                    </div>
                    <div>
                      {{ employees.examination ? employees.examination : 0 }} 人
                    </div>
                  </div>
                  <div class="two">
                    <div
                      class="line-four"
                      :style="{
                        width: employees.examinationPercentage
                          ? employees.examinationPercentage
                          : '0',
                      }"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 下面内容 -->
          <div class="right-one-bottom">
            <!-- 实时预警 -->
            <div class="right-one-bottom-one">
              <div class="title">
                <img src="../../assets/img/multipleGovernance/bt.png" alt />
                <div>告警记录</div>
              </div>
              <div class="contents box">
                <div class="left">
                  <div>
                    <div>
                      <countTo :start-val="0" :end-val="dealWarning.newt" />
                    </div>
                    <div>预警新增</div>
                  </div>
                  <div>
                    <div>
                      <countTo
                        :start-val="0"
                        :end-val="dealWarning.untreated"
                      />
                    </div>
                    <div>未处理数量</div>
                  </div>
                </div>
                <div class="right">
                  <div class="thead">
                    <div>告警类型</div>
                    <div>时间</div>
                    <div>处理</div>
                  </div>
                  <template
                    v-if="dealWarning.some && dealWarning.some.length > 0"
                  >
                    <div v-if="dealWarning.some.length <= 5" class="tbody">
                      <div
                        v-for="(item, index) in dealWarning.some"
                        :key="index"
                        class="tr"
                      >
                        <div>{{ item.typeName ? item.typeName : '-' }}</div>
                        <div>
                          {{
                            item.createTime ? item.createTime.slice(5, 16) : '-'
                          }}
                        </div>
                        <div>
                          <img :src="item.isDisposal ? gou : cha" alt />
                        </div>
                      </div>
                    </div>
                    <Swiper v-else :options="swiperOptionWarning" class="tbody">
                      <SwiperSlide
                        v-for="(item, index) in dealWarning.some"
                        :key="index"
                        class="tr"
                      >
                        <div>{{ item.typeName ? item.typeName : '-' }}</div>
                        <div>
                          {{
                            item.createTime ? item.createTime.slice(5, 16) : '-'
                          }}
                        </div>
                        <div>
                          <img :src="item.isDisposal ? gou : cha" alt />
                        </div>
                      </SwiperSlide>
                    </Swiper>
                  </template>
                  <div
                    v-else
                    class="tbody"
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: center;
                    "
                  >
                    暂无数据
                  </div>
                </div>
              </div>
            </div>
            <!-- 食材管理 -->
            <div class="right-one-bottom-two">
              <div class="title">
                <img src="../../assets/img/multipleGovernance/bt.png" alt />
                <div>菜品管理</div>
              </div>
              <div class="contents box">
                <div class="canteenName">{{ centerName }}食堂</div>
                <div class="teacherScore">
                  <div class="score-title">系统评分</div>
                  <div class="stars">
                    <img
                      v-for="i in starnum"
                      :key="i"
                      src="@/assets/img/stars.png"
                      alt
                      style="width: 0.08rem; height: 0.08rem"
                      class="startImg"
                    />
                  </div>
                </div>
                <div
                  v-if="weeklist.length"
                  @mouseleave="startSwiper('swiper1')"
                  @mouseenter="stopSwiper('swiper1')"
                >
                  <Swiper
                    ref="swiper1"
                    :options="swiperOption"
                    class="weekList"
                  >
                    <SwiperSlide
                      v-for="(item, index) in weeklist"
                      :key="index"
                      class="weekitem"
                    >
                      <div class="itemhead">
                        <div>{{ item.type }}</div>
                        <div>{{ item.canteenMealDescription }}</div>
                        <div>{{ item.dishForDay }}</div>
                      </div>
                      <el-popover placement="top" width="420" trigger="hover">
                        <div class="list-box" style="max-height: 150px">
                          <span
                            v-for="(fooditem, index) in item.cookeryBook"
                            :key="index"
                            class="list-item"
                            style="padding: 0 0.02rem"
                          >{{ fooditem.bookName + ' ' }}</span>
                        </div>
                        <div slot="reference" class="itemcontent">
                          <span
                            v-for="(fooditem, index) in item.cookeryBook"
                            :key="index"
                            style="padding: 0 0.02rem"
                          >{{ fooditem.bookName + ' ' }}</span>
                        </div>
                      </el-popover>
                    </SwiperSlide>
                  </Swiper>
                </div>

                <!-- <div v-for="(item, i) in weeklist" :key="i" class="weekitem">
                    <div class="item-title">{{ item.type }}</div>
                    <div class="item-center">
                      {{ item.canteenMealDescription }}
                    </div>
                    <div class="item-bottom">
                      <div
                        v-for="(fooditem, index) in item.cookeryBook"
                        :key="index"
                        class="food-name"
                      >
                        {{ fooditem.bookName }}
                      </div>
                    </div>
                </div>-->
                <div v-if="weeklist.length === 0" class="no-data">暂无数据</div>
                <!-- <foodManageCharts
                  :id="'foodManageCharts'"
                  :width="'1.8rem'"
                  :height="'100%'"
                  :prop-data="foodManagement"
                />-->
              </div>
            </div>
            <!-- 自查记录 -->
            <div class="right-one-bottom-three">
              <div class="title">
                <img src="../../assets/img/multipleGovernance/bt.png" alt />
                <div>自查记录</div>
              </div>
              <div class="contents box">
                <div v-for="(item, i) in querylist" :key="i" class="query-item">
                  <div class="item-left">
                    <div class="left-title">{{ item.name }}</div>
                    <div class="left-time">{{ item.submitTime }}</div>
                  </div>
                  <div
                    v-if="item.isComplete"
                    class="item-right"
                    @click="detailsView(item.id, item.name)"
                  >
                    <span>
                      查看
                      <br />详情
                    </span>
                  </div>
                  <div v-else class="item-right isNotComplete">
                    <span>
                      暂未
                      <br />提交
                    </span>
                  </div>
                </div>
                <div v-if="querylist.length === 0" class="no-data">
                  暂无记录
                </div>
              </div>
              <!-- <div class="contents box">
                <div>距离本次自查还有：</div>
              <div>-->
              <!-- // day, hour, minute -->

              <!-- <div class="time">
                    {{ self.day && self.day.length > 0 ? self.day[0] : 0 }}
                  </div>s
                  <div class="time">
                    {{ self.day && self.day.length > 0 ? self.day[1] : 0 }}
                  </div>
                  <div class="day">天</div>
                  <div class="time">
                    {{ self.hour && self.hour.length > 0 ? self.hour[0] : 0 }}
                  </div>
                  <div class="time">
                    {{ self.hour && self.hour.length > 0 ? self.hour[1] : 0 }}
                  </div>
                  <div class="hour">小时</div>
                  <div class="time">
                    {{
                      self.minute && self.minute.length > 0 ? self.minute[0] : 0
                    }}
                  </div>
                  <div class="time">
                    {{
                      self.minute && self.minute.length > 0 ? self.minute[1] : 0
                    }}
                  </div>
                  <div class="minute">分</div>
                  <div class="end">结束</div>
                </div>
                <div>
                  <div class="text">
                    <div>未提交</div>
                    <div class="text-one">
                      <countTo
                        :start-val="0"
                        :end-val="self.notSubmitted ? self.notSubmitted : 0"
                      />
                    </div>
                  </div>
                  <div class="lines" />
                  <div class="text">
                    <div>待审核</div>
                    <div class="text-two">
                      <countTo
                        :start-val="0"
                        :end-val="self.unReviewed ? self.unReviewed : 0"
                      />
                    </div>
                  </div>
                  <div class="lines" />
                  <div class="text">
                    <div>合格</div>
                    <div class="text-three">
                      <countTo
                        :start-val="0"
                        :end-val="self.qualified ? self.qualified : 0"
                      />
                    </div>
                  </div>
                  <div class="lines" />
                  <div class="text">
                    <div>不合格</div>
                    <div class="text-four">
                      <countTo
                        :start-val="0"
                        :end-val="self.unQualified ? self.unQualified : 0"
                      />
                    </div>
                  </div>
                </div>
                <div>自查详情：</div>
                <div>
                  <div>许可管理</div>
                  <div>
                    <div>审核状态：</div>
                    <div>进行中</div>
                  </div>
                </div>
              </div>-->
              <el-dialog
                :visible.sync="centerDialogVisible"
                :append-to-body="true"
                center
                class="dialog"
                width="1001px"
              >
                <div slot="title" style="font-size: 0.16rem">
                  <span style="font-size: 0.1rem">{{ diaLogTile }}</span>
                </div>
                <div class="check-result">
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      margin-right: 0.1rem;
                    "
                  >
                    自查人:{{ checkResult.signPerson || '暂无' }}
                  </div>
                  <!-- <div style="display:flex;align-items:center;margin-left:0.1rem;">
                    自查人签名:
                    <img
                      v-if="checkResult.signImage"
                      :src="checkResult.signImage"
                      alt=""
                      style="cursor: pointer;width:0.3rem;margin-left:0.2rem;height:0.5rem;transform: rotate(-90deg);"
                    >
                    <span v-else>暂无</span>
                  </div>-->
                </div>
                <el-table
                  :data="seeData"
                  border
                  min-height="6rem"
                  style="width: 1500px"
                  :span-method="objectSpanMethod"
                >
                  <el-table-column
                    align="center"
                    prop="parentName"
                    label="自查名称"
                    width="200"
                  />
                  <el-table-column
                    align="center"
                    prop="content"
                    label="自查内容"
                    width="400"
                  />
                  <el-table-column
                    align="center"
                    label="自查结果"
                    width="100"
                    prop="checkResult"
                  >
                    <template slot-scope="{ row }">
                      <span
                        style="font-size: 18px"
                        :style="{
                          color: row.checkResult == '√' ? '#26CC6F' : '#E70013',
                        }"
                      >{{ row.checkResult }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    label="是否佐证"
                    width="100"
                    prop="usingImage"
                  >
                    <template slot-scope="{ row }">
                      <span
                        :style="{
                          color: row.usingImage == '√' ? '#26CC6F' : '',
                          fontSize: row.usingImage == '√' ? '18px' : '12px',
                        }"
                      >{{ row.usingImage }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="佐证图片" width="150">
                    <template slot-scope="scope">
                      <div
                        v-if="
                          scope.row.selfCheckRecord &&
                            scope.row.selfCheckRecord.recordImages[0]
                        "
                      >
                        <el-image
                          :src="
                            scope.row.selfCheckRecord.recordImages[0]
                              ? scope.row.selfCheckRecord.recordImages[0].image
                              : ''
                          "
                          :preview-src-list="
                            scope.row.selfCheckRecord.recordImages[0]
                              ? [
                                scope.row.selfCheckRecord.recordImages[0]
                                  .image,
                              ]
                              : []
                          "
                          style="
                            width: 50px;
                            height: 50px;
                            border-radius: 5px;
                            position: relative;
                            top: 5px;
                          "
                          fit="cover"
                        />
                      </div>
                      <span v-else>/</span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-dialog>
            </div>
          </div>
        </div>
        <!-- 一人一档 -->
        <div v-if="tabName == '一人一档'" class="right-two-div">
          <div v-if="staffDetails" class="top">
            <div class="left-info">
              <div class="info1">
                <div
                  class="info-box1"
                  :class="{
                    'info-box1-red': !staffDetails.personnelFile.isNormal,
                  }"
                >
                  <img
                    v-if="staffDetails.personnelFile.isNormal"
                    class="isQualified"
                    src="../../assets/img/<EMAIL>"
                    alt
                  />
                  <img
                    v-else
                    class="isQualified"
                    src="../../assets/img/<EMAIL>"
                    alt
                  />
                  <div class="box-title align-right">个人档案</div>
                  <div class="name">
                    <div>
                      <span class="gray">姓名：</span>
                      <span
                        class="white"
                        v-text="staffDetails.personnelFile.personName"
                      />
                    </div>
                    <div>
                      <span class="gray">年龄：</span>
                      <span
                        class="white"
                        :style="{
                          color:
                            staffDetails.personnelFile.age > 60 ? 'red' : '',
                        }"
                        v-text="staffDetails.personnelFile.age"
                      />
                    </div>
                  </div>
                  <div>
                    <span class="gray">籍贯：</span>
                    <span
                      class="white"
                      v-text="staffDetails.personnelFile.registration"
                    />
                  </div>
                  <div>
                    <span class="gray">健康证期至：</span>
                    <span
                      class="white"
                      v-text="staffDetails.personnelFile.documentExpiredTime"
                    />
                  </div>
                </div>
                <svg
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="0.85rem"
                  class="line"
                >
                  <line
                    x1="0.07rem"
                    y1="0.07rem"
                    x2="0.21rem"
                    y2="0.07rem"
                    :style="{
                      stroke: staffDetails.personnelFile.isNormal
                        ? '#469CED'
                        : '#E31E44',
                    }"
                  />
                  <line
                    x1="0.21rem"
                    y1="0.07rem"
                    x2="0.4rem"
                    y2="0.3rem"
                    :style="{
                      stroke: staffDetails.personnelFile.isNormal
                        ? '#469CED'
                        : '#E31E44',
                    }"
                  />
                  <line
                    x1="0.4rem"
                    y1="0.3rem"
                    x2="0.8rem"
                    y2="0.3rem"
                    :style="{
                      stroke: staffDetails.personnelFile.isNormal
                        ? '#469CED'
                        : '#E31E44',
                    }"
                  />
                </svg>
              </div>
              <div class="info2">
                <div
                  class="info-box2"
                  :class="{
                    'info-box2-red':
                      !staffDetails.isVacation && staffDetails.morningCheckRecord.morningCheckRecordList&&
                      (!staffDetails.morningCheckRecord.isNormal ||
                        !staffDetails.morningCheckRecord
                          .morningCheckRecordList[0]),
                  }"
                >
                  <img
                    v-if="
                      !staffDetails.isVacation &&
                        staffDetails.morningCheckRecord.isNormal && staffDetails.morningCheckRecord.morningCheckRecordList&&
                        staffDetails.morningCheckRecord.morningCheckRecordList[0]
                    "
                    class="isQualified"
                    src="../../assets/img/<EMAIL>"
                    alt
                  />
                  <img
                    v-if="
                      !staffDetails.isVacation &&
                        !staffDetails.morningCheckRecord.isNormal && staffDetails.morningCheckRecord.morningCheckRecordList&&
                        !staffDetails.morningCheckRecord.morningCheckRecordList[0]
                    "
                    class="isQualified"
                    src="../../assets/img/<EMAIL>"
                    alt
                  />
                  <img
                    v-if="staffDetails.isVacation"
                    class="isQualified"
                    src="../../assets/img/yifangjia.png"
                    alt
                  />
                  <div class="box-title align-right">晨检</div>
                  <div
                    v-if="staffDetails.morningCheckRecord.nowDate"
                    class="thead gray"
                  >
                    <div>体温</div>
                    <div>手部</div>
                    <div>疾病申报</div>
                  </div>
                  <div
                    v-if="staffDetails.morningCheckRecord.nowDate"
                    class="tbody white"
                  >
                    <div
                      :class="
                        Number(
                          staffDetails.morningCheckRecord
                            .morningCheckRecordList[0].bodyTemperature
                        ) > 37.3
                          ? 'red'
                          : 'green'
                      "
                    >
                      {{
                        staffDetails.morningCheckRecord
                          .morningCheckRecordList[0].bodyTemperature + '℃'
                      }}
                    </div>
                    <div>正常</div>
                    <div
                      :class="
                        staffDetails.morningCheckRecord
                          .morningCheckRecordList[0].answer != 7
                          ? 'red'
                          : ''
                      "
                    >
                      {{
                        staffDetails.morningCheckRecord
                          .morningCheckRecordList[0].answer == 7
                          ? '正常'
                          : '异常'
                      }}
                    </div>
                  </div>
                  <div v-else>暂无晨检记录</div>
                </div>
                <svg
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="0.85rem"
                  class="line"
                >
                  <line
                    x1="0.07rem"
                    y1="0.07rem"
                    x2="0.75rem"
                    y2="0.07rem"
                    :style="{
                      stroke:
                        !staffDetails.isVacation && staffDetails.morningCheckRecord.morningCheckRecordList&&
                        (!staffDetails.morningCheckRecord.isNormal ||
                          !staffDetails.morningCheckRecord
                            .morningCheckRecordList[0])
                          ? '#E31E44'
                          : '#469CED',
                    }"
                  />
                </svg>
              </div>
              <div class="info3">
                <div
                  class="info-box3"
                  :class="{ 'info-box3-red': staffDetails.faceAlarm > 0 }"
                >
                  <img
                    v-if="staffDetails.faceAlarm <= 0"
                    class="isQualified"
                    src="../../assets/img/<EMAIL>"
                    alt
                  />
                  <img
                    v-else
                    class="isQualified"
                    src="../../assets/img/<EMAIL>"
                    alt
                  />
                  <div class="box-title align-right">行为分析</div>
                  <div>
                    <span>
                      近30天异常行为次数：
                      <span
                        :class="Number(staffDetails.faceAlarm) > 0 ? 'red' : ''"
                      >{{ staffDetails.faceAlarm }}</span>次
                    </span>
                  </div>
                </div>
                <svg
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="0.85rem"
                  class="line"
                >
                  <line
                    x1="0.07rem"
                    y1="0.5rem"
                    x2="0.21rem"
                    y2="0.5rem"
                    :style="{
                      stroke:
                        Number(staffDetails.faceAlarm) === 0
                          ? '#469CED'
                          : '#E31E44',
                    }"
                  />
                  <line
                    x1="0.21rem"
                    y1="0.5rem"
                    x2="0.4rem"
                    y2="0.3rem"
                    :style="{
                      stroke:
                        Number(staffDetails.faceAlarm) === 0
                          ? '#469CED'
                          : '#E31E44',
                    }"
                  />
                  <line
                    x1="0.4rem"
                    y1="0.3rem"
                    x2="0.8rem"
                    y2="0.3rem"
                    :style="{
                      stroke:
                        Number(staffDetails.faceAlarm) === 0
                          ? '#469CED'
                          : '#E31E44',
                    }"
                  />
                </svg>
              </div>
            </div>
            <div class="circle">
              <video
                v-if="staffDetails.risk === 1"
                autoplay
                loop
                name="media"
                src="../../assets/video/webm1.webm"
              />
              <video
                v-if="staffDetails.risk === 2"
                autoplay
                loop
                name="media"
                src="../../assets/video/webm2.webm"
              />
              <video
                v-if="staffDetails.risk === 3"
                autoplay
                loop
                name="media"
                src="../../assets/video/webm3.webm"
              />
              <img
                v-if="staffDetails.personnelFile.faceImage"
                class="avatar"
                :src="staffDetails.personnelFile.faceImage"
                alt
              />
              <img
                v-else
                class="avatar"
                src="../../assets/img/facemr.png"
                alt
              />
              <div class="type">
                <div
                  class="type-name"
                  :class="
                    staffDetails.risk === 1
                      ? 'type-blue'
                      : staffDetails.risk === 2
                        ? 'type-orange'
                        : 'type-red'
                  "
                >
                  {{
                    (staffDetails.risk === 1
                      ? '低风险'
                      : staffDetails.risk === 2
                        ? '关注'
                        : '重点关注') + '人员'
                  }}
                </div>
              </div>
            </div>

            <div class="right-info">
              <div class="info4">
                <svg
                  version="1.1"
                  style="transform: rotateY(180deg)"
                  xmlns="http://www.w3.org/2000/svg"
                  width="0.85rem"
                  class="line"
                >
                  <line
                    x1="0.07rem"
                    y1="0.07rem"
                    x2="0.21rem"
                    y2="0.07rem"
                    :style="{
                      stroke:
                        staffDetails.accessRecord.normal /
                        staffDetails.accessRecord.workDay <
                        0.7
                          ? '#E31E44'
                          : '#469CED',
                    }"
                  />
                  <line
                    x1="0.21rem"
                    y1="0.07rem"
                    x2="0.4rem"
                    y2="0.3rem"
                    :style="{
                      stroke:
                        staffDetails.accessRecord.normal /
                        staffDetails.accessRecord.workDay <
                        0.7
                          ? '#E31E44'
                          : '#469CED',
                    }"
                  />
                  <line
                    x1="0.4rem"
                    y1="0.3rem"
                    x2="0.8rem"
                    y2="0.3rem"
                    :style="{
                      stroke:
                        staffDetails.accessRecord.normal /
                        staffDetails.accessRecord.workDay <
                        0.7
                          ? '#E31E44'
                          : '#469CED',
                    }"
                  />
                </svg>
                <div
                  class="info-box4"
                  :class="{
                    'info-box4-red':
                      staffDetails.accessRecord.normal /
                      staffDetails.accessRecord.workDay <
                      0.7,
                  }"
                >
                  <img
                    v-if="
                      staffDetails.accessRecord.normal /
                        staffDetails.accessRecord.workDay >
                        0.7
                    "
                    class="isQualified"
                    src="../../assets/img/<EMAIL>"
                    alt
                  />
                  <img
                    v-else
                    class="isQualified"
                    src="../../assets/img/<EMAIL>"
                    alt
                  />
                  <div class="box-title">近30天出勤率</div>
                  <div>
                    <span>
                      {{
                        staffDetails.accessRecord.normal /
                          staffDetails.accessRecord.workDay >=
                          1
                          ? '100%'
                          : parseInt(
                            (staffDetails.accessRecord.normal /
                              staffDetails.accessRecord.workDay) *
                              100
                          ) + '%'
                      }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="info5">
                <svg
                  version="1.1"
                  style="transform: rotateY(180deg)"
                  xmlns="http://www.w3.org/2000/svg"
                  width="0.85rem"
                  class="line"
                >
                  <line
                    x1="0.07rem"
                    y1="0.07rem"
                    x2="0.65rem"
                    y2="0.07rem"
                    :style="{
                      stroke:
                        !staffDetails.training.paperList|| staffDetails.training.paperList.length == 0 ||
                        (staffDetails.training.paperList[0].number < 60 &&
                          (staffDetails.training.paperList[1] && staffDetails.training.paperList[1].number < 60))
                          ? '#E31E44'
                          : '#469CED',
                    }"
                  />
                </svg>
                <div
                  class="info-box5"
                  :class="{
                    'info-box5-red':
                      !staffDetails.training.paperList|| staffDetails.training.paperList.length == 0 ||(staffDetails.training.paperList[0].number < 60 && (staffDetails.training.paperList[1] && staffDetails.training.paperList[1].number < 60)),
                  }"
                >
                  <img
                    v-if="
                      staffDetails.training.paperList && staffDetails.training.paperList[0] &&
                        (staffDetails.training.paperList[0].number >= 60 ||
                          (staffDetails.training.paperList[1] && staffDetails.training.paperList[1].number >= 60))
                    "
                    class="isQualified"
                    src="../../assets/img/<EMAIL>"
                    alt
                  />
                  <img
                    v-else
                    class="isQualified"
                    src="../../assets/img/<EMAIL>"
                    alt
                  />
                  <div v-if="staffDetails.training.paperList && staffDetails.training.paperList.length > 0">
                    <div class="box-title">食安考核</div>
                    <div>考试记录</div>
                    <div
                      v-for="item in staffDetails.training.paperList"
                      :key="item.date"
                      class="tbody"
                    >
                      <div>{{ item.date }}</div>
                      <div>{{ item.number }}分</div>
                    </div>
                  </div>
                  <div v-else>
                    <div class="box-title">食安考核</div>
                    <div>暂无考试记录</div>
                  </div>
                </div>
              </div>
              <div class="info6">
                <svg
                  version="1.1"
                  style="
                    transform: rotateY(180deg);
                    position: relative;
                    top: -0.45rem;
                  "
                  xmlns="http://www.w3.org/2000/svg"
                  width="0.85rem"
                  class="line"
                >
                  <line
                    x1="0.07rem"
                    y1="0.5rem"
                    x2="0.21rem"
                    y2="0.5rem"
                    style="stroke: #469ced"
                  />
                  <line
                    x1="0.21rem"
                    y1="0.5rem"
                    x2="0.4rem"
                    y2="0.3rem"
                    style="stroke: #469ced"
                  />
                  <line
                    x1="0.4rem"
                    y1="0.3rem"
                    x2="0.8rem"
                    y2="0.3rem"
                    style="stroke: #469ced"
                  />
                </svg>
                <div class="info-box6">
                  <img
                    class="isQualified"
                    src="../../assets/img/<EMAIL>"
                    alt
                  />
                  <div class="box-title">健康申报</div>
                  <div>正常</div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="staffDetails" class="bottom">
            <div>
              <div class="title">
                <img src="../../assets/img/multipleGovernance/bt.png" alt />
                <div>门禁记录</div>
              </div>
              <div
                class="contents box"
                @mouseleave="startSwiper('acceccRecordsSwiper')"
                @mouseenter="stopSwiper('acceccRecordsSwiper')"
              >
                <div class="thead">
                  <div class="tr">
                    <div class="td">门禁名称</div>
                    <div class="td">开门时间</div>
                    <div class="td">开门图片</div>
                  </div>
                </div>
                <Swiper
                  v-if="staffDetails.accessRecord.accessRecords && staffDetails.accessRecord.accessRecords.length > 0"
                  ref="acceccRecordsSwiper"
                  :options="swiperOptionAccess"
                  class="tbody"
                >
                  <SwiperSlide
                    v-for="(item, index) in staffDetails.accessRecord
                      .accessRecords"
                    :key="index"
                    class="tr"
                  >
                    <div class="td">
                      {{ item.accessName ? item.accessName : '-' }}
                    </div>
                    <div class="td">
                      {{ item.openTime ? item.openTime : '-' }}
                    </div>
                    <div class="td">
                      <img
                        v-if="item.image"
                        class="image"
                        :src="item.image"
                        alt
                        @click="showPreviewImage(item.image)"
                      />
                      <img
                        v-else
                        class="image"
                        src="../../assets/img/imgmr.png"
                        alt
                      />
                    </div>
                  </SwiperSlide>
                </Swiper>
                <div
                  v-else
                  class="tbody"
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  暂无数据
                </div>
              </div>
            </div>
            <div>
              <!-- 晨检记录 -->
              <div class="title">
                <img src="../../assets/img/multipleGovernance/bt.png" alt />
                <div>晨检分析</div>
              </div>
              <div class="contents box">
                <div class="left">
                  <div class="left-one">
                    <div class="echarts">
                      <accessRecordCharts
                        :id="'accessRecordCharts'"
                        :width="'0.6rem'"
                        :height="'0.6rem'"
                        :prop-data="attendance"
                      />
                    </div>
                    <div class="text">晨检通过率</div>
                  </div>
                  <div class="left-two">
                    <div class="box">
                      <div class="left-top" />
                      <div class="right-top" />
                      <div class="left-bottom" />
                      <div class="right-bottom" />
                      晨检正常：{{ staffDetails.morningCheckRecord.normal }}天
                    </div>
                    <div class="box">
                      <div class="left-top" />
                      <div class="right-top" />
                      <div class="left-bottom" />
                      <div class="right-bottom" />
                      晨检异常：{{ staffDetails.morningCheckRecord.abnormal }}天
                    </div>
                  </div>
                </div>
                <div class="lines" />
                <div
                  class="table"
                  @mouseleave="startSwiper('morningCheckSwiper')"
                  @mouseenter="stopSwiper('morningCheckSwiper')"
                >
                  <div class="thead">
                    <div class="tr">
                      <div class="td">晨检时间</div>
                      <div class="td">体温</div>
                      <div class="td">晨检图片</div>
                      <div class="td">状态</div>
                    </div>
                  </div>
                  <Swiper
                    v-if="
                      staffDetails.morningCheckRecord.morningCheckRecordList && staffDetails.morningCheckRecord.morningCheckRecordList
                        .length > 2
                    "
                    ref="morningCheckSwiper"
                    :options="swiperOptionMorning"
                    class="tbody"
                  >
                    <SwiperSlide
                      v-for="(item, index) in staffDetails.morningCheckRecord
                        .morningCheckRecordList"
                      :key="index"
                      class="tr"
                    >
                      <div class="td">
                        {{ item.checkTime ? item.checkTime.slice(5, 16) : '-' }}
                      </div>
                      <div class="td">{{ item.bodyTemperature }}℃</div>
                      <div class="td">
                        <img
                          v-if="item.handsPositive.length > 0"
                          :src="item.handsPositive"
                          class="image"
                          alt
                          @click="showPreviewImage(item.handsPositive)"
                        />
                        <img
                          v-else
                          class="image"
                          src="../../assets/img/imgmr.png"
                          alt
                        />
                        &nbsp;&nbsp;
                        <img
                          v-if="item.handsNegative.length > 0"
                          :src="item.handsNegative"
                          class="image"
                          alt
                          @click="showPreviewImage(item.handsNegative)"
                        />
                        <img
                          v-else
                          class="image"
                          src="../../assets/img/imgmr.png"
                          alt
                        />
                      </div>
                      <div class="td">
                        <div v-if="item.pass == -1" class="yc">异常</div>
                        <div v-if="item.pass == 1" class="zc">正常</div>
                      </div>
                    </SwiperSlide>
                  </Swiper>
                  <div
                    v-if="staffDetails.morningCheckRecord.morningCheckRecordList &&
                      staffDetails.morningCheckRecord.morningCheckRecordList
                        .length <= 2 &&
                      staffDetails.morningCheckRecord.morningCheckRecordList
                        .length > 0
                    "
                    ref="morningCheckSwiper"
                    :options="swiperOptionMorning"
                    class="tbody"
                  >
                    <div
                      v-for="(item, index) in staffDetails.morningCheckRecord
                        .morningCheckRecordList"
                      :key="index"
                      class="tr"
                    >
                      <div class="td">
                        {{ item.checkTime ? item.checkTime.slice(5, 16) : '-' }}
                      </div>
                      <div class="td">{{ item.bodyTemperature }}℃</div>
                      <div class="td">
                        <img
                          v-if="item.handsPositive"
                          :src="item.handsPositive"
                          class="image"
                          alt
                          @click="showPreviewImage(item.handsPositive)"
                        />
                        <img
                          v-else
                          class="image"
                          src="../../assets/img/imgmr.png"
                          alt
                        />
                        &nbsp;&nbsp;
                        <img
                          v-if="item.handsNegative"
                          :src="item.handsNegative"
                          class="image"
                          alt
                          @click="showPreviewImage(item.handsNegative)"
                        />
                        <img
                          v-else
                          class="image"
                          src="../../assets/img/imgmr.png"
                          alt
                        />
                      </div>
                      <div class="td">
                        <div v-if="item.pass == -1" class="yc">异常</div>
                        <div v-if="item.pass == 1" class="zc">正常</div>
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="
                      !staffDetails.morningCheckRecord.morningCheckRecordList || staffDetails.morningCheckRecord.morningCheckRecordList
                        .length === 0
                    "
                    class="tbody"
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: center;
                    "
                  >
                    暂无数据
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 一企一档 -->
        <div v-if="tabName == '一企一档'" class="right-three-div">
          <div class="three-div-left">
            <div class="div-left-top">
              <div class="select-conditions">
                <div class="date-picker">
                  <div>选择日期：</div>
                  <el-date-picker
                    v-model="expiredDate"
                    :picker-options="pickerOptions"
                    size="small"
                    :clearable="false"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :editable="false"
                    @change="getSupplierCircle(true)"
                  />
                </div>
                <div class="ingredients-select">
                  <el-select
                    v-model="ingredientIds"
                    size="large"
                    placeholder="选择或搜索食材"
                    filterable
                    clearable
                    @clear="ingredientIds = undefined"
                    @change="getSupplierCircle(false)"
                  >
                    <el-option
                      v-for="item in ingredientList"
                      :key="item.id"
                      :value="item.otherId"
                      :label="item.name"
                    />
                  </el-select>
                </div>
              </div>
              <div class="chart-box">
                <treeCharts
                  :id="'originToSupplier'"
                  :orient="'RL'"
                  :colors="originToSupplierColors"
                  class="originToSupplier"
                  :width="'680px'"
                  :height="
                    parseInt(ingredientList.length / 10) * 80 + 100 + '%'
                  "
                  :prop-data="originToSupplier"
                  @clickPoint="getPoint"
                />
                <treeCharts
                  :id="'supplierToCenter'"
                  :orient="'LR'"
                  :colors="supplierToCenterColors"
                  class="supplierToCenter"
                  :width="'680px'"
                  :height="
                    parseInt(ingredientList.length / 10) * 80 + 100 + '%'
                  "
                  :prop-data="supplierToCenter"
                  @clickPoint="getPoint"
                />
              </div>

              <div class="legend">
                <div class="item">
                  <div class="icon" />
                  <div class="name">项目点</div>
                </div>
                <div class="item">
                  <div class="icon" />
                  <div class="name">供应商</div>
                </div>
                <div class="item">
                  <div class="icon" />
                  <div class="name">食材</div>
                </div>
                <div class="item">
                  <div class="icon" />
                  <div class="name">菜品</div>
                </div>
                <div class="item">
                  <div class="icon" />
                  <div class="name">产地</div>
                </div>
              </div>
              <!-- <div v-else class="no-tree-data">暂无数据</div> -->
            </div>
          </div>
          <div class="three-div-right" :class="rightState ? '' : 'rightState'">
            <!-- 风险研判 -->
            <div class="three-div-right-one">
              <div class="title" style="margin-left: 44px">
                <img src="../../assets/img/multipleGovernance/bt.png" alt />
                <div>风险研判</div>
                <el-select
                  v-model="day"
                  class="select"
                  placeholder="请选择周期"
                  @change="getRiskAnalysis(null)"
                >
                  <el-option
                    v-for="item in dayList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </div>
              <div class="contents box">
                <div class="shrink" @click="rightState = !rightState">
                  <i
                    :class="
                      rightState ? 'el-icon-arrow-right' : 'el-icon-arrow-left'
                    "
                  />
                </div>
                <div class="charts">
                  <div class="link1">
                    <div class="link-name-left">证件有效期</div>

                    <div
                      :class="
                        new Date().getTime() <
                          new Date(riskAnalysis.papersDate).getTime()
                          ? 'judge-true'
                          : 'judge-false'
                      "
                      v-text="
                        new Date().getTime() <
                          new Date(riskAnalysis.papersDate).getTime()
                          ? '合格'
                          : '不合格'
                      "
                    />
                    <div class="info">{{ '至' + riskAnalysis.papersDate }}</div>
                  </div>
                  <div class="line line1" />
                  <div class="link2">
                    <div class="info">
                      <div v-if="riskAnalysis.origin" class="info-list">
                        暂无风险产地
                      </div>
                      <div
                        v-for="item in riskAnalysis.origin"
                        :key="item"
                        style="text-align: left; padding-left: 0.17rem"
                      >
                        {{ item }}
                      </div>
                      <div v-if="!riskAnalysis.origin">暂无数据</div>
                    </div>
                    <div class="judge-true">
                      {{ riskAnalysis.origin ? '合格' : '不详' }}
                    </div>
                    <div>
                      <div class="link-name-right">风险产地</div>
                      <!-- <div class="risk-place">石家庄市藁城区</div>
                      <div class="risk-place">北京市顺义区</div>
                      <div class="risk-place">沈阳市皇姑区</div>
                      <div class="risk-place">大连市金州区</div>-->
                      <div class="risk-place">国内无风险产地</div>
                    </div>
                  </div>
                  <div class="line line2" />
                  <div class="link3">
                    <div class="link-name-left">食安事件</div>
                    <div class="judge-true">合格</div>
                    <div class="info info-list">无</div>
                  </div>
                  <div class="line line3" />
                  <div class="link4">
                    <div class="info">
                      <div
                        v-for="item in riskAnalysis.ingredientName"
                        :key="item"
                      >
                        {{ item }}
                      </div>
                      <div v-if="!riskAnalysis.ingredientName">无风险食材</div>
                    </div>
                    <div
                      :class="
                        riskAnalysis.ingredientName
                          ? 'judge-false'
                          : 'judge-true'
                      "
                    >
                      {{ riskAnalysis.ingredientName ? '不合格' : '合格' }}
                    </div>
                    <div>
                      <div class="link-name-right">风险食材</div>
                      <div>
                        四季豆
                        <i
                          class="el-icon-circle-close"
                          style="background: #00419e"
                        />
                      </div>
                      <div>
                        黄花菜
                        <i
                          class="el-icon-circle-close"
                          style="background: #00419e"
                        />
                      </div>
                      <div>
                        野生菌
                        <i
                          class="el-icon-circle-close"
                          style="background: #00419e"
                        />
                      </div>
                      <div>...</div>
                    </div>
                  </div>
                </div>
                <div class="result">
                  {{ '参考为“' + riskAnalysis.risk + '”企业' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 图片预览 -->
    <preview-image ref="previewImage" :url-list="previewImageUrlList" />
    <!-- 一企一档树图弹框 -->
    <com-popup ref="comPopup" :suplier="box2Data" :source="box1Data" />
    <!-- 分数详情 -->
    <score-detail
      ref="scoreDetail"
      :cid="cid"
      :score="score"
      :rank="rank"
      class="score-detail"
    />
  </div>
</template>

<script>
import $ from 'jquery'
import moment from 'moment'
import PreviewImage from '@/components/PreviewImage'
import comPopup from './modules/popup'
import { componentMixin } from '@/components/mixin/componentMixin'
import viewMixins from '@/views/mixins'
import { formatDate } from '@/utils/date'
// import componentTitle from '@/components/title'
import ScoreDetail from '@/components/scoreDetail'
import { getDateTime } from '@/utils/index'
import radarCharts from '@/components/Charts/radarCharts'
import foodManageCharts from '@/components/Charts/foodManageCharts'
import eventCharts from '@/components/Charts/eventCharts'
import treeCharts from '@/components/Charts/treeCharts'
import accessRecordCharts from '@/components/Charts/accessRecordCharts'
import countTo from 'vue-count-to'
import gou from '@/assets/img/wisdomFoodSafety/<EMAIL>'
import cha from '@/assets/img/wisdomFoodSafety/<EMAIL>'
import facemr from '@/assets/img/facemr.png'
import imgmr from '@/assets/img/imgmr.png'
import {
  areaCircle,
  supplierCircle,
  supplierList,
  canteenStaff,
  staffDetails,
  foodManagement,
  dealWarning,
  centerList,
  dynamicMonitoring,
  employees,
  handlingEvents,
  fetchRiskAnalysis,
  dishesWeek,
  checkDetails,
  detailsList,
  checkAbnormalDetails
} from '@/api/wisdomFoodSafety'
export default {
  components: {
    PreviewImage,
    // componentTitle,
    radarCharts,
    foodManageCharts,
    eventCharts,
    treeCharts,
    accessRecordCharts,
    countTo,
    comPopup,
    ScoreDetail
  },
  filters: {
    eventTypeFilter(val) {
      console.log(val)
      if (!val) return '其他任务'
      const typeMap = {
        1: '整改',
        2: '日常',
        3: '应急',
        4: '其他'
      }
      return typeMap[val] + '任务'
    }
  },
  mixins: [componentMixin, viewMixins],
  data() {
    return {
      ingredientList: [],
      ingredientIds: '',
      cid: undefined,
      score: 0,
      rank: 0,
      day: 1,
      dayList: [
        { id: 1, name: '当日' },
        { id: 7, name: '一周内' },
        { id: 30, name: '一月内' },
        { id: 365, name: '一年内' }
      ],
      toolTopbody: '',
      centerName: '', // 学校名称
      principalName: '', // 校长名称
      detailsData: {},
      rowData: {},
      checkResult: {},
      seeData: [],
      loading: false, // 自查查看详情
      querylist: [],
      weeklist: [],
      starnum: 4,
      diaLogTile: '',
      imgmr,
      facemr,
      gou,
      cha,
      list: [],
      box1Data: {},
      box2Data: undefined,
      rightState: false,
      // 一企一档
      supplierList: [], // 一企一档列表
      supplierListDefault: '',
      areaStaus: false,
      purchaseList: {}, // 采购记录以及供应商详情
      originToSupplier: {}, // 中间的圆环
      supplierToCenter: {}, // 中间的圆环
      originToSupplierColors: ['#4358C9', '#A26827', '#328E85'], // 中间的圆环
      supplierToCenterColors: ['#4358C9', '#2168C1', '#328E85', '#1381A1'], // 中间的圆环
      // 一人一档
      canteenStaff: [], // 一人一档列表
      defaultMenu: '', // 默认选中员工
      staffDetails: undefined, // 员工详情
      attendance: {}, // 本月出勤率
      // 一校一档
      centerList: [], // 服务商列表
      serviceObj: {},
      centerListDefault: '', // 默认选中服务商
      monitoring: [], // 动态监测雷达图
      dynamicMonitoring: {}, // 动态监测
      background1: 'linear-gradient(to right, #60E589 50%, transparent 0)',
      background2: 'linear-gradient(to right, #FE9F1A 50%, transparent 0)',
      background3: 'linear-gradient(to right, #FD4D61 50%, transparent 0)',
      dealWarning: {
        some: []
      }, // 实时预警
      foodManagement: {
        bottomList: [],
        ins: [],
        lose: []
      }, // 食材管理
      self: {
        srlfList: []
      }, // 自查记录
      employees: {}, // 从业人员
      GRADE: {
        1: '差',
        2: '一般',
        3: '较好',
        4: '好',
        5: '很好'
      },
      eventData: {},
      handlingEvents: {
        eventLines: []
      }, // 待处理事件

      previewImageUrlList: [],
      tabName: '一厨一档',
      search1: '', // 一厨一档
      search2: '', // 一人一档
      search3: '', // 一企一档
      scroolHight: 0,
      scroolOneType: '鲁南',
      warning: [],
      swiperOptionWarning: {
        direction: 'vertical',
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        slidesPerView: 5,
        slidesPerGroup: 1,
        loop: true,
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        }
      },
      swiperOption: {
        direction: 'vertical',
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: false,
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        }
      },
      swiperOptionEvent: {
        direction: 'vertical',
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: true,
        spaceBetween: 10,
        autoplay: {
          delay: 3500,
          disableOnInteraction: false
        }
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      treeDate: undefined,
      expiredDate: undefined,
      treeNum: 0,
      swiperOptionMorning: {
        direction: 'vertical',
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        slidesPerView: 3,
        slidesPerGroup: 1,
        loop: true,
        on: {
          click: (v) => {
            const src = v.target.getAttribute('src')
            if (src) {
              this.showPreviewImage(src)
            }
          }
        },
        autoplay: {
          delay: 2000,
          disableOnInteraction: false
        }
      },
      swiperOptionAccess: {
        direction: 'vertical',
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        slidesPerView: 3,
        slidesPerGroup: 1,
        loop: true,
        on: {
          click: (v) => {
            const src = v.target.getAttribute('src')
            if (src) {
              this.showPreviewImage(src)
            }
          }
        },
        autoplay: {
          delay: 2000,
          disableOnInteraction: false
        }
      },
      riskAnalysis: undefined,
      centerDialogVisible: false,
      clearinput: false,
      clearinput2: false,
      clearinput3: false,
      AbnormalList: [],
      nameList1: ['荣信化工餐厅', '内蒙古矿业项目部', '兖矿能源餐厅', '迎宾楼餐厅', '金斗山餐厅', '未来能源餐厅'],
      nameList2: ['会宝岭餐厅', '唐口煤矿餐厅', '泰山学院新生二部', '时代大厦项目部', '长城三矿餐厅', '鲁西新区项目部'],
      nameList3: ['新疆能源总部餐厅', '海天酒店项目部']
    }
  },
  created() {
    this.getcheckAbnorma()
    this.treeDate = getDateTime(this.treeNum).times
    // this.expiredDate = formatDate(
    //   new Date(getDateTime(this.treeNum).dataTime),
    //   'yyyy-MM-dd'
    // )
    this.expiredDate = formatDate(new Date(), 'yyyy-MM-dd')
  },
  mounted() {
    setTimeout(() => {
      this.scroolHight =
        document.querySelector('.top-height').offsetHeight + 'px'
      this.getWeekmenu()
      this.getSelfinspect()
    }, 500)
    this.getCanteenStaff()
    this.getCenterList()
  },
  methods: {
    // 显示得分详情
    showScore2(data) {
      this.cid = data.serviceId
      this.score = data.score
      this.rank = data.ranking
      this.$refs.scoreDetail.showScoreDetail = true
    },
    // 获取晨检异常数据
    getcheckAbnorma() {
      const data = {
        serviceId: this.centerListDefault,
        date: moment(new Date().getTime()).format('YYYY-MM-DD')
      }
      checkAbnormalDetails(data).then((res) => {
        this.AbnormalList = res.data.data
      })
    },
    itemMouseout() {
      const focusTooltip = $('#focus_toolTip')
      // this.showbox = false
      // setTimeout(() => {
      focusTooltip.css('display', 'none')
      // })
    },
    // 搜索学校
    selectcenter1() {
      const value1 = this.$refs.input1.value
      const newlist = this.centerList.filter((item) => {
        return item.serviceProviceName === value1
      })
      if (newlist.length === 0) {
        this.centerList = ''
      }
      this.centerList = newlist
      this.clearinput = false
    },
    // 回车搜索
    enterselect() {
      this.clearinput = false
      this.selectcenter1()
    },
    enterselect2() {
      this.clearinput2 = false
    },
    enterselect3() {
      this.clearinput3 = false
    },
    // 搜索框监听
    selectCon() {
      if (this.$refs.input1.value) {
        this.clearinput = true
      }
      if (this.$refs.input1.value === '') {
        this.clearinput = false
      }
    },
    selectContwo() {
      if (this.$refs.input2.value) {
        this.clearinput2 = true
      }
      if (this.$refs.input2.value === '') {
        this.clearinput2 = false
      }
    },
    selectConthree() {
      if (this.$refs.input3.value) {
        this.clearinput3 = true
      }
      if (this.$refs.input3.value === '') {
        this.clearinput3 = false
      }
    },
    // 清空input
    Input1Remove() {
      this.search1 = undefined
      this.clearinput = false
      this.getCenterList()
    },
    Input2Remove() {
      this.search2 = undefined
      this.clearinput2 = false
      this.getCanteenStaff()
    },
    Input3Remove() {
      this.search3 = undefined
      this.clearinput3 = false
      this.getSupplierList()
    },
    // 表格合并列
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (row.mergeIndex) {
          return {
            rowspan: row.mergeIndex,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    // 获取自查列表
    getSelfinspect() {
      const obj = {
        date: formatDate(new Date(), 'yyyy-MM-dd'),
        serviceId: this.centerListDefault
      }
      detailsList(obj).then((res) => {
        this.querylist = res.data.data
      })
    },
    // 获取本周菜单
    getWeekmenu() {
      const obj = {
        serviceId: this.centerListDefault
      }
      dishesWeek(obj).then((res) => {
        this.weeklist = res.data.data[0] ? res.data.data[0].cdd : []
        // this.starnum = res.data.data[0] ? res.data.data[0].score: 4
      })
    },
    // 查看详情
    detailsView(id, diaLogTile) {
      this.diaLogTile = diaLogTile
      this.centerDialogVisible = true
      const obj = {
        id,
        serviceId: this.centerListDefault
      }
      checkDetails(obj).then((res) => {
        const detailArr = []
        this.detailsData = res.data.data
        this.detailsData.checkItems.forEach((item, index) => {
          item.checkItems.forEach((child, cellIndex) => {
            const obj = {
              id: child.selfCheckRecord.id,
              parentName: item.name,
              content: child.name,
              checkResult:
                child.selfCheckRecord.checkResult === 'N' ? '√' : '/',
              usingImage: child.usingImage ? '√' : '/',
              images: [],
              selfCheckRecord: child.selfCheckRecord,
              authResult: child.selfCheckRecord.authResult,
              isEvent: child.isEvent,
              mergeIndex: undefined
            }
            // console.log(child.selfCheckRecord, child.selfCheckRecord.recordImages.length, 11111)
            if (cellIndex === 0) {
              obj.mergeIndex = item.checkItems.length
            }
            if (
              child.selfCheckRecord.length &&
              child.selfCheckRecord.recordImages.length
            ) {
              obj.images = child.selfCheckRecord.recordImages.map((imgObj) => {
                return imgObj.image
              })
            }
            detailArr.push(obj)
          })
        })
        this.checkResult = res.data.data.checkResult
        this.seeData = detailArr
        this.loading = false
        this.show = true
      })
    },
    // 切换列表
    swichTab(text) {
      this.tabName = text
      setTimeout(() => {
        this.scroolHight =
          document.querySelector('.top-height').offsetHeight + 'px'
      }, 100)
    },
    // 切换一校一档类型
    switchScroolOneType(text) {
      this.scroolOneType = text
    },
    /*
     * 一企一档
     */
    // 树状图切换日期
    checkDate(text) {
      if (text === 'left') {
        const num = --this.treeNum
        this.treeDate = getDateTime(num).times
        this.expiredDate = formatDate(
          new Date(getDateTime(num).dataTime),
          'yyyy-MM-dd'
        )
        if (this.areaStaus) {
          this.getAreaCircle()
        } else {
          this.getSupplierCircle(true)
        }
      } else if (text === 'right' && this.treeNum < 0) {
        const num = ++this.treeNum
        this.treeDate = getDateTime(num).times
        this.expiredDate = formatDate(
          new Date(getDateTime(num).dataTime),
          'yyyy-MM-dd'
        )
        if (this.areaStaus) {
          this.getAreaCircle()
        } else {
          this.getSupplierCircle(true)
        }
      }
    },
    // 供应商列表
    getSupplierList() {
      const obj = {
        name: this.search3 ? this.search3 : undefined,
        serviceIds: JSON.parse(localStorage.getItem('cateenBigDataUserInfo')).serviceIds,
      }
      supplierList(obj).then((res) => {
        const data = res.data.data
        this.supplierList = data
        this.supplierListDefault = data.children[5]
          ? data.children[5].name
          : data.children[0].name
        // this.supplierListDefault = '金裕达膳食管理服务有限公司'
        this.getSupplierCircle(true)
        this.getRiskAnalysis()
      })
    },
    // 风险研判
    getRiskAnalysis(name) {
      const params = {
        name: name || this.supplierListDefault,
        day: this.day
      }
      fetchRiskAnalysis(params).then((res) => {
        this.riskAnalysis = res.data.data
      })
    },
    // 供应商列表选择
    checkSupplierList(name) {
      this.areaStaus = false
      this.supplierListDefault = name
      this.getSupplierCircle(true)
      this.getRiskAnalysis(name)
    },
    // 供应商为主体的圆圈
    getSupplierCircle(changeIngredientList) {
      const obj = {
        expiredDate: this.expiredDate,
        name: this.supplierListDefault,
        ingredientIds: this.ingredientIds || undefined
      }
      supplierCircle(obj).then((res) => {
        const data = res.data.data
        this.supplierToCenter = data.dishes
        this.originToSupplier = data.origin
        // console.log('食品溯源数据', data.origin)
        // console.log('食品溯源数据id', data.origin.children[0].children.map(ele => ele.id))
        if (changeIngredientList) {
          this.ingredientList = data.ingredientList
        }
      })
    },
    // 区为主体的圆圈
    getAreaCircle() {
      this.areaStaus = true
      const obj = {
        expiredDate: this.expiredDate
      }
      areaCircle(obj).then((res) => {
        const data = res.data.data
        this.supplierCircle = data
      })
    },
    /*
     * 一人一档
     */
    // 食堂员工列表
    getCanteenStaff() {
      const obj = {
        name: this.search2 ? this.search2 : undefined
      }
      canteenStaff(obj).then((res) => {
        const data = res.data.data
        this.canteenStaff = data
        this.defaultMenu = String(
          data.canteenStaffList[2]
            ? data.canteenStaffList[2].staffInfos?.[0].personId
            : data.canteenStaffList[0].staffInfos[0].personId
        )
        this.getStaffDetails()
      })
    },
    // 一人一档搜索员工
    selectpeople() {
      const value2 = this.$refs.input2.value
    },
    // 一企一档搜索企业
    selectcompany() {
      const value3 = this.$refs.input3.value
      const newlist = this.supplierList.children.filter((item) => {
        return item.name === value3
      })
      if (newlist.length === 0) {
        this.supplierList.children = ''
      }
      this.supplierList.children = newlist
      this.clearinput = false
    },
    // 菜单激活回调
    menuSelect(val) {
      console.log(val)
      this.defaultMenu = val
      this.getStaffDetails()
    },
    // 员工详情
    getStaffDetails() {
      const obj = {
        canteenPersonId: this.defaultMenu
      }
      staffDetails(obj).then((res) => {
        this.$nextTick(() => {
          const data = res.data.data
          data.training.percentage = parseInt(
            (data.training.carryOutScore / data.training.examScore) * 100
          )
          this.staffDetails = data
          const total =
            data.morningCheckRecord.normal + data.morningCheckRecord.abnormal
          this.attendance = {
            normal: data.morningCheckRecord.normal,
            workDay: total,
            percentage:
              total !== 0
                ? parseInt((data.morningCheckRecord.normal / total) * 100) + '%'
                : '无记录'
          }
        })
      })
    },
    /*
     * 一厨一档
     */
    //  所有学校的服务商
    getCenterList() {
      const obj = {
        name: this.search1 ? this.search1 : undefined
      }
      centerList(obj).then((res) => {
        const data = res.data.data
        this.centerName = data.centerJsonDTOList[0].name
        this.principalName = data.centerJsonDTOList[0].centerUser
        this.centerList = data.centerJsonDTOList
        this.centerListDefault = data.centerJsonDTOList[0].serviceProviceId
        this.serviceObj = {
          total: data.number,
          A: 0,
          B: 0,
          C: 0,
          D: 0
        }
        this.serviceObj.A = data.number - 2 <= 0 ? 0 : data.number - 2
        this.serviceObj.B =
          data.number - 2 - this.serviceObj.A <= 0
            ? 0
            : data.number - 2 - this.serviceObj.A
        this.serviceObj.C =
          data.number - 2 - this.serviceObj.A - this.serviceObj.B <= 0
            ? 0
            : data.number - 2 - this.serviceObj.A - this.serviceObj.B
        this.serviceObj.D =
          data.number -
            this.serviceObj.A -
            this.serviceObj.B -
            this.serviceObj.C <=
          0
            ? 0
            : data.number -
              this.serviceObj.A -
              this.serviceObj.B -
              this.serviceObj.C
        this.getDealWarning()
        this.getFoodManagement()
        // this.getSelf()
        this.getEmployees()
        this.getHandlingEvents()
        this.getDynamicMonitoring()
        this.getSupplierList()
      })
    },
    // 切换服务商
    checkCenterList(item) {
      this.centerListDefault = item.serviceProviceId
      this.centerName = item.name
      this.principalName = item.centerUser
      this.getDealWarning()
      this.getFoodManagement()
      // this.getSelf()
      this.getEmployees()
      this.getHandlingEvents()
      this.getWeekmenu()
      this.getSelfinspect()
      this.getcheckAbnorma()
      this.getDynamicMonitoring()
    },
    // 动态监测
    getDynamicMonitoring() {
      const obj = {
        serviceId: this.centerListDefault,
        date: formatDate(new Date(getDateTime(-1).dataTime), 'yyyy-MM-dd')
      }
      dynamicMonitoring(obj).then((res) => {
        if (res.data.data) {
          const data = res.data.data
          this.dynamicMonitoring = data
          this.dynamicMonitoring.background =
            data.score < 20
              ? this.background1
              : data.score < 60
                ? this.background2
                : this.background3
          const obj = {
            1: '环境',
            2: '食材',
            3: '事件',
            4: '人员',
            5: '证件'
          }
          const indicator = []
          const indicatorData = []
          for (const item of data.list) {
            indicator.push({
              name: obj[item.type],
              max: data.score ? data.score : 100
            })
            indicatorData.push(item.score)
          }
          this.monitoring = { indicator, data: indicatorData }
        } else {
          this.dynamicMonitoring = {}
          this.monitoring = {}
        }
      })
    },
    // 自查记录
    // getSelf() {
    //   const obj = {
    //     serviceId: this.centerListDefault,
    //     date: formatDate(new Date(getDateTime(-1).dataTime), 'yyyy-MM-dd')
    //   }
    //   self(obj).then(res => {
    //     if (res.data.data) {
    //       const data = res.data.data
    //       console.log(data, 656565)
    //       data.day = this.getRemainderTime(data.endTime, data.beginTime).day
    //       data.hour = this.getRemainderTime(data.endTime, data.beginTime).hour
    //       data.minute = this.getRemainderTime(
    //         data.endTime,
    //         data.beginTime
    //       ).minute
    //       this.self = data
    //     } else {
    //       this.self = {
    //         srlfList: []
    //       }
    //     }
    //   })
    // },
    // 获取时间差
    getRemainderTime(endTime, startTime) {
      var s1 = new Date(startTime.replace(/-/g, '/'))
      var s2 = new Date(endTime)
      var runTime = parseInt((s2.getTime() - s1.getTime()) / 1000)
      var year = Math.floor(runTime / 86400 / 365)
      runTime = runTime % (86400 * 365)
      var month = Math.floor(runTime / 86400 / 30)
      runTime = runTime % (86400 * 30)
      var day = Math.floor(runTime / 86400)
      runTime = runTime % 86400
      var hour = Math.floor(runTime / 3600)
      runTime = runTime % 3600
      var minute = Math.floor(runTime / 60)
      runTime = runTime % 60
      var second = runTime
      year = year < 10 ? '0' + year : year
      month = month < 10 ? '0' + month : month
      day = day < 10 ? '0' + day : day
      hour = hour < 10 ? '0' + hour : hour
      minute = minute < 10 ? '0' + minute : minute
      second = second < 10 ? '0' + second : second
      return { year, month, day, hour, minute, second }
    },
    // 实时预警
    getDealWarning() {
      const obj = {
        serviceId: this.centerListDefault
      }
      dealWarning(obj).then((res) => {
        const data = res.data.data
        this.dealWarning = data
      })
    },
    // 食材管理
    getFoodManagement() {
      const obj = {
        serviceId: this.centerListDefault,
        date: formatDate(new Date(getDateTime(-1).dataTime), 'yyyy-MM-dd')
      }
      foodManagement(obj).then((res) => {
        const data = res.data.data
        const foodManagements = {
          bottomList: [],
          ins: [],
          lose: []
        }
        for (const item of data) {
          // foodManagements.bottomList.push(item.date.slice(8, 10) + '日' + '\n周' + item.week.slice(-1))
          foodManagements.bottomList.push(
            item.date.slice(8, 10) + '日' + '\n' + item.week
          )
          foodManagements.ins.push(item.ins)
          foodManagements.lose.push(item.lose)
        }
        this.foodManagement = foodManagements
      })
    },
    // 从业人员
    getEmployees() {
      const obj = {
        serviceId: this.centerListDefault,
        // date: formatDate(new Date(getDateTime(-1).dataTime), 'yyyy-MM-dd')
        date: formatDate(new Date(), 'yyyy-MM-dd')
      }
      employees(obj).then((res) => {
        const data = res.data.data
        data.MorningNormalPercentage =
          parseInt((data.morningNormal / data.totle) * 100) + '%'
        data.MorningAbnormalPercentage =
          parseInt((data.morningAbnormal / data.totle) * 100) + '%'
        data.trainPercentage = parseInt((data.train / data.totle) * 100) + '%'
        data.examinationPercentage =
          parseInt((data.examination / data.totle) * 100) + '%'
        this.employees = data
        console.log(this.employees, 'dddddddddddd')
      })
    },
    // 待处理事件
    getHandlingEvents() {
      const obj = {
        serviceId: this.centerListDefault,
        type: 4
      }
      handlingEvents(obj).then((res) => {
        const data = res.data.data
        if (data) {
          const eventData = {
            data: [
              { value: data.giveAlarm ? data.giveAlarm : 0, name: '整改' },
              { value: data.complaint ? data.complaint : 0, name: '日常' },
              { value: data.self ? data.self : 0, name: '应急' },
              { value: data.other ? data.other : 0, name: '其他' }
            ],
            total: data.giveAlarm + data.complaint + data.self + data.other
          }
          this.eventData = eventData
          this.handlingEvents = data
        } else {
          this.eventData = undefined
        }
      })
    },
    // 树图点击事件
    getPoint(data) {
      // 点击菜品
      if (data.data.envenType === 5) {
        this.box1Data = {
          bid: Number(data.data.otherId),
          dishForDay: this.expiredDate
        }
        // this.$refs.comPopup.box1Data = this.box1Data
        this.$refs.comPopup.showBox1 = true
      }
      // 点击供应商
      if (data.data.envenType === 3) {
        this.box2Data = Number(data.data.id)
        this.$refs.comPopup.showBox2 = true
      }
    }
  }
}
</script>

<style lang="scss">
#main {
  ::-webkit-scrollbar {
    display: none;
  }
}
.ingredients-select {
  .el-select {
    border: 1px solid #175ad8;
    color: #abc9ff;
    box-shadow: inset 0 0 20px #0a339a;
  }
  ::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #abc9ff;
  }

  ::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #abc9ff;
  }

  :-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #abc9ff;
  }
}
.select-conditions {
  .el-icon-date:before {
    font-size: 0.1rem;
    color: #13ecff;
  }
  .el-input__inner {
    padding-left: 0.2rem;
    font-size: 0.09rem;
    color: #13ecff !important;
  }
}
.left-content {
  .el-menu {
    border-right: none !important;
  }
  .el-menu-item:focus,
  .el-menu-item:hover {
    background: rgba(20, 61, 131, 0.54) !important;
    box-shadow: inset 0 0 10px #0936b0 !important;
  }
  .el-submenu__title:hover {
    background: rgba(20, 61, 131, 0.54) !important;
    box-shadow: inset 0 0 10px #0936b0 !important;
  }
  .el-menu-item,
  .el-submenu__title {
    height: 0.2rem;
    line-height: 0.2rem;
  }
  .el-menu-item-group__title {
    display: none !important;
  }
  .el-icon-arrow-down {
    display: none !important;
  }
  .el-submenu__icon-arrow {
    position: unset;
    margin-top: 0;
  }
  .el-submenu.is-opened > .el-submenu__title .el-submenu__icon-arrow {
    transform: rotateZ(90deg);
  }
  .el-menu-item.is-active {
    background: rgba(20, 61, 131, 0.54) !important;
    box-shadow: inset 0 0 10px #0936b0 !important;
  }
}
.div-left-bottom-left {
  .el-input {
    box-shadow: inset 0 0 0.1rem rgba(20, 77, 134, 1);
    background: #020e46 !important;
    color: #baceeb;
    border: 1px solid #000;
    height: 0.17rem !important;
    box-sizing: border-box;
    border-radius: 5px;
  }
  .el-picker-panel {
    background: #072564;
    border: none;
    color: #baceeb;
  }
  .el-date-table td.next-month,
  .el-date-table td.prev-month {
    color: #7b819f;
  }
  .el-date-picker__header-label {
    color: #409eff;
  }
  .el-date-table td.disabled div {
    background: rgba(3, 3, 29, 0.5) !important;
  }
  .el-picker-panel__icon-btn {
    color: #ccc;
  }
  .el-date-table th {
    color: #baceeb;
  }
}
.el-dialog {
  color: #f9f9f9;
  .el-dialog__header {
    color: #f9f9f9;
  }
}
.check-result {
  margin-bottom: 15px;
  > div {
    > span {
      color: #f9f9f9;
    }
  }
  color: #f9f9f9;
}
</style>

<style lang="scss" scoped>
@import url('../../assets/css/index.css');
@import './index.scss';
</style>

