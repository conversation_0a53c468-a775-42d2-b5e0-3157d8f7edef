<template>
  <div class="content">
    <div id="logo">
      <h1 class="hogo">
        <i>山东能源发展服务集团智慧食安监管平台</i>
      </h1>
    </div>
    <section class="stark-login">
      <div class="form">
        <div id="fade-box">
          <input
            v-model="username"
            type="text"
            maxlength="16"
            placeholder="请输入用户名"
            @keyup.enter="login"
          >
          <!-- <div class="msg" :class="{show: noUserName}">请输入用户名</div> -->
          <div style="height:19px" />
          <input
            v-model="password"
            type="password"
            maxlength="16"
            placeholder="请输入密码"
            class="marginBot20"
            @keyup.enter="login"
          >
          <div v-if="base64Code" style="height:19px">  <input v-model="code" type="code" placeholder="请输入验证码" maxlength="4" style="width:192px;float:left;margin: 0 45px 15px 45px" @keyup.enter="login">
            <img :src="`data:image/jpg;base64,${base64Code}`" alt="正在加载" class="code" @click="getCode"></div>

          <!-- <div class="msg" v-show="noCode">请输入验证码</div> -->
          <!-- <div v-show="!noCode" style="height:19px"></div> -->
          <button :loading="loading" class="mousePointer" @click="login">登 录</button>
        </div>
      </div>
      <div class="hexagons">
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <br>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <br>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>

        <br>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <br>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
        <span>&#x2B22;</span>
      </div>
    </section>

    <div id="circle1">
      <div id="inner-cirlce1">
        <h2 />
      </div>
    </div>
    <ul>
      <li />
      <li />
      <li />
      <li />
      <li />
    </ul>
  </div>
</template>

<script>
import { getValidateCode, login, configurationInfo, getPublicKey } from '@/api/login.js'
import { fetchUserInfo } from '@/api/common'
import JSEncrypt from 'jsencrypt'
export default {
  name: 'Login',
  data() {
    return {
      // noUserName: false,
      noPassword: false,
      noCode: false,
      username: '',
      password: '',
      code: '',
      base64Code: '',
      times: undefined,
      loading: false,
      publicKey: ''
    }
  },
  // watch: {
  //   username: (newVal, oldVal) => {
  //     newVal ? this.noUserName = false : this.noUserName = true
  //   }
  // },
  mounted() {
    this.getConfigurationInfo()
    this.getCode()
    this.token = window.location.href.split('?')[1] ? window.location.href.split('?')[1].split('=')[1] : undefined
    if (this.token) {
      localStorage.setItem('token', this.token)
      sessionStorage.setItem('hasBackButton', true)
      fetchUserInfo().then((res) => {
        res.data.data.userInfo.userToken = this.token
        localStorage.setItem('cateenBigDataUserInfo', JSON.stringify(res.data.data.userInfo))
        this.$router.push('/commandSandTable')
      })
    } else {
      sessionStorage.removeItem('hasBackButton')
    }
    // this.getCode()
  },
  methods: {
    // 加密
    RSAencrypt(val) {
      const jse = new JSEncrypt()
      jse.setPublicKey(this.publicKey)
      return jse.encrypt(val)
    },
    getCode() {
      this.base64Code = ''
      this.times = new Date().getTime()
      getValidateCode({
        type: 6,
        times: this.times
      }).then((res) => {
        if (res.data.data) {
          this.base64Code = res.data.data
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 获取配置信息
    getConfigurationInfo() {
      getPublicKey().then((response) => {
        this.publicKey = response.data.data
        configurationInfo().then((res) => {
          const NodeRSA = require('node-rsa')
          // 公钥解密
          const privateKey = `-----BEGIN PUBLIC KEY-----\n${this.publicKey}\n-----END PUBLIC KEY-----`
          const nodersa = new NodeRSA(privateKey)
          const decrypted = nodersa.decryptPublic(res.data.data, 'utf8')
          this.infoData = JSON.parse(decrypted)
          // this.changeFavicon(res.data.data.supervisionIcon,res.data.data.supervisionSystemName)
        })
      })
    },
    async login() {
      this.times = new Date().getTime()
      if (!this.username) {
        alert('请输入用户名')
      } else if (!this.password) {
        alert('请输入密码')
      } else if (this.base64Code && !this.code) {
        this.$message.warning('请输入验证码')
      } else {
        this.loading = true
        const formdata = new FormData()
        formdata.append('account', this.username.trim())
        formdata.append('password', this.RSAencrypt(this.password.trim()))
        formdata.append('type', 6)
        formdata.append('times', this.times)
        formdata.append('code', this.code)
        await login(formdata).then(res => {
          this.loading = false
          const userInfo = res.data.data.userInfo
          userInfo.userToken = res.headers['x-auth-token']
          localStorage.setItem(
            'cateenBigDataUserInfo',
            JSON.stringify(userInfo)
          )
        }).catch(() => {
          this.getCode()
        })
        console.log(localStorage.getItem('cateenBigDataUserInfo'), 'dd')
        localStorage.router = JSON.stringify({ value: '/commandSandTable', name: '全域总览' })
        this.$router.replace('/commandSandTable')
      }
    }
  }
}
</script>

<style scoped>
@import url('../assets/css/login.css');
.content {
  text-align: center;
  font-size: 0.16rem;
}
.code {
  width: 160px;
  height: 40px;
  float: left;
  /* vertical-align: middle; */
  cursor: pointer;
}
.msg {
  width: 400px;
  margin: 0 auto;
  text-align: left;
  color: #f64e4e;
}
#fade-box .marginBot20 {
  margin-bottom: 20px;
}
#fade-box .mousePointer {
  cursor: pointer;
}
</style>
