<template>
    <div class="box">
      搜索
    </div>
</template>

<script>
export default {
     name:'hoverwindow',
    components: {

    },
    props: {

    },
    data() {
        return {

        };
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {

    },
    methods: {

    },
};
</script>

<style scoped lang="scss">
.box{
  width: 1rem;
  height: 1rem;
  background: red;
}
</style>
