
<style>
.el-dialog {
  position: relative;
}
.el-dialog__header {
  background: #011247;
}
.el-dialog__body {
  background: #011247;
}
.dialog-header {
  margin-bottom: 10px;
  color: #abc9ff;
}
.dialog-header .label {
  font-size: 20px;
  font-weight: bold;
  color: #01d4ef;
}
.dialog-title {
  width: 250px;
  height: 38px;
  background: url('../assets/img/marker-title.png');
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  color: #fff;
  background-size: 100% 100%;
  position: absolute;
  top: 0px;
  left: 50%;
  transform: translateX(-50%);
}
.el-collapse-item__wrap {
  background: #011247;
}
.el-table__fixed-right::before,
.el-table__fixed::before {
  background-color: #011247 !important;
}
.el-table--scrollable-y .el-table__body-wrapper {
  background-color: #011247 !important;
}
</style>
<template>
  <el-dialog
    width="900px"
    :modal="false"
    :visible.sync="showScoreDetail"
    @closed="dialogClosed"
  >
    <div class="dialog-title">风险指数详情</div>
    <el-row class="dialog-header">
      <el-col :span="6"
        >风险指数：<span class="label">{{ score }}分</span></el-col
      >
      <el-col :span="6"
        >当前排名：<span class="label">{{ rank }}</span></el-col
      >
    </el-row>
    <el-collapse v-show="!!CKPIDetail" v-model="activeName" accordion>
      <el-collapse-item
        v-for="(item, index) in CKPIDetail"
        :key="'first' + index"
        :title="item.name + '：' + item.score + '分'"
        :name="index + 1"
      >
        <el-collapse accordion style="width: 97%; margin: 0 auto">
          <el-collapse-item
            v-for="(item2, index2) in item.list"
            :key="'second' + index2"
            :title="item2.name + '：' + item2.score + '分'"
            :name="index2 + 1"
          >
            <el-table
              v-if="item2.name == '监管抽查指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="事件标题" prop="title" />
              <el-table-column
                align="center"
                label="事件类型"
                prop="eventTypeName"
              />
              <el-table-column
                align="center"
                label="创建时间"
                prop="createTime"
                :formatter="(row) => row.createTime.replace('T', ' ')"
              />
              <el-table-column
                align="center"
                label="截止时间"
                prop="eventStintTime"
              />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                width="80"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '穿戴指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="人员姓名" prop="name" />
              <el-table-column align="center" label="描述" prop="describe" />
              <el-table-column align="center" label="时间" prop="triggerTime" />
              <el-table-column align="center" label="人脸图片" prop="faceImage">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="success"
                    plain
                    @click="showImg(scope.row.faceImage)"
                  >
                    <i class="el-icon-picture" /> 查看
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="报警图片"
                prop="faceAlarmImage"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="success"
                    plain
                    @click="showImg(scope.row.faceAlarmImage)"
                  >
                    <i class="el-icon-picture" /> 查看
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="分数"
                width="80"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '健康证指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="人员姓名" prop="name" />
              <el-table-column align="center" label="描述" prop="describe" />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '报警指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="时间" prop="time" />
              <el-table-column align="center" label="描述" prop="describe" />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '温湿度指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="数值" prop="val" />
              <el-table-column align="center" label="时间" prop="time" />
              <el-table-column align="center" label="描述" prop="describe" />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '食材过期指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="食材名称" prop="name" />
              <el-table-column align="center" label="数量" prop="laveNum" />
              <el-table-column
                align="center"
                label="过期时间"
                prop="expireTime"
              />
              <el-table-column
                align="center"
                label="过期天数"
                prop="expireDay"
              />
              <el-table-column
                align="center"
                label="批次"
                prop="batch"
                width="150"
              />
              <el-table-column
                align="center"
                label="描述"
                prop="describe"
                width="200"
              />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="
                item2.name == '食品留样指数' || item2.name == '食品成品指数'
              "
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column
                align="center"
                label="菜品名称"
                prop="bookName"
              />
              <el-table-column
                align="center"
                label="菜品所属日期"
                prop="dishForDay"
              />
              <el-table-column align="center" label="餐别" prop="useTime" />
              <el-table-column
                align="center"
                label="描述"
                prop="describe"
                width="250"
              />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '食品数量指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="数量" prop="useCount" />
              <el-table-column align="center" label="日期" prop="dishForDay" />
              <el-table-column align="center" label="餐别" prop="useTime" />
              <el-table-column align="center" label="描述" prop="describe" />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '出入库指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="食材名称" prop="name" />
              <el-table-column
                align="center"
                label="菜品名称"
                prop="bookName"
              />
              <el-table-column align="center" label="日期">
                <template>{{
                  new Date(+new Date() - 86400000).toLocaleDateString()
                }}</template>
              </el-table-column>
              <el-table-column
                align="center"
                label="描述"
                prop="describe"
                min-width="200"
              />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
              />
            </el-table>
            <el-table
              v-if="item2.name == '膳食结构指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="描述" prop="describe" />
              <el-table-column align="center" label="食材类型" prop="name">
                <template slot-scope="scope">
                  <el-tag
                    v-for="(item, index) in scope.row.name"
                    :key="'type' + index"
                    v-text="item"
                  />
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '事件处理指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="事件标题" prop="title" />
              <el-table-column
                align="center"
                label="事件类型"
                prop="eventTypeName"
              />
              <el-table-column
                align="center"
                label="创建时间"
                prop="createTime"
              />
              <el-table-column
                align="center"
                label="截止时间"
                prop="eventStintTime"
              />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '投诉指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="原因" prop="describe" />
              <el-table-column align="center" label="详情" prop="name" />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '自查指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="原因" prop="reason" />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '资质证件过期指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column
                align="center"
                label="过期时间"
                prop="expiredTime"
              />
              <el-table-column align="center" label="名称" prop="name" />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '未晨检指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="员工姓名" prop="name" />
              <el-table-column
                align="center"
                label="晨检日期"
                prop="checkDate"
              />
              <el-table-column align="center" label="描述" prop="describe" />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '考试指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="员工姓名" prop="name" />
              <el-table-column align="center" label="描述" prop="describe" />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
            <el-table
              v-if="item2.name == '培训指数'"
              :data="item2.list"
              max-height="250"
              border
            >
              <el-table-column align="center" label="原因" prop="describe" />
              <el-table-column
                align="center"
                label="分数"
                prop="score"
                fixed="right"
                min-width="80"
              />
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </el-collapse-item>
    </el-collapse>
    <div v-show="CKPIDetail === null" style="text-align: center; color: red">
      暂无数据
    </div>
  </el-dialog>
</template>

<script>
import { fetchCkpi } from '@/api/common'
import { formatDate } from '@/utils/date'
export default {
  props: {
    cid: {
      require: true,
      type: Number,
      default: 0,
    },
    score: {
      require: true,
      type: [String, Number],
      default: '0',
    },
    rank: {
      require: true,
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      CKPIDetailLoading: false,
      CKPIDetail: undefined,
      activeName: '',
      showScoreDetail: false,
    }
  },
  watch: {
    showScoreDetail(newVal) {
      if (newVal) {
        this.showPointDetail()
      }
    },
    cid(newVal) {
      if (newVal) {
        this.showPointDetail()
      }
    },
  },
  created() {
    this.formatDate = formatDate
  },
  methods: {
    async showPointDetail() {
      this.CKPIDetailLoading = true
      this.showCKPIDetailWindow = true
      const res = await fetchCkpi(
        this.cid,
        formatDate(new Date(+new Date() - 86400000), 'yyyy-MM-dd')
      )
      if (res.data.data) {
        this.CKPIDetail = JSON.parse(res.data.data.json)
        console.log(this.CKPIDetail)
        this.rank = res.data.data.ranking
        this.canteenDetail = res.data.data
        this.CKPIDetailLoading = false
      } else {
        this.$message.error('暂无相关数据')
        this.CKPIDetail = null
        this.CKPIDetailLoading = false
      }
    },
    dialogClosed() {
      this.$emit('dialogClosed')
    },
  },
}
</script>

<style>
</style>
