.main-content {
  color: #A7BDE6;

  .special_focus_toolTip {
    padding: 0.15rem;
    z-index: 7000000000;
    position: absolute;
    display: none;
    width: 1.2rem;
    min-height: 0.4rem;
    background: url(../../assets/img/<EMAIL>) no-repeat;
    background-size: 100% 100%;
    background-position: 100% 100%;
  }

  .special_focus_tool {
    padding: 0.15rem;
    z-index: 7000000000;
    position: absolute;
    display: none;
    width: 1.2rem;
    min-height: 0.4rem;
    background: url(../../assets/img/<EMAIL>) no-repeat;
    background-size: 100% 100%;
    background-position: 100% 100%;
  }

  .start {
    animation: myfirst 1s infinite;
  }

  @keyframes myfirst {
    0% {
      opacity: 1;
    }

    50% {
      opacity: 0.5;
    }

    100% {
      opacity: 1;
    }
  }

  .list-window {
    padding: 0.15rem 0 0.3rem;
    width: 4.4rem;
    height: 2.6rem;
    // border: 1px solid red;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 100000;
    background: url(../../assets/img/<EMAIL>) no-repeat;
    background-position: 100% 100%;
    background-size: 100% 100%;

    .not-morning-check{
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 100%;
    }
    .is-morning-check{
      .window-title{
        margin: 0.1rem auto;
        text-align: center;
      }
      .morning-check-warning{
        display: flex;
        padding: 0 0.3rem 0;
        box-sizing: border-box;
        .left{
          width: 1.78rem !important;
          height: 2.04rem;
          margin-right: 0.1rem;
          background: url('../../assets/img/gjtp.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          .el-image{
            width: 82%;
            height: 85%;
          }
        }
        .right{
          padding-top: 0.08rem;
          height: 1.9rem;
          width: 2.2rem;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .temp{
            width: 1.2rem;
            height: 0.3rem;
            display: flex;
            font-size: 0.12rem;
            align-items: center;
            color: #FF6161;
            background: url('../../assets/img/ycl@2x (1).png');
            background-size: 100% 100%;
          }
          .content{
            color: #95ADD7;
            font-size: 0.11rem;
          }
          .line{
            display: flex;
            font-size: 0.13rem;
            .label{
              color: #13ECFF;
              flex-shrink: 0;
            }
            .info{
              color: #ABC9FF
            }
          }
        }
      }
    }


    .processed,
    .unprocessed {
      width: 2rem;
      height: 0.27rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .processed {
      color: #13ECFF;
      background: url(../../assets/img/<EMAIL>);
    }

    .unprocessed {
      color: #FF6161;
      background: url(../../assets/img/<EMAIL>);
    }

    .window-quxiao {
      position: absolute;
      right: 0.1rem;
      top: 0.1rem;
      width: 0.35rem;
      height: 0.35rem;
      background: url(../../assets/img/<EMAIL>) no-repeat;
      background-position: 100% 100%;
      background-size: 100% 100%;
    }

    .window-title {
      font-size: 0.16rem;
      font-family: PingFang SC;
      font-weight: bold;
      color: #F66060;
    }

    .window-image {
      width: 3rem;
      height: 1.6rem;
      background: url('../../assets/img/<EMAIL>') no-repeat;
      background-size: 100% 100%;
      padding: 15px;
      box-sizing: border-box;

      .el-image {
        width: 100%;
        height: 100%;
      }
    }

    .window-week {
      width: 1.1rem;
      height: 1.1rem;
      background: url(../../assets/img/<EMAIL>) no-repeat;
      background-size: 100% 100%;
      // opacity: 0.1;
      border-radius: 9px;

      .weekrow {
        // border: 1px solid;
        width: 100%;
        padding: 0 5%;
        color: #fff;
        box-sizing: border-box;

        >span:nth-child(1) {
          font-size: 0.08rem;
          font-family: PingFang SC;
          font-weight: bold;
          color: #13ECFF;
        }

        >span:nth-child(2) {
          margin-left: 0.2rem;
          font-size: 0.08rem;
          font-family: PingFang SC;
          font-weight: bold;
          color: rgba(171, 201, 255, 1);
        }
      }
    }

    .window-info {
      color: #ABC9FF;
      text-align: center;
    }

    .window-list {
      display: flex;
      width: 100%;
      height: 0.95rem;
      padding-left: 5%;
      box-sizing: border-box;

      .list-left {
        font-size: 0.08rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: #13ECFF;
      }
    }
  }

  .yellow {
    color: #F5CA34
  }

  .green {
    color: #32B6A6
  }

  .red {
    color: #E94A48
  }

  .blue {
    color: #00BFF3;
  }

  .box-title {
    font-size: 0.1rem;
    margin-bottom: 0.05rem;
    display: flex;
    align-items: center;

    >div {
      margin-left: 0.045rem;
    }
  }

  .left-side,
  .right-side {
    position: absolute;
    width: 2.25rem;
    height: 3.67rem;
    top: 0.6rem;
  }

  .left-side {
    left: 0.2rem;

    .box1 {
      margin-bottom: 0.1rem;
      width: 100%;
      padding: 0.1rem 0.2rem;
      box-sizing: border-box;
      height: calc(1.6rem - (calc(1080px - 100vh) / 3));

      .total {
        display: flex;
        align-items: center;

        .nums {
          display: flex;

          .num-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 0.22rem;
            height: 0.28rem;
            font-family: myFont;
            font-size: 0.16rem;
            color: #70BAFF;
            margin-right: 0.02rem;
            background: url(../../assets/img/multipleGovernance/sz.png);
            background-size: 100% 100%;
          }
        }
      }

      .person-detail {
        display: flex;
        justify-content: space-between;
        margin: 0.05rem 0;

        .num {
          text-align: center;
          font-size: 0.13rem;
          font-weight: bold;
        }
      }
    }

    .box2 {
      margin-bottom: 0.1rem;
      height: calc(1.19rem - (calc(1080px - 100vh) / 3));
      display: flex;
      padding: 0.1rem 0.2rem;
      box-sizing: border-box;

      .warning-nums {
        width: 1.2rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        font-size: 0.08rem;

        >div {
          display: flex;
          align-items: center;

          span:nth-child(1) {
            flex: 0.45
          }
        }

        .num {
          font-size: 0.13rem;
          font-weight: bold;
        }
      }
    }

    .box3 {
      padding: 0.08rem 0.1rem;
      box-sizing: border-box;
      height: calc(1.04rem - (calc(1080px - 100vh) / 3));
    }
  }

  .right-side {
    right: 0.2rem;

    .box1 {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-bottom: 0.1rem;
      padding: 0.1rem 0.15rem;
      box-sizing: border-box;
      height: calc(1.05rem - (calc(1080px - 100vh) / 3));

      // 原生表格
      .table {
        width: 100%;
        border-color: #1C91BD;

        thead {
          tr {
            color: #fff;
            height: 0.18rem;
            line-height: 0.18rem;
            text-align: center;
            background: #143185;
          }
        }

        tbody {
          td {
            text-align: center;
            padding: 0.015rem 0;
          }
        }
      }
    }

    .box2 {
      margin-bottom: 0.1rem;
      padding: 0.05rem 0.15rem;
      height: calc(1.75rem - (calc(1080px - 100vh) / 3));
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      .equipment {
        font-size: 0.075rem;
        width: 110px;
        height: 110px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-bottom: 0.08rem;

        .name {
          width: 100%;
          height: 0.15rem;
          margin-bottom: 0.03rem;
          display: flex;
          justify-content: center;
          align-items: center;
          background: linear-gradient(to right, #0c237805, #0c237870, #0c237805);
        }

        .num {
          font-size: 0.09rem;
          color: #00C9B3;
          font-weight: bold;
        }
      }
    }

    .box3 {
      padding: 0.08rem 0.1rem;
      box-sizing: border-box;
      height: calc(1.04rem - (calc(1080px - 100vh) / 3));
    }
  }

  // swiper滚动表格
  .thead {
    color: #EAEAEA;
    font-size: 0.08rem;
    margin-bottom: 0.05rem;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;

    >div {
      text-align: center;
    }

    >div:nth-child(1) {
      width: 35%;
    }

    >div:nth-child(2) {
      width: 30%;
    }

    >div:nth-child(3) {
      width: 25%;
    }

    >div:last-child {
      width: 10%;
    }
  }

  .tbody {
    height: calc(100% - 0.13rem);
    overflow: hidden;
    font-size: 18px;
    cursor: pointer;

    .tr:nth-child(odd) {
      background: linear-gradient(to right, #0c237805, #0c237870, #0c237805);
    }

    .tr-danger {
      background: linear-gradient(to right, #e10b0c05, #e10b0c70, #e10b0c05) !important;
    }

    .tr {
      display: flex;
      align-items: center;

      >div {
        text-align: center;
      }

      >div:nth-child(1) {
        width: 35%;
      }

      >div:nth-child(2) {
        width: 30%;
      }

      >div:nth-child(3) {
        width: 25%;
      }

      >div:last-child {
        width: 10%;
      }



      .td {
        font-size: 0.08rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      // .td:last-child {
      //   color: #F94749
      // }
    }
  }

  .warning-main {
    width: 3.53rem;
    height: 3.15rem;
    //width: 3.5rem;
    //height: 86vh;
    position: fixed;
    top: 1.2rem;
    right: 3rem;
    z-index: 100;
    background: url('../../assets/img/warning_box.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 0.265rem 0.26rem 0.195rem 0.29rem;

    .warning-title {
      display: flex;
      align-items: center;
      padding-bottom: 0.2rem;

      >img {
        width: 0.085rem;
        height: 0.15rem;
        margin-right: 0.085rem;
      }

      .center-name {
        font-size: 0.13rem;
        font-weight: bold;
        color: #CDDFFF;
      }
    }

    .bg-box,
    .bg-box1 {
      height: 1.95rem;
      background: url('../../assets/img/pengzheng.png') no-repeat;
      background-size: 100% 100%;
      margin: 0.135rem 0;
      position: relative;

      .warning-content {
        width: 2rem;
        height: 0.25rem;
        box-sizing: border-box;
        padding: 0.03rem;
        background: rgba(4, 5, 26, 0.3);
        box-shadow: inset 0 0 30px #AD0636;
        //background: url('../../assets/img/<EMAIL>') no-repeat;
        background-size: 100% 100%;
        position: absolute;
        left: -0.01rem;
        top: -0.3rem;
      }

      .img-area {
        >img {
          width: 0.13rem;
          height: 0.13rem;
          position: absolute;
          cursor: pointer;
          z-index: 101;
        }
      }
    }

    .bg-box1 {
      background: url('../../assets/img/shuangliu.png') no-repeat;
      background-size: 100% 100%;
    }

    .warning-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      >div {
        display: flex;
        align-items: center;

        >img {
          width: 0.11rem;
          height: 0.11rem;
          margin-right: 0.03rem;
        }

        >div {
          font-size: 0.08rem;
          color: #D8EFFE;
        }
      }
    }
  }
}

.infoWindow {
  position: absolute;
  top: 20%;
  left: 40%;
  width: 1.75rem;
  height: 2.2rem;
  background: url('../../assets/img/<EMAIL>');
  background-size: 100% 100%;

  .infoWindow-title {
    background: url('../../assets/img/<EMAIL>');
    background-size: 100% 100%;
    width: 1.2rem;
    height: 0.2rem;
    text-align: center;
    margin: 0 auto;
    line-height: 0.17rem;
  }
}
