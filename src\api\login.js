import request from '@/utils/request'

export function login(data) {
  return request({
    url: `/api/v1/login/loginIng`,
    method: 'post',
    data
  })
}

export function getValidateCode(params) {
  return request({
    url: `/api/v1/login/qryValidateCode`,
    method: 'get',
    params
  })
}

// 获取配置信息
export function configurationInfo() {
  return request({
    url: `/api/v1/system_config/get`,
    method: 'get'
  })
}
export function logout() {
  return request({
    url: '/api/v1/login/loginOut',
    method: 'post'
  })
}

export function getPublicKey() {
  return request({
    url: '/api/v1/login/getPublicKey',
    method: 'get'
  })
}

// export function changePassword(phone, oldPassword, newPassword) {
//   return request({
//     url: '/api/v1/user/updatePassword',
//     method: 'post',
//     data: {
//       telephone: phone,
//       password: oldPassword,
//       newPassword: newPassword
//     }
//   })
// }
