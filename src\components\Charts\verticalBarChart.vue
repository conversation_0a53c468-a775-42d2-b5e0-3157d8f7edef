<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    propData: {
      require: false,
      type: [Object, Array],
      default: () => {}
    },
    colors: {
      require: false,
      type: Array,
      default: () => { return [] }
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      const barBottomColor = ['#FF877C', '#FFCE8C', '#F7FFA4', '#43E9A9', '#96FFB0']
      const barTopColor = ['#FD1547', '#FD9D15', '#FFD12D', '#9EFFD8', '#1ED94F']
      this.option = {
        animationDuration: 3000,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          top: '15%',
          right: 0,
          left: 0,
          bottom: '20%'
        },
        xAxis: [{
          type: 'category',
          data: ['差', '一般', '较好', '好', '很好'],
          axisLine: {
            lineStyle: {
              color: '#17195D'
            }
          },
          axisLabel: {
            margin: 10,
            color: '#e2e9ff',
            textStyle: {
              fontSize: 14
            }
          }
        }],
        yAxis: {
          show: false
        },
        series: [{
          type: 'bar',
          data: this.propData,
          barWidth: '12px',
          label: {
            normal: {
              show: true,
              position: 'top',
              formatter: '{c}',
              textStyle: {
                color: 'white'
              }
            }
          },
          itemStyle: {
            normal: {
              color: (params) => {
                return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: this.colors.length > 0 ? this.colors[0] : barBottomColor[params.dataIndex]
                }, {
                  offset: 1,
                  color: this.colors.length > 0 ? this.colors[1] : barTopColor[params.dataIndex]
                }])
              },
              barBorderRadius: [30, 30, 30, 30],
              shadowColor: 'rgba(0,160,221,1)',
              shadowBlur: 4
            }
          }
        }]
      }
      this.chart.setOption(this.option)
      if (this.propData && this.propData.length > 0) {
        this.autoPlayTool(this.chart, this.propData, 0)
      }
    }
  }
}
</script>
