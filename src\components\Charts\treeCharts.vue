<template>
  <!-- <div id="container" :style="{ height: height, width: width }" /> -->
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
// import chartsMixIn from './mixins'

export default {
  name: 'TreeCharts',
  // mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    orient: {
      require: false,
      type: String,
      default: 'LR'
    },
    width: {
      require: false,
      type: String,
      default: '1350px'
    },
    height: {
      require: false,
      type: String,
      default: '700px'
    },
    propData: {
      require: false,
      type: [Object, Array],
      default: () => {}
    },
    colors: {
      require: true,
      type: Array,
      default: () => { return [] }
    }
  },
  data() {
    return {
      graph: null,
      doNotRedraw: true
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.graph) {
          this.graph.changeData(newValue)
        }
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      }
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.graph) {
      return
    }
    this.graph.clear()
    // this.chart.dispose()
    this.graph = null
  },
  methods: {
    initChart() {
      // S id排序查重
      // const arr = []
      // const testDuplicateData = (data) => {
      //   data.forEach(item => {
      //     arr.push({ name: item.name, id: Number(item.id) ? Number(item.id) : item.id })
      //     if (item.children && item.children.length > 0) {
      //       testDuplicateData(item.children)
      //     }
      //   })
      // }
      // testDuplicateData([this.propData])
      // function sortByKey(array, key) {
      //   return array.sort(function(a, b) {
      //     var x = a[key]
      //     var y = b[key]
      //     return ((x < y) ? -1 : ((x > y) ? 1 : 0))
      //   })
      // }
      // sortByKey(arr, 'id')
      // console.log(arr, 666)
      // E id排序查重

      const processingData = (data, index) => {
        data.forEach(item => {
          const hasChildren = item.children && item.children.length > 0
          item.label = {
            position: index === 0 ? 'bottom' : 'inside'
          }
          item.itemStyle = {
            color: this.colors[index],
            borderWidth: hasChildren ? 0 : 1,
            borderColor: hasChildren ? 0 : (index === 0 ? '#4358C9' : index === 1 ? '#2168C1' : index === 2 ? '#328E85' : '#1381A1')
          }
          if (hasChildren) {
            processingData(item.children, index + 1)
          }
        })
      }
      processingData([this.propData], 0)
      this.chart = echarts.init(document.getElementById(this.id))
      this.option = {

        grid: {
          top: 'middle'
        },
        series: [
          {
            left: '15%',
            right: '15%',
            type: 'tree',
            symbolSize: 28,
            symbol: 'circle',
            initialTreeDepth: 3,
            roam: true,
            itemStyle: {
              borderWidth: 0
            },
            orient: this.orient,
            lineStyle: {
              color: '#4A87CC',
              curveness: 0.8
            },

            label: {
              show: true,
              position: 'inside',
              textStyle: {
                fontSize: 15,
                color: '#fff'
              }
            },

            leaves: {
              label: {
                position: 'right',
                verticalAlign: 'middle'
              }
            },
            data: [this.propData]
          }
        ]
      }
      this.chart.setOption(this.option)
      const clickFun = params => {
        console.log(params, 999999777777)
        if (typeof params.seriesIndex === 'undefined') {
          return
        }
        if (params.type === 'click') {
          this.$emit('clickPoint', params)
        }
      }
      this.chart.off('click')
      this.chart.on('click', clickFun)
      this.chart.resize()
    }
  }
}
</script>
