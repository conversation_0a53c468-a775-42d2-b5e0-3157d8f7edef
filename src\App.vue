<template>
  <div id="app">
    <component-title />
    <transition :name="transitionName">
      <router-view class="child-view" />
    </transition>
  </div>
</template>
<script>
import componentTitle from '@/components/title'

export default {
  components: {
    componentTitle
  },
  data() {
    return {
      transitionName: 'slide-left'
    }
  },
  watch: {
    $route(to, from) {
      const toDepth = to.path.split('/').length
      const fromDepth = from.path.split('/').length
      this.transitionName = toDepth < fromDepth ? 'slide-right' : 'slide-left'
    }
  }
}
</script>

<style lang="scss">
.child-view {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  transition: all 1.5s cubic-bezier(0.55, 0, 0.1, 1);
}
.slide-left-enter,
.slide-right-leave-active {
  opacity: 0;
  -webkit-transform: translate(-80px, 0);
  transform: translate(-80px, 0);
}
.slide-left-leave-active,
.slide-right-enter {
  opacity: 0;
  -webkit-transform: translate(100%, 0);
  transform: translate(100%, 0);
}

* {
  margin: 0;
  padding: 0;
}
ul {
  list-style: none;
}
#app {
  transform-origin: 0 0;
  background: url('./assets/img/indexBG.png');
  background-size: 100% 100%;
  position: relative;
  height: 100vh;
}
html,
body {
  color: #ffffff;
  font-size: 0.08rem;
  overflow: hidden;
}
</style>
