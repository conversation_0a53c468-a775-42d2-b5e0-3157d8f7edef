import Vue from 'vue'
import App from './App.vue'
import router from './router'
import ElementUI from 'element-ui'
import echarts from 'echarts'
import 'element-ui/lib/theme-chalk/index.css'
import VueAwesomeSwiper from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
Vue.use(VueAwesomeSwiper)
import 'echarts-wordcloud/dist/echarts-wordcloud'
import 'echarts-wordcloud/dist/echarts-wordcloud.min'
Vue.prototype.$echarts = echarts

Vue.use(ElementUI, { size: 'small' })
Vue.config.productionTip = false

function getPageTitle(pageTitle) {
  const title = '山东能源智慧食安指挥监管平台'
  if (pageTitle) {
    return `${pageTitle} - ${title}`
  }
  return `${title}`
}
router.beforeEach((to, from, next) => {
  if (to.path === '/login') {
    next()
  } else {
    if (localStorage.getItem('cateenBigDataUserInfo')) {
      next()
    } else {
      next({ path: '/login' })
    }
  }
})
new Vue({
  router,
  render: h => h(App)
}).$mount('#app')
