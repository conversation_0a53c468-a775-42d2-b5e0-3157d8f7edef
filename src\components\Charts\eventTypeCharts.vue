<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import chartsMixIn from './mixins'
export default {
  name: 'EventTypeCharts',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '200px'
    },
    height: {
      require: false,
      type: String,
      default: '200px'
    },
    propData: {
      require: false,
      type: [Object, Array],
      default: () => []
    }
  },
  watch: {
    propData: {
      // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.setOption(newValue)
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
    },
    setOption(data) {
      const total = data.reduce((a, b) => {
        return a + b.value * 1
      }, 0)

      const properties = function(row) {
        return ((row / total) * 100).toFixed(2)
      }

      const colorList = [
        '#0966E6 ',
        '#4CEFEF',
        '#357FDD',
        '#E8B501',
        '#8493FB'
      ]

      this.option = {
        title: {
          textStyle: {
            fontSize: 16,
            color: '#999',
            lineHeight: 20
          },
          subtextStyle: {
            fontSize: 28,
            color: '#333'
          },
          textAlign: 'center',
          left: '39.8%',
          top: '45%'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          itemGap: 25,
          padding: [
            10, // 上
            5, // 右
            5, // 下
            10 // 左
          ],
          selectedMode: false,
          itemHeight: 8,
          itemWidth: 8,
          top: '10%',
          right: '10%',
          data: data.map((item) => item.name),
          formatter(name) {
            if (data && data.length) {
              for (var i = 0; i < data.length; i++) {
                if (name === data[i].name) {
                  return name + data[i].value + '件'
                }
              }
            }
          },
          textStyle: {
            fontSize: 12,
            color: '#ABC9FF'
          }
        },
        color: colorList,
        series: [
          {
            tooltip: {
              trigger: 'item',
              formatter(params) {
                return params.name + '：' + params.value
              }
            },
            itemStyle: {
              normal: {
                borderColor: '#0A1934',
                borderWidth: 5,
                color(params) {
                  return colorList[params.dataIndex]
                }
              }
            },
            type: 'pie',
            radius: [40, 60],
            center: ['35%', '50%'],
            label: {
              normal: {
                formatter(params) {
                  return (
                    '{percent|' +
                    properties(params.value) +
                    '%}' +
                    '\n{hr|———}' +
                    `\n{font|${params.name}}`
                  )
                },
                // padding: [0, -100, 25, -100],
                rich: {
                  percent: {
                    fontSize: 12,
                    padding: [0, 10, 0, 4],
                    color: '#86C6FF'
                  },
                  hr: {
                    color: '#113D72'
                  },
                  font: {
                    fontSize: 12,
                    fontWeight: 'bold',
                    color: '#86C6FF'
                  }
                }
              }
            },
            labelLine: {
              show: true,
              lineStyle: {
                color: '#113D72'
              }
            },
            data: data
          },
          {
            z: -2,
            name: '大环',
            type: 'gauge',
            splitNumber: 40,
            radius: '70%',
            center: ['35%', '50%'],
            startAngle: 90,
            endAngle: -269.9999,
            axisLine: {
              show: false,
              lineStyle: {
                color: [[1, '#145071']]
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              length: 6,
              lineStyle: {
                color: 'auto',
                width: 2.5
              }
            },
            axisLabel: {
              show: false
            },
            detail: {
              show: false
            }
          },
          {
            type: 'pie',
            radius: [19, 20],
            center: ['35%', '50%'],
            hoverAnimation: false,
            color: '#033C6E',
            label: {
              normal: {
                show: false
              }
            },
            data: [
              {
                value: 1
              }
            ],
            silent: true,
            z: -1
          },
          {
            type: 'pie',
            radius: [29, 30],
            center: ['35%', '50%'],
            hoverAnimation: false,
            color: '#033C6E',
            label: {
              normal: {
                show: false
              }
            },
            data: [
              {
                value: 1
              }
            ],
            silent: true,
            z: -1
          },
          {
            type: 'pie',
            radius: [39, 40],
            center: ['35%', '50%'],
            hoverAnimation: false,
            color: '#033C6E',
            label: {
              normal: {
                show: false
              }
            },
            data: [
              {
                value: 1
              }
            ],
            silent: true,
            z: -1
          }
        ]
      }
      this.chart.setOption(this.option)
    }

  },

}
</script>
