.select {
  margin-left: auto;
  width: 0.855rem;
  height: 0.155rem;
  background: rgba(14, 139, 255, 0.32);
  //border: 1px solid #00EAFF;
  //opacity: 0.5;
  background: url("../../assets/img/shaixuanc.png") no-repeat;
  background-size: 100% 100%;
}

.el-input .el-input__inner {
  color: #3ADBFF !important;
}

.main-content {
  padding-top: 0.3rem;
  box-sizing: border-box;
  padding-left: 0.19rem;
  margin-top: 0;
  padding-right: 0.16rem;
  padding-bottom: 0.1rem;
  display: flex;
  position: relative;
  top: 0.42rem;
}

.special_focus_toolTip {
  padding: 0.15rem;
  z-index: 7000000000;
  position: absolute;
  display: none;
  width: 1.2rem;
  min-height: 0.4rem;
  background: url(../../assets/img/<EMAIL>) no-repeat;
  background-size: 100% 100%;
  background-position: 100% 100%;
}

.video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  >div {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .one {
    position: absolute;
    top: 0.28rem;
    left: 37%;
    z-index: 9999;
    width: 0.41rem;
    height: 0.41rem;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.6);
    box-shadow: inset 0 0 0.2rem #08bd6a;
  }

  .two {
    position: absolute;
    top: 0.4rem;
    left: 58%;
    z-index: 9999;
    width: 0.41rem;
    height: 0.41rem;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.6);
    // box-shadow: inset 0 0 0.2rem #00c6ff;
    box-shadow: inset 0 0 0.2rem #e9dc0e;
  }

  .three {
    position: absolute;
    top: 1.1rem;
    left: 54%;
    z-index: 9999;
    width: 0.49rem;
    height: 0.49rem;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.6);
    box-shadow: inset 0 0 0.2rem #e9dc0e;
  }

  .four {
    position: absolute;
    top: 1.45rem;
    left: 34%;
    z-index: 9999;
    width: 0.62rem;
    height: 0.62rem;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.6);
    box-shadow: inset 0 0 0.2rem #00c6ff;
  }

  .five {
    position: absolute;
    top: 1.8rem;
    left: 57%;
    z-index: 9999;
    width: 0.6rem;
    height: 0.6rem;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.6);
    box-shadow: inset 0 0 0.2rem #00ff8a;
  }
}

.left-content {
  width: 2.25rem;
  height: 100%;
  background: rgba(5, 6, 27, 0.3);
  padding: 0.14rem;
  box-sizing: border-box;
  z-index: 99;

  .top-tab {
    padding: 0 0.065rem;
    box-sizing: border-box;
    width: 100%;
    display: flex;
    justify-content: space-between;

    >div {
      font-size: 0.1rem;
      font-family: PingFang SC;
      font-weight: bold;
      color: #CCDAF0;
      cursor: pointer;
    }

    .top-tab-active {
      color: #05DAFE;
      position: relative;
    }

    .top-tab-active::after {
      box-sizing: border-box;
      position: absolute;
      content: '';
      width: 90%;
      // padding: 0 0.2rem;
      background: #04DAFE;
      height: 0.02rem;
      left: 50%;
      transform: translateX(-50%);
      bottom: -0.05rem;
    }
  }

  .top-search {
    width: 100%;
    margin-top: 0.15rem;
    position: relative;

    input {
      box-sizing: border-box;
      outline: none;
      width: 1.96rem;
      color: #fff;
      font-size: 0.09rem;
      padding: 0 0.095rem;
      padding-right: calc(0.095rem + 0.265rem);
      height: 0.23rem;
      background: rgba(2, 18, 72, 0.6);
      border: 0.01rem solid #175AD8;
    }

    .clearinput1 {
      position: absolute;
      top: 0.07rem;
      right: 0.3rem;
      width: 0.09rem;
      height: 0.09rem;
      z-index: 1000;
      background-image: url(../../assets/img/clearinput.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      -moz-background-size: 100% 100%;
    }

    input::-webkit-input-placeholder {
      color: #ABC9FF;
    }

    div {
      position: absolute;
      right: 0;
      top: 0;
      width: 0.27rem;
      height: 0.23rem;
      background: #04A2EB;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      img {
        width: 0.14rem;
        height: 0.15rem;
      }
    }
  }

  .line-list {
    margin-top: 0.1rem;

    .item-mation {
      height: 0.5rem;
      background: url(../../assets/img/<EMAIL>) no-repeat center;
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      color: #ABC9FF;

      .sc-Name {
        margin-left: 0.1rem;
        margin-top: 0.1rem;
        font-size: 0.08rem;
        width: 90%;
        background: linear-gradient(40deg, #03BCFF 0%, #AAFCFF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 1000;
      }

      .pri-Name {
        margin-top: 0.05rem;
        margin-left: 0.1rem;
        font-size: 0.08rem;
        width: 90%;
        background: linear-gradient(40deg, #03BCFF 0%, #AAFCFF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 1000;
      }
    }

    .line-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.05rem;

      >div:nth-of-type(1) {
        font-size: 0.08rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: #ABC9FF;
        width: 0.5rem;
      }

      >div:nth-of-type(2) {
        display: flex;
        align-items: left;
        height: 0.03rem;
        box-sizing: border-box;
        background: linear-gradient(90deg, #05A2E7, #3BFCFB);
        border-radius: 0.02rem;
      }

      >div:nth-of-type(3) {
        font-size: 0.08rem;
        font-family: PingFang SC;
        font-weight: bold;
        width: 0.3rem;
        color: #ABC9FF;
        text-align: right;
        margin-left: auto;
      }
    }

    .line-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 0.11rem;
      font-family: PingFang SC;
      font-weight: bold;
      color: #ABC9FF;
    }
  }

  .scrool-div {
    margin-top: 0.1rem;
    background: rgba(1, 18, 71, 0.6);
    border: 0.005rem solid #216DFD;
    box-sizing: border-box;
    transition: 1s;

    // 一校一档
    .one-div {
      height: 100%;


      .one-div-top {
        height: 0.24rem;
        font-size: 0.09rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: #CCDAF0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        >div {
          width: 34%;
          line-height: 0.24rem;
          text-align: center;
          border-bottom: 0.005rem solid #216DFD;
          cursor: pointer;
        }

        .no-bd-lf {
          border-left: none !important;
        }

        .no-bd-rt {
          border-right: none !important;
        }

        .active-top-type {
          border-bottom: none;
          border-right: 0.005rem solid #216DFD;
          border-left: 0.005rem solid #216DFD;
        }
      }

      .one-div-bottom {
        margin-top: 0.15rem;
        height: 100%;
        box-sizing: border-box;
        padding-right: 0.07rem;

        .noData {
          height: calc(100% - 0.45rem);
          box-sizing: border-box;
          overflow-y: auto;
          text-align: center;
          line-height: 1.5rem;
        }

        .one-div-height {
          height: calc(100% - 0.45rem);
          box-sizing: border-box;
          overflow-y: auto;

          .one-div-item {
            display: flex;
            align-items: center;
            height: 0.2rem;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            div {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              padding-right: 0.15rem;
            }

            img {
              width: 0.09rem;
              height: 0.08rem;
              margin-right: 0.04rem;
              margin-left: 0.25rem;
            }

            font-size: 0.08rem;
            font-family: PingFang SC;
            font-weight: bold;
            color: #A0BDF2;
          }

          .one-div-item:hover {
            background: rgba(20, 61, 131, 0.54);
            box-shadow: inset 0 0 10px #0936b0;
          }

          .one-div-item-active {
            background: rgba(20, 61, 131, 0.54);
            box-shadow: inset 0 0 10px #0936b0;
          }
        }
      }
    }

    // 一人一档
    .two-div {
      height: 100%;

      .two-div-top {
        height: 0.24rem;
        font-size: 0.09rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: #CCDAF0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        >div {
          width: 34%;
          line-height: 0.24rem;
          text-align: center;
          border-bottom: 0.005rem solid #216DFD;
          cursor: pointer;
        }

        .no-bd-lf {
          border-left: none !important;
        }

        .no-bd-rt {
          border-right: none !important;
        }

        .active-top-type {
          border-bottom: none;
          border-right: 0.005rem solid #216DFD;
          border-left: 0.005rem solid #216DFD;
        }
      }

      .two-div-bottom {
        margin-top: 0.15rem;
        height: 100%;
        box-sizing: border-box;
        padding-right: 0.07rem;

        .two-div-height {
          height: calc(100% - 0.45rem);
          box-sizing: border-box;
          overflow-y: auto;

          .two-div-item {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            .top-text {
              padding-right: 0.15rem;
              font-size: 0.08rem;
              font-family: PingFang SC;
              font-weight: bold;
              color: #A0BDF2;
            }

            .top-img {
              width: 0.09rem;
              height: 0.08rem;
              margin-right: 0.04rem;
            }

            .top-img-child {
              width: 0.08rem;
              height: 0.08rem;
              margin-right: 0.04rem;
              margin-left: 0.15rem;
            }
          }
        }
      }
    }

    // 一企一档
    .three-div {
      height: 100%;
      box-sizing: border-box;
      padding-right: 0.07rem;

      .noData {
        height: calc(100% - 0.45rem);
        box-sizing: border-box;
        overflow-y: auto;
        text-align: center;
        line-height: 1.5rem;
      }

      .three-div-bottom {
        margin-top: 0.1rem;
        height: 100%;

        .three-div-height {
          height: calc(100% - 0.25rem);
          box-sizing: border-box;
          overflow-y: auto;

          .three-div-item {
            display: flex;
            align-items: center;
            height: 0.2rem;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            div {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              padding-right: 0.15rem;
            }

            img {
              width: 0.09rem;
              height: 0.08rem;
              margin-right: 0.04rem;
              margin-left: 0.25rem;
            }



            font-size: 0.08rem;
            font-family: PingFang SC;
            font-weight: bold;
            color: #A0BDF2;
          }

          .three-div-item:hover {
            background: rgba(20, 61, 131, 0.54);
            box-shadow: inset 0 0 10px #0936b0;
          }

          .three-div-item-active {
            background: rgba(20, 61, 131, 0.54);
            box-shadow: inset 0 0 10px #0936b0;
          }
        }
      }
    }
  }
}

.right-content {
  height: 100%;
  flex: auto;
  overflow-x: hidden;
  z-index: 99;

  .title {
    display: flex;
    align-items: center;
    font-size: 0.1rem;
    font-family: PingFang SC;
    font-weight: bold;
    color: #CDDFFF;
    margin-bottom: 0.07rem;

    img {
      width: 0.16rem;
      height: 0.145rem;
      margin-right: 0.02rem;
    }
  }

  // 一校一档
  .right-one-div {
    margin-left: 0.1rem;
    height: 100%;
    // border: 0.005rem solid #216DFD;
    // background-color: transparent;
    box-sizing: border-box;

    .right-one-top {
      display: flex;
      align-items: center;
      height: 0.45rem;
      // height: calc(0.45rem - (calc(1080px - 100vh) / 4));

      >div:nth-of-type(1) {
        width: 1.2rem;
        height: 0.45rem;
        background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.1rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: #CCDAF0;

        img {
          width: 0.2rem;
          height: 0.2rem;
          margin-right: 0.04rem;
        }
      }

      >div:nth-of-type(2) {
        width: 2rem;
      }

      // >div:nth-of-type(2) {
      //   width: 1.2rem;
      //   height: 0.45rem;
      //   background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
      //   background-size: 100% 100%;
      //   display: flex;
      //   align-items: center;
      //   justify-content: center;
      //   font-size: 0.12rem;
      //   font-family: PingFang SC;
      //   font-weight: bold;
      //   color: #CCDAF0;

      //   >div {
      //     width: 0.18rem;
      //     height: 0.18rem;
      //     line-height: 0.18rem;
      //     text-align: center;
      //     border: 0.005rem solid RGBA(222, 185, 53, 1);
      //     border-radius: 50%;
      //     font-size: 0.14rem;
      //     font-family: PingFang SC;
      //     font-weight: bold;
      //     color: RGBA(253, 208, 46, 1);
      //     margin-right: 0.085rem;
      //   }
      // }
      .item-mation {
        height: 0.5rem;
        background: url(../../assets/img/wisdomFoodSafety/<EMAIL>) no-repeat center;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        color: #ABC9FF;
        padding-right: 0.1rem;

        .sc-Name {
          // margin-left: 0.1rem;
          margin-top: 0.1rem;
          font-size: 0.08rem;
          width: 90%;
          font-weight: 1000;

          >span {
            color: #8FD9C8;
          }
        }

        .pri-Name {
          margin-top: 0.05rem;
          // margin-left: 0.1rem;
          font-size: 0.08rem;
          width: 90%;
          font-weight: 1000;

          >span {
            color: #9D8F7B;
          }
        }
      }

      // >div:nth-of-type(3) {
      //   width: 1rem;
      //   height: 0.45rem;
      //   background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
      //   background-size: 100% 100%;
      //   display: flex;
      //   align-items: center;
      //   justify-content: center;
      //   font-size: 0.1rem;
      //   font-family: PingFang SC;
      //   font-weight: bold;
      //   color: #CCDAF0;

      //   img {
      //     width: 0.2rem;
      //     height: 0.2rem;
      //     margin-right: 0.07rem;
      //   }
      // }

      // >div:nth-of-type(4) {
      //   width: 1rem;
      //   height: 0.45rem;
      //   background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
      //   background-size: 100% 100%;
      //   display: flex;
      //   justify-content: space-between;
      //   box-sizing: border-box;
      //   padding: 0.08rem 0.13rem;

      //   >div {
      //     display: flex;
      //     align-items: center;
      //     >div:nth-child(2){
      //       margin-left: 0.1rem;
      //     }
      //   }

      //   >div:nth-of-type(1) {
      //     >div:nth-of-type(1) {
      //       font-size: 0.08rem;
      //       font-family: PingFang SC;
      //       font-weight: bold;
      //       color: #CCDAF0;
      //     }

      //     >div:nth-of-type(2) {
      //       font-size: 0.18rem;
      //       font-family: PingFang SC;
      //       font-weight: bold;
      //       color: #44F5FD;
      //     }
      //   }

      //   >div:nth-of-type(2) {
      //     font-size: 0.08rem;
      //     font-family: PingFang SC;
      //     font-weight: bold;
      //     color: #CCDAF0;

      //     >div:nth-of-type(1) {
      //       span {
      //         color: rgba(143, 217, 200, 1);
      //       }
      //     }

      //     >div:nth-of-type(2) {
      //       span {
      //         color: rgba(207, 181, 136, 1);
      //       }
      //     }
      //   }
      // }

      >div:nth-of-type(3) {
        width: 1.2rem;
        height: 0.45rem;
        background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.1rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: #CCDAF0;

        img {
          width: 0.2rem;
          height: 0.2rem;
          margin-right: 0.045rem;
        }
      }

      >div:nth-of-type(4) {
        flex: 1;
        height: 0.45rem;
        background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
        background-size: 100% 100%;
        display: flex;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 0.08rem 0.13rem;

        >div {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }

        >div:nth-of-type(1) {
          >div:nth-of-type(1) {
            font-size: 0.08rem;
            font-family: PingFang SC;
            font-weight: bold;
            color: #CCDAF0;
          }

          >div:nth-of-type(2) {
            font-size: 0.16rem;
            font-family: PingFang SC;
            font-weight: bold;
            color: #44F5FD;
          }
        }

        >div:nth-of-type(2),
        >div:nth-of-type(3) {
          font-size: 0.08rem;
          font-family: PingFang SC;
          font-weight: bold;
          color: #CCDAF0;

          >div:nth-of-type(1) {
            span {
              color: rgba(143, 217, 200, 1);
            }
          }

          >div:nth-of-type(2) {
            span {
              color: rgba(207, 181, 136, 1);
            }
          }
        }
      }
    }

    .right-one-center {
      // margin-top: 0.2rem;
      display: flex;
      margin-top: calc(0.4rem - (calc(1080px - 100vh) / 4));

      // 动态监测
      .right-one-center-one {
        .contents {
          box-sizing: border-box;
          width: 1.87rem;
          // height: 1.40rem;
          height: calc(1.40rem - (calc(1080px - 100vh) / 4));
          background: rgba(3, 3, 29, 0.5) !important;
          padding: 0 0.09rem;
          padding-top: 0.1rem;

          .top {
            display: flex;
            justify-content: space-between;
            align-items: center;

            >div {
              // flex: 1;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              height: 0.7rem;

              .one {
                cursor: pointer;

                >div:nth-of-type(1) {
                  font-size: 0.08rem;
                  font-family: Source Han Sans CN;
                  font-weight: 500;
                  color: #CDDFFF;
                }

                >div:nth-of-type(2) {
                  font-size: 0.12rem;
                  font-family: Source Han Sans CN;
                  font-weight: 500;
                  color: #FF3541;
                }
              }

              .two {
                >div:nth-of-type(1) {
                  font-size: 0.08rem;
                  font-family: Source Han Sans CN;
                  font-weight: 500;
                  color: #CDDFFF;
                }

                >div:nth-of-type(2) {
                  font-size: 0.12rem;
                  font-family: Source Han Sans CN;
                  font-weight: 500;
                  color: #FFFFFF;
                }
              }
            }
          }

          .bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;

            >div:nth-of-type(1) {
              font-size: 0.08rem;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #ABC9FF;
            }

            >div:nth-of-type(2) {
              flex: 1;
              //background: linear-gradient(to right, #56EF8D 50%, transparent 0);
              //background-size: 0.03rem 100%;
              height: 0.07rem;
              margin: 0 0.06rem;
            }

            >div:nth-of-type(3) {
              font-size: 0.08rem;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #FD4D61;
              margin-right: 0.04rem;
            }

            >div:nth-of-type(4) {
              font-size: 0.08rem;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #ABC9FF;
            }
          }
        }
      }

      // 待处理事件
      .right-one-center-two {
        margin: 0 0.1rem;

        .contents {
          box-sizing: border-box;
          width: 2.81rem;
          // height: 1.40rem;
          height: calc(1.40rem - (calc(1080px - 100vh) / 4));
          background: rgba(3, 3, 29, 0.5) !important;
          padding: 0 0.09rem;
          padding-top: 0.1rem;
          display: flex;
          justify-content: space-between;

          .right-div {
            flex: 1;
            margin-left: 0.15rem;

            .cont-title {
              display: flex;
              align-items: center;

              div {
                width: 0.02rem;
                height: 0.08rem;
                background: #20C2FD;
                margin-right: 0.06rem;
              }

              font-size: 0.08rem;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #CDDFFF;
            }

            .list {
              margin-top: 0.06rem;
              height: 0.94rem;

              .list-item {
                display: flex;
                padding: 0.05rem 0;
                // margin-top: 0.06rem;
                box-sizing: border-box;
                height: 0.42rem;
                background: linear-gradient(to right, rgba(5, 35, 100, .2) 0%, rgba(5, 35, 100, .9) 50%, rgba(5, 35, 100, .2) 100%);

                .left {
                  width: 0.47rem;
                  height: 0.32rem;
                  object-fit: cover;
                }

                .right {
                  margin-left: 0.06rem;
                  height: 0.32rem;
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;

                  >div:nth-of-type(1) {
                    font-size: 0.08rem;
                    font-family: Source Han Sans CN;
                    font-weight: 400;
                    color: #FFFFFF;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                  }

                  >div:nth-of-type(2) {
                    font-size: 0.08rem;
                    font-family: PingFang SC;
                    font-weight: 500;
                    color: #FFFFFF;
                  }
                }
              }
            }
          }
        }
      }

      // 从业人员
      .right-one-center-three {
        flex: 1;

        .contents {
          box-sizing: border-box;
          // height: 1.40rem;
          height: calc(1.40rem - (calc(1080px - 100vh) / 4));
          background: rgba(3, 3, 29, 0.5) !important;
          padding: 0 0.09rem;
          padding-top: 0.1rem;

          .one {
            display: flex;
            align-items: center;
            justify-content: space-between;

            img {
              width: 0.08rem;
              height: 0.08rem;
              margin-right: 0.04rem;
            }

            >div:nth-of-type(1) {
              font-size: 0.07rem;
              font-family: PingFang SC;
              font-weight: 500;
              color: #FFFFFF;
              display: flex;
              align-items: center;
            }

            >div:nth-of-type(2) {
              font-size: 0.09rem;
              font-family: PingFang SC;
              font-weight: 400;
              color: #05FFED;
            }
          }

          .two {
            margin-top: 0.04rem;
            width: 100%;
            height: 0.04rem;
            border-radius: 0.04rem;
            background-color: rgba(32, 194, 253, 0.1);
            position: relative;

            div {
              position: absolute;
              left: 0;
              top: 0;
              border-radius: 0.04rem;
              height: 0.04rem;
              width: 0%;
              transition: 1s;
            }

            .line-one {
              background-color: #05FFED;
            }

            .line-two {
              background-color: #FF3541;
            }

            .line-three {
              background-color: #20C2FD;
            }

            .line-four {
              background-color: #20C2FD;
            }
          }

          >div {
            margin-bottom: 0.07rem;

          }

          .item {
            margin-bottom: 0.07rem;

          }

        }
      }
    }

    .right-one-bottom {
      // margin-top: 0.2rem;
      display: flex;
      margin-top: calc(0.4rem - (calc(1080px - 100vh) / 4));

      // 实时预警
      .right-one-bottom-one {
        .contents {
          box-sizing: border-box;
          width: 2.69rem;
          // height: 1.40rem;
          height: calc(1.47rem - (calc(1080px - 100vh) / 4));
          background: rgba(3, 3, 29, 0.5) !important;
          padding: 0 0.1rem;
          padding-top: 0.2rem;
          display: flex;
          justify-content: space-between;

          .left {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 1rem;

            >div {
              display: flex;
              flex-direction: column;
              justify-content: center;
              box-sizing: border-box;
              padding: 0.15rem 0;
              padding-left: 0.55rem;
            }

            >div:nth-of-type(1) {
              width: 1.04rem;
              height: 0.44rem;
              background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
              background-size: 100% 100%;

              >div:nth-of-type(1) {
                font-size: 0.13rem;
                font-family: PingFang SC;
                font-weight: bold;
                color: #44F5FD;
              }

              >div:nth-of-type(2) {
                font-size: 0.07rem;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #A8BEE7;
              }
            }

            >div:nth-of-type(2) {
              width: 1.04rem;
              height: 0.44rem;
              background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
              background-size: 100% 100%;

              >div:nth-of-type(1) {
                font-size: 0.13rem;
                font-family: PingFang SC;
                font-weight: bold;
                color: #DDE67B;
              }

              >div:nth-of-type(2) {
                font-size: 0.07rem;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #A8BEE7;
              }
            }
          }

          .right {
            flex: 1;
            height: 1rem;
            margin-left: 0.11rem;
            margin-top: -0.1rem;

            .thead {
              display: flex;
              align-items: center;
              height: 0.18rem;
              font-size: 0.08rem;
              font-family: PingFang SC;
              font-weight: bold;
              color: #FFFFFF;

              div {
                text-align: center;
                line-height: 0.18rem;
              }

              >div:nth-of-type(1) {
                width: 46%;
              }

              >div:nth-of-type(2) {
                width: 33%;
              }

              >div:nth-of-type(3) {
                width: 20%;
              }
            }

            .tbody {
              height: 0.9rem;

              .tr:nth-child(odd) {
                // background-color: rgba(6, 36, 102, 0.8);
                background: linear-gradient(to right, rgba(59, 63, 73, 0.2) 0%, rgba(6, 36, 102, .8) 50%, rgba(6, 36, 102, .2) 100%);
              }

              .tr {

                display: flex;
                align-items: center;
                font-size: 0.07rem;
                font-family: Source Han Sans CN;
                font-weight: 500;
                color: #9DB2DD;

                div {
                  text-align: center;
                  line-height: 0.18rem;
                }



                >div:nth-of-type(1) {
                  width: 46%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                >div:nth-of-type(2) {
                  width: 33%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                >div:nth-of-type(3) {
                  width: 20%;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  img {
                    width: 0.175rem;
                    height: 0.14rem;
                  }
                }
              }
            }
          }
        }
      }

      // 菜品管理
      .right-one-bottom-two {
        margin: 0 0.1rem;

        .contents {
          box-sizing: border-box;
          width: 1.99rem;
          // height: 1.47rem;
          height: calc(1.47rem - (calc(1080px - 100vh) / 4));
          background: rgba(3, 3, 29, 0.5) !important;
          padding: 0 0.07rem;
          padding-top: 0.1rem;

          .canteenName {
            color: #FFFFFF;
            font-size: 0.1rem;
          }

          .teacherScore {
            display: flex;
            margin-top: 0.03rem;

            .score-title {
              color: #00E7FF;
              font-size: 0.07rem;
            }

            .stars {
              margin-left: 0.1rem;

              .startImg {
                margin-right: 0.04rem;
              }
            }
          }

          .weekList {
            margin-top: 0.02rem;
            width: 100%;
            display: flex;
            flex-direction: column;
            height: calc(1.1rem - (calc(1080px - 100vh) / 4));

            // border: 1px solid;
            // justify-content: space-between;
            .weekitem {
              width: 100%;
              // height: 45%;
              text-align: center;
              background-color: #02174C;
              // margin-bottom: 0.02rem;
              color: #00E7FF;

              .itemhead {
                width: 100%;
                height: 0.12rem;
                background: #133F9F;
                display: flex;
                justify-content: space-between;
                align-content: center;
                align-items: center;
                padding: 0 0.05rem;
                box-sizing: border-box;

                >div:nth-child(2) {
                  margin-left: -0.5rem;
                }
              }

              .itemcontent {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3;
                cursor: pointer;
                overflow: hidden;
                width: 100%;
                height: 0.3rem;
                background: #041D4F;
                // display: flex;
                font-size: 0.07rem;
                color: #fff;
                flex-wrap: wrap;
                padding: 0.02rem 0 0.1rem;
                box-sizing: border-box;

                // >div {
                //   float: left;
                //   margin-left: 0.05rem;
                // }
              }

              .item-title {
                height: 0.1rem;
                background-color: #133F9F;
                font-size: 0.08rem;
                line-height: 0.1rem;
              }

              .item-center {
                border-bottom: 1px solid #00E7FF;
                width: 0.25rem;
                margin: 0 auto;
                height: 0.15rem;
                margin-top: 0.01rem;
                font-size: 0.07rem;
                line-height: 0.15rem;
              }

              .item-bottom {
                height: 0.45rem;
                font-size: 0.06rem;
                color: #FFFFFF;

                .food-name {
                  margin-top: 0.02rem;
                }
              }
            }
          }
        }
      }

      // 自查记录
      .right-one-bottom-three {
        flex: 1;

        .dialog {
          ::v-deep .el-dialog--center {
            background-color: blue;
          }
        }

        .isNotComplete {
          opacity: 0.6;
          cursor: default !important;
        }

        .contents {
          box-sizing: border-box;
          // height: 1.47rem;
          height: calc(1.47rem - (calc(1080px - 100vh) / 4));
          background: rgba(3, 3, 29, 0.5) !important;
          padding: 0 0.09rem;
          padding-top: 0.1rem;
          display: flex;
          flex-direction: column;
          overflow: auto;

          .query-item {
            margin-bottom: 0.05rem;
            background-color: #02174C;
            border-radius: 0.02rem;
            height: 0.32rem;
            display: flex;
            justify-content: space-around;

            .item-left {
              .left-title {
                font-size: 0.07rem;
                color: #FFFFFF;
                margin-top: 0.05rem;
              }

              .left-time {
                color: #A8BEE7;
                font-size: 0.07rem;
                margin-top: 0.02rem;
              }
            }

            .item-right {
              padding: 10px 20px;
              display: flex;
              text-align: center;
              margin-top: 0.03rem;
              background: url(../../assets/img/<EMAIL>) center / 100% 100% no-repeat;
              color: #13ECFF;
              font-size: 0.07rem;
              position: relative;
              cursor: pointer;

            }
          }


          // >div:nth-of-type(1) {
          //   font-size: 0.09rem;
          //   font-family: Source Han Sans CN;
          //   font-weight: 500;
          //   color: #20C8FF;
          // }

          // >div:nth-of-type(2) {
          //   display: flex;
          //   align-items: flex-end;

          //   .day,
          //   .hour,
          //   .minute,
          //   .end {
          //     font-size: 0.08rem;
          //     font-family: Source Han Sans CN;
          //     font-weight: 500;
          //     color: #71BBFF;
          //     margin-bottom: 0.025rem;
          //   }

          //   .end {
          //     flex: 1;
          //     text-align: right;
          //   }

          //   .time {
          //     width: 0.065rem;
          //     height: 0.16rem;
          //     display: flex;
          //     align-items: center;
          //     justify-content: center;
          //     background-size: 100% 100%;
          //     background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
          //     font-size: 0.18rem;
          //     font-family: myFont;
          //     font-weight: 400;
          //     color: #71BBFF;
          //   }
          // }

          // >div:nth-of-type(3) {
          //   display: flex;
          //   justify-content: space-around;
          //   margin-top: 0.09rem;

          //   >div {
          //     text-align: center;
          //     height: 0.26rem;
          //   }

          //   .lines {
          //     background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
          //     background-size: 100% 100%;
          //     height: 0.26rem;
          //     width: 0.01rem;
          //   }

          //   .text {
          //     display: flex;
          //     flex-direction: column;
          //     justify-content: space-between;

          //     >div:nth-of-type(1) {
          //       font-size: 0.08rem;
          //       font-family: Source Han Sans CN;
          //       font-weight: 400;
          //       color: #A8BEE7;
          //     }

          //     >div:nth-of-type(2) {
          //       font-size: 0.12rem;
          //       font-family: PingFang SC;
          //       font-weight: bold;
          //     }

          //     .text-one {
          //       color: #90BDFF;
          //     }

          //     .text-two {
          //       color: #90BDFF;
          //     }

          //     .text-three {
          //       color: #32B6A6;
          //     }

          //     .text-four {
          //       color: #E94A48;
          //     }
          //   }
          // }

          // >div:nth-of-type(4) {
          //   font-size: 0.09rem;
          //   margin-top: 0.05rem;
          //   margin-left: 0.06rem;
          //   font-family: Source Han Sans CN;
          //   font-weight: 500;
          //   color: #1FC8FF;
          // }

          // >div:nth-of-type(5) {
          //   margin-top: 0.05rem;
          //   height: 0.2rem;
          //   width: 100%;
          //   background: linear-gradient(to right, rgba(6, 36, 102, .1) 0%, rgba(6, 36, 102, .8) 50%, rgba(6, 36, 102, .1) 100%);
          //   display: flex;
          //   justify-content: space-between;
          //   align-items: center;
          //   box-sizing: border-box;
          //   padding: 0 0.06rem;

          //   >div:nth-of-type(1) {
          //     font-size: 0.08rem;
          //     font-family: Source Han Sans CN;
          //     font-weight: 400;
          //     color: #A8BEE7;
          //   }

          //   >div:nth-of-type(2) {
          //     display: flex;
          //     justify-content: space-between;
          //     align-items: center;

          //     >div:nth-of-type(1) {
          //       font-size: 0.08rem;
          //       font-family: Source Han Sans CN;
          //       font-weight: 400;
          //       color: #A8BEE7;
          //     }

          //     >div:nth-of-type(2) {
          //       width: 0.35rem;
          //       height: 0.17rem;
          //       background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
          //       background-size: 100% 100%;
          //       display: flex;
          //       align-items: center;
          //       justify-content: center;
          //     }
          //   }
          // }
        }
      }
    }
  }

  // 一人一档
  .right-two-div {
    padding: 0 0.1rem;
    margin-left: 0.1rem;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    // 中间人像
    .top {
      position: relative;
      width: 100%;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      .circle {
        display: flex;
        position: relative;
        border-radius: 50%;
        justify-content: center;
        align-items: center;
        width: 2.78rem;
        height: 2.78rem;

        video {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translateX(-50%) translateY(-50%);
          z-index: 99999999;
          width: 3rem;
          height: 3rem;
        }

        .avatar,
        .type {
          position: absolute;
          top: 50%;
          left: 52%;
          transform: translateX(-50%) translateY(-50%);
          width: 1.6rem;
          height: 1.6rem;
          object-fit: cover;
          border-radius: 50%;
        }

        .type {
          background: rgba(0, 0, 0, 0.38);

          .type-name {
            width: 1.4rem;
            height: 0.26rem;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: 0.1rem;
            font-size: 0.12rem;
          }

          .type-blue {
            background: linear-gradient(to right, transparent, #3E87DB, transparent);
          }

          .type-orange {
            background: linear-gradient(to right, transparent, #81521C, transparent);
          }

          .type-red {
            background: linear-gradient(to right, transparent, #CA0000, transparent);
          }
        }
      }


      .left-info,
      .right-info {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .info3 {
          svg {
            position: relative;
            top: -0.43rem
          }
        }

        .line {
          // 动画1
          // animation: flashingFadeIn 2s ease-in-out;
        }

        .info1 {
          position: relative;
          left: 0.5rem;
          // 动画2
          // opacity: 0;
          // animation: flashingFadeIn .75s;
          // animation-fill-mode:forwards
          top: 0.1rem;
          // >div{ // 动画1
          //   animation: fadeIn .5s 2s;
          // }
        }

        .info2 {
          position: relative;
          left: -0.05rem;
          top: 0.1rem;
          // opacity: 0;
          // animation: flashingFadeIn .75s .75s;
          // animation-fill-mode:forwards;

          // >div{
          //   animation: fadeIn .5s 2.5s;
          // }
        }

        .info3 {
          position: relative;
          left: 0.2rem;
          top: 0.1rem;
          // opacity: 0;
          // animation: flashingFadeIn .75s 1.5s;
          // animation-fill-mode:forwards;

          // >div{
          //   animation: fadeIn .5s 3s;
          // }
        }

        .info4 {
          top: 0.1rem;
          position: relative;
          left: -0.5rem;
          // opacity: 0;
          // animation: flashingFadeIn .75s 2.25s;
          // animation-fill-mode:forwards;

          // >div{
          //   animation: fadeIn .5s 3.5s;
          // }
        }

        .info5 {
          position: relative;
          top: 0.1rem;
          // opacity: 0;
          // animation: flashingFadeIn .75s 3s;
          // animation-fill-mode:forwards;

          // >div{
          //   animation: fadeIn .5s 4s;
          // }
        }

        .info6 {
          position: relative;
          left: -0.2rem;
          top: 0.1rem;
          // opacity: 0;
          // animation: flashingFadeIn .75s 3.75s;
          // animation-fill-mode:forwards;

          // >div{
          //   animation: fadeIn .5s 4.5s;
          // }
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }

          to {
            opacity: 1;
          }
        }

        @keyframes flashingFadeIn {
          10% {
            opacity: 0;
          }

          20% {
            opacity: 0.6;
          }

          30% {
            opacity: 0;
          }

          40% {
            opacity: 0.6;
          }

          50% {
            opacity: 0;
          }

          60% {
            opacity: 0.6;
          }

          70% {
            opacity: 0;
          }

          80% {
            opacity: 0.6;
          }

          90% {
            opacity: 0;
          }

          100% {
            opacity: 1;
          }
        }

        >div {
          display: flex;

          >div {
            background-size: 100% 100%;
            width: 1.4rem;
            font-size: 0.08rem;
            padding: 0.14rem 0.24rem 0.13rem 0.1rem;
            margin-right: -0.07rem;
            box-sizing: border-box;

            .gray {
              color: #7D90B2;
              font-weight: bold;
            }

            .white {
              color: #ABCAFF;
              font-weight: bold;
            }

            .green {
              color: #1AD806
            }

            .red {
              color: #E31E44
            }

            .align-right {
              text-align: right;
            }

            .box-title {
              font-size: 0.09rem;
              font-weight: 400;
              color: #38D8FF;
            }
          }

          .red-box {}

          .info-box1 {
            position: relative;
            top: -0.05rem;
            height: 0.8rem;
            background: url('../../assets/img/1-blue.png');
            width: 1.44rem;

            .name {
              display: flex;
              justify-content: space-between;
            }
          }

          .info-box1-red {
            background: url('../../assets/img/1-red.png');
          }

          .info-box2 {
            position: relative;
            top: -0.05rem;
            height: 0.67rem;
            background: url('../../assets/img/2-blue.png');
            width: 1.44rem;

            .thead,
            .tbody {
              display: flex;
              justify-content: space-between;

              >div {
                width: 33%;
                text-align: center;
              }
            }
          }

          .info-box2-red {
            background: url('../../assets/img/2-red.png');
          }

          .info-box3 {
            position: relative;
            top: -0.05rem;
            height: 0.51rem;
            background: url('../../assets/img/3-blue.png');
            width: 1.44rem;

            >div:nth-child(2) {
              text-align: center;
            }
          }

          .info-box3-red {
            background: url('../../assets/img/3-red.png');
          }

          .info-box4,
          .info-box5,
          .info-box6 {
            margin-left: -0.07rem;
            margin-right: 0;
            padding: 0.14rem 0.08rem 0.13rem 0.24rem;
          }

          .info-box4 {
            position: relative;
            top: -0.05rem;
            height: 0.51rem;
            background: url('../../assets/img/4-blue.png');
            width: 1.44rem;
          }

          .info-box4-red {
            background: url('../../assets/img/4-red.png');
          }

          .info-box5 {
            position: relative;
            top: -0.05rem;
            height: 0.73rem;
            background: url('../../assets/img/5-blue.png');
            width: 1.44rem;

            .tbody {
              display: flex;
              justify-content: space-between;
            }

            .isQualified {
              position: absolute;
              right: 0.08rem;
              top: 0.08rem;
              width: 0.3rem;
            }
          }

          .info-box1,
          .info-box2,
          .info-box3 {
            .isQualified {
              position: absolute;
              left: 0.08rem;
              top: 0.08rem;
              width: 0.25rem;
            }
          }

          .info-box4,
          .info-box5,
          .info-box6 {
            .isQualified {
              position: absolute;
              right: 0.08rem;
              top: 0.08rem;
              width: 0.25rem;
            }
          }

          .info-box5-red {
            background: url('../../assets/img/5-red.png');
          }

          .info-box6 {
            position: relative;
            top: -0.05rem;
            height: 0.73rem;
            background: url('../../assets/img/5-blue.png');
            width: 1.44rem;
          }
        }
      }

      .left-info {
        // 动画3
        left: 0;
        opacity: 0;
        animation: spreadToLeft 1s 1s;
        animation-fill-mode: forwards;
      }

      .right-info {
        // 动画3
        right: 0;
        opacity: 0;
        animation: spreadToRight 1s 1s;
        animation-fill-mode: forwards;
      }

      @keyframes spreadToRight {
        from {
          position: absolute;
          right: 50%;
          opacity: 0;
        }

        to {
          position: absolute;
          right: 0;
          opacity: 1;
        }
      }

      @keyframes spreadToLeft {
        from {
          position: absolute;
          left: 50%;
          opacity: 0;
        }

        to {
          position: absolute;
          left: 0;
          opacity: 1;
        }
      }
    }

    // 晨检分析
    .bottom {
      width: 100%;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;

      .image {
        width: 0.25rem;
        height: 0.15rem;
      }

      >div:nth-child(1) {
        >.contents {
          width: 2.3rem;
        }
      }

      >div:nth-child(2) {

        >.contents {
          display: flex;
          justify-content: space-around;

          .table {
            flex: auto;

            .td:nth-child(1) {
              width: 28%;
            }

            .td:nth-child(2) {
              width: 15%;
            }

            .td:nth-child(3) {
              width: 37%;
            }

            .td:nth-child(4) {
              width: 20%;
            }

            .zc {
              padding: 0.04rem 0.08rem;
              background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
              background-size: 100% 100%;
            }

            .yc {
              padding: 0.04rem 0.08rem;
              background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
              background-size: 100% 100%;
            }
          }
        }
      }

      .contents {
        box-sizing: border-box;
        width: 4.27rem;
        height: calc(1rem - (calc(1080px - 100vh) / 4));
        background: rgba(3, 3, 29, 0.5) !important;
        padding: 0.05rem 0 0.1rem;

        .left {
          width: 40%;
          padding-left: 0.25rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;

          .left-one {
            .echarts {
              background-image: url('../../assets/img/<EMAIL>');
              background-size: 100% 100%;
              width: 0.6rem;
              height: 0.6rem;
              display: flex;
              align-items: center;
              justify-content: center;

            }

            .text {
              font-size: 0.08rem;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #A8BEE7;
              text-align: center;
              width: 0.65rem;
            }
          }

          .left-two {
            >div {
              position: relative;
              width: 0.8rem;
              height: 0.23rem;
              display: flex;
              align-items: center;
              justify-content: center;
              border: 1px solid rgba(61, 171, 255, 0.2);

              .left-top {
                position: absolute;
                top: 0;
                left: 0;
                width: 0.05rem;
                height: 0.05rem;
                border-top: 1px solid #3DABFF;
                border-left: 1px solid #3DABFF;
              }

              .right-top {
                position: absolute;
                top: 0;
                right: 0;
                width: 0.05rem;
                height: 0.05rem;
                border-top: 1px solid #3DABFF;
                border-right: 1px solid #3DABFF;
              }

              .left-bottom {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 0.05rem;
                height: 0.05rem;
                border-bottom: 1px solid #3DABFF;
                border-left: 1px solid #3DABFF;
              }

              .right-bottom {
                position: absolute;
                bottom: 0;
                right: 0;
                width: 0.05rem;
                height: 0.05rem;
                border-bottom: 1px solid #3DABFF;
                border-right: 1px solid #3DABFF;
              }
            }

            >div:nth-of-type(1) {
              margin-bottom: 0.11rem;
            }
          }
        }

        .lines {
          background-image: url('../../assets/img/wisdomFoodSafety/<EMAIL>');
          background-size: 100% 100%;
          // height: 0.75rem;
          height: calc(0.8rem - (calc(1080px - 100vh) / 4));
          width: 0.01rem;
          margin: 0 0 0 0.1rem;
        }

        .thead,
        .tbody {
          width: 100%;

          .tr {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }

          .td {
            display: flex;
            align-items: center;
            justify-content: center;
            // height: 0.2rem;
            // height: calc(0.2rem - (calc(1080px - 100vh) / 4));
          }

          .td:nth-of-type(1) {
            width: 33%;
          }

          .td:nth-of-type(2) {
            width: 33%;
          }

          .td:nth-of-type(3) {
            width: 33%;

            .circle {
              width: 0.04rem;
              height: 0.04rem;
              background: #F03E06;
              border-radius: 50%;
            }
          }

        }

        .thead {
          font-size: 0.08rem;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #FFFFFF;

          .td {
            height: 0.2rem;
          }
        }

        .tbody {
          height: calc(100% - 0.2rem);

          .tr:nth-child(odd) {
            background: linear-gradient(to right, rgba(6, 36, 102, .2) 0%, rgba(6, 36, 102, .8) 50%, rgba(6, 36, 102, .2) 100%);
          }

          font-size: 0.07rem;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #CDDFFF;
        }

        .right {
          width: 100%;
        }
      }

    }

    .two-div-right {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      // 人员档案
      .two-div-right-one {

        .contents {
          box-sizing: border-box;
          width: 2.25rem;
          // height: 1.47rem;
          height: calc(1.5rem - (calc(1080px - 100vh) / 4));
          background: rgba(3, 3, 29, 0.5) !important;
          // padding: 0 0.09rem;
          // padding-top: 0.1rem;
          padding: 0 0.15rem;

          >div {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          display: flex;
          justify-content: space-between;

          .left {
            width: 0.86rem;
            height: 1.06rem;
            object-fit: cover;
          }

          .right {
            flex: 1;
            margin-left: 0.07rem;
            height: 1.06rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            >div {
              >div:nth-of-type(1) {
                color: rgba(125, 144, 178, 1);
                font-size: 0.08rem;
                margin-bottom: 0.01rem;
              }

              >div:nth-of-type(2) {
                color: rgba(171, 202, 255, 1);
                font-size: 0.08rem;
                font-weight: bold;
              }
            }
          }
        }
      }

      // 食安培训
      .two-div-right-two {

        .contents {
          box-sizing: border-box;
          width: 2.25rem;
          // height: 1.47rem;
          height: calc(0.67rem - (calc(1080px - 100vh) / 4));
          background: rgba(3, 3, 29, 0.5) !important;
          padding: 0 0.2rem;
          display: flex;
          justify-content: space-around;
          align-items: center;

          >div:nth-of-type(1) {
            text-align: center;

            >div:nth-of-type(1) {
              font-size: 0.16rem;
              font-family: PingFang SC;
              font-weight: bold;
              color: #33B6A6;
            }

            >div:nth-of-type(2) {
              font-size: 0.09rem;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #BACEEB;
            }
          }

          >div:nth-of-type(2) {
            text-align: center;

            >div:nth-of-type(1) {
              font-size: 0.16rem;
              font-family: PingFang SC;
              font-weight: bold;
              color: #F6CB35;
            }

            >div:nth-of-type(2) {
              font-size: 0.09rem;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #BACEEB;
            }
          }

          >div:nth-of-type(3) {
            img {
              width: 0.52rem;
              height: 0.41rem;
            }
          }
        }
      }
    }
  }

  // 一企一档
  .right-three-div {
    margin-left: 0.1rem;
    height: 100%;
    // border: 0.005rem solid #216DFD;
    box-sizing: border-box;
    display: flex;
    // background: rgba(34, 114, 255, 0.1) !important;

    .three-div-left {
      flex: 1;
      background-image: radial-gradient(rgba(13, 43, 129, 1) 0%, rgba(13, 43, 129, 0.4) 50%, rgba(13, 43, 129, 0.1) 70%, rgba(13, 43, 129, 0) 100%);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      position: relative;

      .no-tree-data {
        display: flex;
        width: 1350px;
        height: 700px;
        justify-content: center;
        align-items: center;
        font-size: 0.12rem;
        color: #75F0FC
      }

      .div-left-top {
        height: 100%;
        .chart-box{
          height: 85%;
          overflow: hidden;
          position: relative;
          .supplierToCenter,
          .originToSupplier{
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
          }
          .supplierToCenter{
            left: 680px;
          }
          .originToSupplier{
            left: 0;
          }
        }


        .select-conditions {
          display: flex;
          justify-content: center;

          .right-arrow {
            transform: rotate(180deg);
            margin-left: 0.05rem;
          }

          .date-picker {
            font-size: 0.09rem;
            display: flex;
            align-items: center;
            color: #86C6FF;
          }
        }

        .legend {
          position: absolute;
          bottom: 0.1rem;
          left: 50px;
          width: 2rem;
          height: 0.18rem;
          background: #081750;
          background-size: 100% 100%;
          display: flex;
          padding: 5px 20px;
          box-sizing: border-box;
          justify-content: space-between;

          .item {
            display: flex;
            align-items: center;

            .icon {
              margin-right: 5px;
              width: 14px;
              height: 9px;
              background: rgba(0, 168, 255, 0.5);
              border-radius: 2px;
            }

            .name {
              font-size: 13px;
              color: #86C6FF;
            }
          }

          .item:nth-child(2) {
            .icon {
              background: #4358C9;
            }
          }

          .item:nth-child(3) {
            .icon {
              background: #328E85;
            }
          }

          .item:nth-child(4) {
            .icon {
              background: #1381A1;
            }
          }

          .item:nth-child(5) {
            .icon {
              background: #A26827;
            }
          }
        }
      }

    }

    .rightState {
      right: -2rem !important;
    }

    .three-div-right {
      transition: all .5s;
      position: absolute;
      right: 0.16rem;
      overflow: hidden;
      width: 2.15rem;
      height: calc(100% - 0.4rem);
      display: flex;
      flex-direction: column;

      // 供应商详情
      .three-div-right-one {

        height: 100%;
        position: relative;

        .shrink {
          position: absolute;
          left: 0;
          top: 48%;
          width: 20px;
          height: 100px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          box-shadow: inset 0 0 5px #0936b0;
        }
        .box{
          background: #071548;
        }
        .contents {
          box-sizing: border-box;
          width: 100%;
          height: 95%;
          padding: 0.15rem;
          box-sizing: border-box;
          // padding-top: 0.1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;

          .charts {
            font-size: 0.08rem;
            flex: 1;
            width: 100%;
            color: #CDDFFF;

            .line {
              margin: -0.02rem auto;
              width: 0px;
              height: calc(0.35rem - (calc(1080px - 100vh)/3));
              border: 1px solid #1C7BF0;
            }

            .judge-false,
            .judge-true {
              color: #76F0FC;
              font-size: 0.1rem;
              width: 0.57rem;
              height: 0.57rem;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            @keyframes easyFadeIn {
              from {
                opacity: 0;
              }

              to {
                opacity: 1;
              }
            }

            .link1 {
              opacity: 0;
              animation: easyFadeIn 1s;
              animation-fill-mode: forwards;
            }

            .line1 {
              opacity: 0;
              animation: easyFadeIn 1s .5s;
              animation-fill-mode: forwards;
            }

            .link2 {
              opacity: 0;
              animation: easyFadeIn 1s 1s;
              animation-fill-mode: forwards;
            }

            .line2 {
              opacity: 0;
              animation: easyFadeIn 1s 1.5s;
              animation-fill-mode: forwards;
            }

            .link3 {
              opacity: 0;
              animation: easyFadeIn 1s 2s;
              animation-fill-mode: forwards;
            }

            .line3 {
              opacity: 0;
              animation: easyFadeIn 1s 2.5s;
              animation-fill-mode: forwards;
            }

            .link4 {
              opacity: 0;
              animation: easyFadeIn 1s 3s;
              animation-fill-mode: forwards;
            }

            .link1,
            .link3 {
              .judge-true {
                background: url('../../assets/gif/<EMAIL>');
                background-size: 100% 100%;
              }

              .judge-false {
                background: url('../../assets/gif/<EMAIL>');
                background-size: 100% 100%;

              }
            }

            .link2,
            .link4 {
              .judge-true {
                background: url('../../assets/gif/<EMAIL>');
                background-size: 100% 100%;

              }

              .judge-false {
                background: url('../../assets/gif/<EMAIL>');
                background-size: 100% 100%;

              }
            }

            >div {
              display: flex;
              justify-content: center;
              align-items: center;

              >div:nth-child(1),
              >div:nth-child(3) {
                width: 35%;
              }

              >div:nth-child(2) {
                width: 30%;
                text-align: center;
              }

              >div:nth-child(1) {
                text-align: right;
              }

              .link-name-left {
                padding-right: 0.05rem;
                box-sizing: border-box;
                width: 0.65rem;
                text-align: right;
                color: #56A1FE;
                background: linear-gradient(to right, transparent, #00419E);
              }

              .risk-place {
                margin-top: 0.05rem;
              }

              .link-name-right {
                padding-left: 0.05rem;
                box-sizing: border-box;
                width: 0.65rem;
                text-align: left;
                color: #56A1FE;
                background: linear-gradient(to left, transparent, #00419E);
              }

              .info {
                text-align: center;
              }

              .info-list {
                >div {
                  padding: 0.03rem 0;
                  text-align: center;
                }

                div:nth-child(odd) {
                  background: linear-gradient(to right, transparent, #061A5E, transparent);
                }
              }
            }
          }

          .result {
            background: url('../../assets//img/<EMAIL>');
            background-size: 100% 100%;
            width: 1.75rem;
            height: 0.7rem;
            font-size: 0.13rem;
            color: #76F0FC;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .top {
            display: flex;
            justify-content: space-between;

            .top-left {
              // height: 0.75rem;
              height: calc(0.75rem - (calc(1080px - 100vh) / 4));
              width: 1rem;
              object-fit: cover;
            }

            .top-right {
              flex: 1;
              // height: 0.75rem;
              height: calc(0.75rem - (calc(1080px - 100vh) / 4));
              margin-left: 0.085rem;
              display: flex;
              justify-content: space-between;
              flex-direction: column;

              >div {
                display: flex;
                font-size: 0.08rem;
                font-weight: bold;

                >div:nth-of-type(1) {
                  color: rgba(88, 130, 206, 1);
                }

                >div:nth-of-type(2) {
                  flex: 1;
                  color: rgba(171, 201, 255, 1);
                }
              }
            }
          }

          .bottom {
            margin-top: 0.1rem;

            >div:nth-of-type(1) {
              margin-bottom: 0.065rem;
            }

            >div {
              display: flex;
              font-size: 0.08rem;
              font-weight: bold;

              >div:nth-of-type(1) {
                color: rgba(88, 130, 206, 1);
              }

              >div:nth-of-type(2) {
                flex: 1;
                color: rgba(171, 201, 255, 1);
              }
            }
          }
        }
      }

      // 采购记录
      .three-div-right-two {
        flex: 1;
        margin-top: 0.1rem;
        height: calc(1.5rem - (calc(1080px - 100vh) / 4));

        .contents {
          box-sizing: border-box;
          width: 100%;
          height: calc(100% - 0.24rem);
          background: rgba(3, 3, 29, 0.5) !important;
          padding: 0.15rem;

          table {
            width: 100%;

            tr {
              height: 0.2rem;
              text-align: center;
            }

            thead td {
              font-size: 0.07rem;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #FFFFFF;
            }

            tbody {
              td {
                border: 1px solid rgba(0, 160, 233, 1);
                font-size: 0.07rem;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #FFFFFF;
              }
            }

          }

          .tbody-height {
            height: calc(100% - 0.15rem);
            overflow-y: auto;

            tr:nth-child(odd) {
              background-color: rgba(3, 26, 88, 0.8);
            }

            tr:nth-child(even) {
              background-color: rgba(25, 53, 128, 0.8);
            }
          }

          .tbody-height::-webkit-scrollbar {
            display: none;
          }
        }
      }
    }
  }
}
