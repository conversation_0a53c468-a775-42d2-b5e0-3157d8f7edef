/*!*! normalize.css v3.0.2 | MIT License | git.io/normalize *!html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent}a:active,a:hover{outline:0}abbr[title]{border-bottom:1px dotted}b,strong{font-weight:bold}dfn{font-style:italic}h1{font-size:2em;margin:0.67em 0}mark{background:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sup{top:-0.5em}sub{bottom:-0.25em}img{border:0}svg:not(:root){overflow:hidden}figure{margin:1em 40px}hr{-moz-box-sizing:content-box;-webkit-box-sizing:content-box;box-sizing:content-box;height:0}pre{overflow:auto}code,kbd,pre,samp{font-family:monospace, monospace;font-size:1em}button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0}button{overflow:visible}button,select{text-transform:none}button,html input[type="button"],input[type="reset"],input[type="submit"]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}input{line-height:normal}input[type="checkbox"],input[type="radio"]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:0}input[type="number"]::-webkit-inner-spin-button,input[type="number"]::-webkit-outer-spin-button{height:auto}input[type="search"]{-webkit-appearance:textfield;-moz-box-sizing:content-box;-webkit-box-sizing:content-box;box-sizing:content-box}input[type="search"]::-webkit-search-cancel-button,input[type="search"]::-webkit-search-decoration{-webkit-appearance:none}fieldset{border:1px solid #c0c0c0;margin:0 2px;padding:0.35em 0.625em 0.75em}legend{border:0;padding:0}textarea{overflow:auto}optgroup{font-weight:bold}table{border-collapse:collapse;border-spacing:0}td,th{padding:0}*/
body, html { padding: 0; margin: 0;overflow: hidden}


/* Clearfix hack by Nicolas Gallagher: http://nicolasgallagher.com/micro-clearfix-hack/ */
.clearfix:before,
.clearfix:after {
    content: " ";
    display: table;
}

.clearfix:after {
    clear: both;
}
ul{list-style: none}

/*body{*/
    /*font-family: "Segoe UI", "Lucida Grande", Helvetica, Arial, "Microsoft YaHei", FreeSans, Arimo, "Droid Sans", "wenquanyi micro hei", "Hiragino Sans GB", "Hiragino Sans GB W3", "FontAwesome", sans-serif;*/
/*}*/
a{color: #2fa0ec;text-decoration: none;outline: none;}
a:hover,a:focus{color:#74777b;}
* {
    box-sizing: border-box;
}


.content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    /*display: grid;*/
    /*place-items: center;*/
    width: 100%;
    height: 100%;
    background: url("../../assets/img/beijing.png");
    background-size: 100% 100%;
    color: #a4afc1
  }
/* =========================================
Stark Industries Logo
========================================= */
#logo {
    animation: logo-entry 4s ease-in;
    width: 100%;
    margin: 0 auto;
    position: relative;
    z-index: 40;
}

h1.hogo {
    width: 1100px;
    animation: text-glow 2s ease-out infinite alternate;
    font-family: 'Ubuntu', sans-serif;
    color: #03a9f4;
    font-size: 0.3rem;
    font-weight: bold;
    position: absolute;
    text-shadow: 0 0 10px #000, 0 0 20px #000, 0 0 30px #000, 0 0 40px #000, 0 0 50px #000, 0 0 60px #000, 0 0 70px #000;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
}
h1.hogo:before {
    animation: before-glow 2s ease-out infinite alternate;
    border-left: 535px solid transparent;
    border-bottom: 10px solid #03a9f4;
    content: ' ';
    height: 0;
    position: absolute;
    right: -74px;
    top: -10px;
    width: 0;
}
h1.hogo:after {
    animation: after-glow 2s ease-out infinite alternate;
    border-left: 100px solid transparent;
    border-top: 16px solid #03a9f4;
    content: ' ';
    height: 0;
    position: absolute;
    right: -85px;
    top: 24px;
    transform: rotate(-47deg);
    width: 0;
}

/* =========================================
Log in form
========================================= */
#fade-box {
    animation: input-entry 3s ease-in;
    z-index: 4;
}

.stark-login .form {
    animation: form-entry 3s ease-in-out;
    background: #111;
    background: linear-gradient(#004876, #111111);
    border: 5px solid #0088ff;
    box-shadow: 0 0 50px #0069ff;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 275px;
    /* height: 235px; */
    margin: 175px auto 0;
    position: relative;
    z-index: 4;
    width: 500px;
    transition: 1s all;
}
.stark-login .form:hover {
    border: 5px solid #00a1ed;
    box-shadow: 0 0 25px #044B84;
    transition: 1s all;
}
.stark-login input {
    background: #222;
    background: linear-gradient(#333333, #222222);
    border: 1px solid #444;
    border-radius: 5px;
    box-shadow: 0 2px 0 #000;
    color: #888;
    display: block;
    font-family: 'Cabin', helvetica, arial, sans-serif;
    font-size: 0.12rem;
    height: 40px;
    margin: 0 auto ;
    padding: 0 10px;
    text-shadow: 0 -1px 0 #000;
    width: 400px;
}
.stark-login input:focus {
    animation: box-glow 1s ease-out infinite alternate;
    background: #0B4252;
    background: linear-gradient(#333933, #222922);
    border-color: #03a9f4;
    box-shadow: 0 0 5px rgba(0, 255, 253, 0.2), inset 0 0 5px rgba(0, 255, 253, 0.1), 0 2px 0 #000;
    color: #efe;
    outline: none;
}
.stark-login input:invalid {
    border: 1px solid #03a9f4;
}
.stark-login button {
    animation: input-entry 3s ease-in;
    background: #222;
    background: linear-gradient(#333333, #222222);
    box-sizing: content-box;
    border: 1px solid #444;
    border-left-color: #000;
    border-radius: 5px;
    box-shadow: 0 2px 0 #000;
    color: #fff;
    display: block;
    font-family: 'Cabin', helvetica, arial, sans-serif;
    font-size: 13px;
    font-weight: 400;
    height: 40px;
    line-height: 40px;
    margin: 0 auto;
    padding: 0;
    position: relative;
    text-shadow: 0 -1px 0 #000;
    width: 400px;
    transition: 1s all;
}
.stark-login button:hover,
.stark-login button:focus {
    background: #0C6125;
    background: linear-gradient(#393939, #292929);
    /*color: #03a9f4;*/
    outline: none;
    transition: 1s all;
}
.stark-login button:active {
    background: #292929;
    background: linear-gradient(#393939, #292929);
    box-shadow: 0 1px 0 #000, inset 1px 0 1px #222;
    top: 1px;
}

/* =========================================
Spinner
========================================= */
#circle1 {
    animation: circle1 4s linear infinite, circle-entry 6s ease-in-out;
    background: #000;
    border-radius: 50%;
    border: 10px solid #03a9f490;
    box-shadow: 0 0 0 2px black, 0 0 0 6px #03a9f4;
    height: 500px;
    width: 500px;
    position: absolute;
    top: 20px;
    left: 50%;
    margin-left: -250px;
    overflow: hidden;
    opacity: 0.4;
    z-index: -3;
}

#inner-cirlce1 {
    background: #000;
    border-radius: 50%;
    border: 36px solid #03a9f4;
    height: 460px;
    width: 460px;
    margin: 10px;
}
#inner-cirlce1:before {
    content: ' ';
    width: 240px;
    height: 480px;
    background: #000;
    position: absolute;
    top: 0;
    left: 0;
}
#inner-cirlce1:after {
    content: ' ';
    width: 480px;
    height: 240px;
    background: #000;
    position: absolute;
    top: 0;
    left: 0;
}

/* =========================================
Hexagon Mesh
========================================= */
.hexagons {
    animation: logo-entry 4s ease-in;
    color: #000;
    font-size: 52px;
    font-size: 0.5rem;
    letter-spacing: -0.2em;
    line-height: 0.7;
    position: absolute;
    text-shadow: 0 0 6px #03a9f4;
    top: 310px;
    width: 100%;
    transform: perspective(600px) rotateX(60deg) scale(1.4);
    z-index: -3;
}

/* =========================================
Animation Keyframes
========================================= */
@keyframes logo-entry {
    0% {
        opacity: 0;
    }
    80% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@keyframes circle-entry {
    0% {
        opacity: 0;
    }
    20% {
        opacity: 0;
    }
    100% {
        opacity: 0.4;
    }
}
@keyframes input-entry {
    0% {
        opacity: 0;
    }
    90% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@keyframes form-entry {
    0% {
        height: 0;
        width: 0;
        opacity: 0;
        padding: 0;
    }
    20% {
        height: 0;
        /*border: 1px solid #00a4a2;*/
        width: 0;
        opacity: 0;
        padding: 0;
    }
    40% {
        width: 0;
        height: 275px;
        /*border: 6px solid #00a4a2;*/
        opacity: 1;
        padding: 0;
    }
    100% {
        height: 275px;
        width: 500px;
    }
}
@keyframes box-glow {
    0% {
        border-color: #03a9f4;
        box-shadow: 0 0 5px rgba(0, 180, 255, 0.4), inset 0 0 5px rgba(0, 180, 255, 0.2), 0 2px 0 #000;
    }
    100% {
        border-color: #03a9f4;
        box-shadow: 0 0 20px rgba(0, 180, 255, 0.8), inset 0 0 10px rgba(0, 180, 255, 0.6), 0 2px 0 #000;
    }
}
@keyframes text-glow {
    0% {
        color: #009fff;
        text-shadow: 0 0 10px #000, 0 0 20px #000, 0 0 30px #000, 0 0 40px #000, 0 0 50px #000, 0 0 60px #000, 0 0 70px #000;
    }
    100% {
        color: #0090ff;
        text-shadow: 0 0 20px rgba(0, 150, 253, 0.6), 0 0 10px rgba(0, 150, 253, 0.4), 0 2px 0 #003aff;
    }
}
@keyframes before-glow {
    0% {
        border-bottom: 10px solid #0088ff;
    }
    100% {
        border-bottom: 10px solid #0991ff;
    }
}
@keyframes after-glow {
    0% {
        border-top: 16px solid #0088ff;
    }
    100% {
        border-top: 16px solid #0991ff;
    }
}
@keyframes circle1 {
    0% {
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
