<template>
  <div
    ref="container"
    style="width:100%; height: 100%; background-color: #000000;margin:0 auto;position: relative;"
    @dblclick="fullscreenSwich"
  >
    <div style="width:100%; padding-top: 56.25%; position: relative;" />
    <div class="buttons-box">
      <div class="buttons-box-left">
        <i
          v-if="!playing"
          class="iconfont icon-play jessibuca-btn"
          @click="playBtnClick"
        />
        <i
          v-if="playing"
          class="iconfont icon-pause jessibuca-btn"
          @click="pause"
        />
        <i class="iconfont icon-stop jessibuca-btn" @click="destroy" />
        <i
          v-if="isNotMute"
          class="iconfont icon-audio-high jessibuca-btn"
          @click="mute()"
        />
        <i
          v-if="!isNotMute"
          class="iconfont icon-audio-mute jessibuca-btn"
          @click="cancelMute()"
        />
      </div>
      <div class="buttons-box-right">
        <!-- <span class="jessibuca-btn">{{ kBps }} kb/s</span> -->
        <!--          <i class="iconfont icon-file-record1 jessibuca-btn"></i>-->
        <!--          <i class="iconfont icon-xiangqing2 jessibuca-btn" ></i>-->
        <i
          class="iconfont icon-camera1196054easyiconnet jessibuca-btn"
          style="font-size: 1rem !important"
          @click="screenshot"
        />
        <!-- <i
            class="iconfont icon-shuaxin11 jessibuca-btn"
            @click="playBtnClick"
          /> -->
        <i
          v-if="!fullscreen"
          class="iconfont icon-weibiaoti10 jessibuca-btn"
          @click="fullscreenSwich"
        />
        <i
          v-if="fullscreen"
          class="iconfont icon-weibiaoti11 jessibuca-btn"
          @click="fullscreenSwich"
        />
      </div>
    </div>
  </div>
  <div
    v-if="!playing && !autoplay"
    class="btn-image"
    :style="'height:' + height"
  >
    <div
      class="btn-image-center"
      :style="
        'width:' +
          btnimageW +
          ';height: ' +
          btnimageH +
          ';background:url(' +
          btnimage +
          ') no-repeat;background-size: 100% 100%;'
      "
      @click="playBtnClick"
    ></div>
  </div>
  </div>
</template>

<script>
/* eslint-disable no-underscore-dangle */
export default {
  name: 'Jessibuca',
  props: {
    videoUrl: {
      // 播放地址
      type: String,
      default: '',
    },
    error: {
      // 报错信息
      type: Function,
      default: null,
    },
    hasAudio: {
      // 静音
      type: Boolean,
      default: false,
    },
    height: {
      // 播放器高度
      type: String,
      default: '500px',
    },
    isFullResize: {
      // 播放面即是否填充满容器
      type: Boolean,
      default: true,
    },
    autoplay: {
      // 是否自动播放
      type: Boolean,
      default: false,
    },
    background: {
      // 封面图
      type: String,
      default: '',
    },
    btnimage: {
      // 播放器按钮图片
      type: String,
      default: '',
    },
    btnimageH: {
      // 播放器按钮图片高度
      type: String,
      default: '60px',
    },
    btnimageW: {
      // 播放器按钮图片宽度
      type: String,
      default: '60px',
    },
  },
  data() {
    return {
      jessibuca: null,
      playing: false,
      isNotMute: false,
      quieting: false,
      fullscreen: false,
      loaded: false, // mute
      speed: 0,
      performance: '', // 工作情况
      kBps: 0,
      btnDom: null,
      volume: 1,
      rotate: 0,
      vod: true, // 点播
      forceNoOffscreen: false,
      playerWidth: 0,
      playerHeight: 0,
      parentNodeResizeObserver: null,
    }
  },
  watch: {
    videoUrl: {
      handler(newData, oldData) {
        if (!this.autoplay && !oldData) return
        this.play(JSON.parse(JSON.stringify(newData)))
      },
    },
  },
  mounted() {
    window.onerror = (msg) => {
      // console.error(msg)
    }
    const paramUrl = decodeURIComponent(this.$route.params.url)
    this.$nextTick(() => {
      this.updatePlayerDomSize()
      window.onresize = this.updatePlayerDomSize
      if (typeof this.videoUrl === 'undefined') {
        this.videoUrl = paramUrl
      }
      console.log(
        `初始化时的地址为: ${JSON.parse(JSON.stringify(this.videoUrl))}`
      )
      this.autoplay ? this.play(JSON.parse(JSON.stringify(this.videoUrl))) : ''
      this.btnDom = document.querySelector('.buttons-box')
    })
  },
  destroyed() {
    if (this.jessibuca) {
      this.jessibuca.destroy()
    }
    if (this.parentNodeResizeObserver) {
      this.parentNodeResizeObserver.disconnect()
    }
    this.playing = false
    this.loaded = false
    this.performance = ''
  },
  methods: {
    updatePlayerDomSize() {
      const dom = this.$refs.container
      if (!this.parentNodeResizeObserver) {
        this.parentNodeResizeObserver = new ResizeObserver(entries => {
          this.updatePlayerDomSize()
        })
        this.parentNodeResizeObserver.observe(dom.parentNode)
      }
      const boxWidth = dom.parentNode.clientWidth
      const boxHeight = dom.parentNode.clientHeight
      let width = boxWidth
      let height = (9 / 16) * width
      if (boxHeight > 0 && boxWidth > boxHeight / 9 * 16) {
        height = boxHeight
        width = boxHeight / 9 * 16
      }

      const clientHeight = Math.min(document.body.clientHeight, document.documentElement.clientHeight)
      if (height > clientHeight) {
        height = clientHeight
        width = (16 / 9) * height
      }
      this.playerWidth = width
      this.playerHeight = height
      if (this.playing && this.jessibuca) {
        this.jessibuca.resize(this.playerWidth, this.playerHeight)
      }
    },
    create() {
      const options = {}
      console.log(this.$refs.container)
      console.log(`hasAudio  ${this.hasAudio}`)

      this.jessibuca = new window.Jessibuca(
        Object.assign(
          {
            container: this.$refs.container,
            isResize: true,
            isFlv: true,
            decoder: 'jessibuca/decoder.js',
            // text: "WVP-PRO",
            // background: "bg.jpg",
            loadingText: '加载中',
            hasAudio:
              typeof this.hasAudio === 'undefined' ? true : this.hasAudio,
            debug: false,
            supportDblclickFullscreen: false, // 是否支持屏幕的双击事件，触发全屏，取消全屏事件。
            operateBtns: {
              fullscreen: false,
              screenshot: false,
              play: false,
              audio: false,
            },
            record: 'record',
            isFullResize: this.isFullResize || false,
            vod: this.vod,
            forceNoOffscreen: this.forceNoOffscreen,
            isNotMute: this.isNotMute,

            autoWasm: true,
            background: '',
            controlAutoHide: false,
            heartTimeout: 5,
            heartTimeoutReplay: true,
            heartTimeoutReplayTimes: 3,
            hiddenAutoPause: false,
            hotKey: true,
            keepScreenOn: true,
            loadingTimeout: 10,
            loadingTimeoutReplay: true,
            loadingTimeoutReplayTimes: 3,
            openWebglAlignment: false,
            recordType: 'mp4',
            rotate: 0,
            showBandwidth: false,
            timeout: 10,
            useMSE: true,
            useWCS: false,
            useWebFullScreen: true,
            videoBuffer: 0.1,
            wasmDecodeErrorReplay: true,
            wcsUseVideoRender: true
          },
          options
        )
      )
      // eslint-disable-next-line no-underscore-dangle
      const _this = this
      console.log(this.jessibuca, 'ddddddddd')
      this.jessibuca.on('load', () => {
        console.log('on load init')
      })

      this.jessibuca.on('pause', () => {
        _this.playing = false
      })
      this.jessibuca.on('play', () => {
        _this.playing = true
      })

      this.jessibuca.on('fullscreen', (msg) => {
        _this.fullscreen = msg
      })

      this.jessibuca.on('mute', (msg) => {
        console.log('on mute', msg)
        _this.isNotMute = !msg
      })

      this.jessibuca.on('bps', (bps) => {
        // console.log('bps', bps);
      })
      // eslint-disable-next-line no-unused-vars
      let _ts = 0
      this.jessibuca.on('timeUpdate', (ts) => {
        // console.log('timeUpdate,old,new,timestamp', _ts, ts, ts - _ts);
        _ts = ts
      })

      this.jessibuca.on('error', (error) => {
        console.log('error', error)
      })

      this.jessibuca.on('timeout', () => {
        console.log('timeout')
      })

      this.jessibuca.on('start', () => {
        console.log('start')
      })

      this.jessibuca.on('performance', (performance) => {
        let show = '卡顿'
        if (performance === 2) {
          show = '非常流畅'
        } else if (performance === 1) {
          show = '流畅'
        }
        _this.performance = show
      })
      this.jessibuca.on('buffer', (buffer) => {
        // console.log('buffer', buffer);
      })

      this.jessibuca.on('stats', (stats) => {
        // console.log('stats', stats);
      })

      this.jessibuca.on('kBps', (kBps) => {
        _this.kBps = Math.round(kBps)
      })

      // 显示时间戳 PTS
      this.jessibuca.on('videoFrame', () => {})

      //
      this.jessibuca.on('metadata', () => {})
    },
    playBtnClick(event) {
      this.play(this.videoUrl)
    },
    play(url) {
      if (this.jessibuca) {
        this.destroy()
      }
      console.log(url, 'urla')

      this.create()
      console.log(this.jessibuca, 'urlb')

      this.jessibuca.on('play', () => {
        this.playing = true
        this.loaded = true
        this.quieting = this.jessibuca.quieting
        console.log(url, 'urlc')
      })
      console.log(url, 'urld')
      if (this.jessibuca.hasLoaded()) {
        console.log(url, 'url1')
        this.jessibuca.play(url)
        console.log(url, 'url2')
      } else {
        console.log(this.jessibuca, 'ddswec')
        this.jessibuca.on('load', () => {
          console.log(url, 'load 播放1')
          this.jessibuca.play(url)
          console.log(url, 'load 播放2')
        })
      }
    },
    pause() {
      if (this.jessibuca) {
        this.jessibuca.pause()
      }
      this.playing = false
      this.err = ''
      this.performance = ''
    },
    mute() {
      if (this.jessibuca) {
        this.jessibuca.mute()
      }
    },
    cancelMute() {
      if (this.jessibuca) {
        this.jessibuca.cancelMute()
      }
    },
    screenshot() {
      if (this.jessibuca) {
        this.jessibuca.screenshot()
      }
    },
    destroy() {
      if (this.jessibuca) {
        this.jessibuca.destroy()
      }
      if (this.btnDom && !document.querySelector('.buttons-box')) {
        this.$refs.container.appendChild(this.btnDom)
      }
      this.jessibuca = null
      this.playing = false
      this.err = ''
      this.performance = ''
    },
    eventcallbacK(type, message) {
      // console.log("player 事件回调")
      // console.log(type)
      // console.log(message)
    },
    fullscreenSwich() {
      const isFull = this.isFullscreen()
      if (this.jessibuca) {
        this.jessibuca.setFullscreen(!isFull)
      }
      this.fullscreen = !isFull
    },
    isFullscreen() {
      return document.fullscreenElement ||
        document.msFullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement || false
    },
  },
}
</script>

<style>

@font-face {
  font-family: 'iconfont'; /* Project id 1291092 */
  src: url('iconfont.woff2?t=1637741914969') format('woff2');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-play:before {
  content: '\e603';
}

.icon-pause:before {
  content: '\e6c6';
}

.icon-stop:before {
  content: '\e6a8';
}

.icon-audio-high:before {
  content: '\e793';
}

.icon-audio-mute:before {
  content: '\e792';
}

.icon-shuaxin11:before {
  content: '\e720';
}

.icon-weibiaoti10:before {
  content: '\e78f';
}

.icon-weibiaoti11:before {
  content: '\e790';
}

.icon-camera1196054easyiconnet:before {
  content: '\e791';
}

.buttons-box {
  width: 100%;
  height: 28px;
  background-color: rgba(43, 51, 63, 0.7);
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  bottom: 0;
  user-select: none;
  z-index: 10;
}

.jessibuca-btn {
  width: 20px;
  color: rgb(255, 255, 255);
  line-height: 27px;
  margin: 0px 10px;
  padding: 0px 2px;
  cursor: pointer;
  text-align: center;
  font-size: 0.8rem !important;
}

.buttons-box-right {
  position: absolute;
  right: 0;
}
.btn-image {
  width: 100%;
  /* height: 100%; */
  position: absolute;
  left: 0;
  top: 0;
  z-index: 99 !important;
}
.btn-image-center {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: auto;
}
</style>
