<template>
  <div class="selectdanger">
    <!-- 视频播放 -->
    <div class="left-select">
      <!-- <LivePlayer
        :video-url="linelist.flv"
        :hasaudio="false"
        style="width: 100%"
        live
        loop
        stretch
        :autoplay="false"
      /> -->
      <Player
        v-if="linelist.wssFlv"
        :video-url="linelist.wssFlv"
        :has-audio="false"
        height="110px"
        style="width: 0.93rem"
        :is-full-resize="true"
        :autoplay="false"
      />
      <div v-else style="text-align: center;width: 0.93rem">暂无回放</div>
      <!-- <img src="" alt=""> -->
    </div>
    <!-- 选择数据 -->
    <div class="right-select">
      <div class="row">
        <div class="title">选择项目点:</div>
        <div class="select" style="background-image: none">
          <el-select
            v-model="indexId"
            placeholder="请选择"
            @change="indexSelect01"
          >
            <el-option
              v-for="item in select01"
              :key="item.value"
              :label="
                item.isHoliday
                  ? item.centerName + '（已歇业）'
                  : item.centerName
              "
              prefix-icon="none"
              :value="item.centerId"
            />
          </el-select>
        </div>
      </div>
      <div class="row" style="margin-left: 0.08rem">
        <div class="title">摄像头:</div>
        <div class="select" style="background-image: none">
          <el-select
            v-model="indexId2"
            placeholder="请选择"
            prefix-icon="none"
            @change="indexSelect02"
          >
            <el-option
              v-for="item in select02"
              :key="item.value"
              :label="item.monitorName"
              :value="item.monitorName"
            />
          </el-select>
        </div>
      </div>
      <div class="row">
        <div class="title">开始时间:</div>
        <div class="select" style="background-image: none">
          <el-date-picker
            v-model="value1"
            type="datetime"
            format="HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            prefix-icon="none"
            :clearable="false"
            style="color: white"
            placeholder="请选择时间"
            :picker-options="pickerOptions"
            @change="changeTime"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LivePlayer from '@liveqing/liveplayer'
import Player from '@/components/jessibucaPlayer/jessibuca'
import { cameraList, fetchPlayback, keepPlayback } from '@/api/brightKitchen'
import viewMixins from '@/views/mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
import moment from 'moment'
export default {
  name: 'Selectfrom',
  components: {
    LivePlayer,
    Player,
  },
  mixins: [viewMixins, componentMixin],
  props: {},
  data() {
    return {
      select01: [],
      select02: [],
      indexId: '请选择项目点',
      indexId2: '请选择设备',
      indexObj2: {},
      indexNum: 0,
      infourl: '',
      linelist: {},
      streamId: '',
      value1: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      },
    }
  },
  computed: {},
  watch: {
    indexId2(newVal, oldVal) {
      if (newVal) {
        // this.indexSelect02()
      }
    },
  },
  created() {
    this.formatTime()
  },
  beforeDestroy() {
    this.linelist.wssFlv = undefined
  },
  methods: {
    // 获取当前时间
    formatTime() {
      const nowTime = new Date().getTime()
      const lunchTime = new Date(
        moment(new Date().getTime()).format('YYYY-MM-DD') + ' 12:10:00'
      ).getTime()
      let startTime
      if (nowTime < lunchTime) {
        startTime =
          moment(new Date().getTime() - 86400000).format('YYYY-MM-DD') +
          ' 12:00:00'
      } else {
        startTime =
          moment(new Date().getTime()).format('YYYY-MM-DD') + ' 12:00:00'
      }
      this.value1 = startTime
      this.cameraList()
    },
    // 改变开始时间
    changeTime() {
      const datas = {
        deviceSerial: this.indexObj2.device,
        channelSerial: this.indexObj2.channel,
        startTime: this.value1,
      }
      fetchPlayback(datas).then((res) => {
        this.streamId = res.data.data.streamId
        this.linelist = res.data.data
        // keepPlayback(res.data.data.streamId)
      })
    },
    // 初始化获取数据
    cameraList() {
      // this.value1 =Time
      const code = this.DISTRICT_CODE['大邑县']
      cameraList(code).then((res) => {
        // this.centerlist = this.centerlist[0].monitorList[0]
        this.select01 = res.data.data
        this.indexSelect01()
        this.indexId = this.select01[0].centerName
        this.indexId2 = this.select02[0].monitorName
        this.indexObj2 = this.select02[0]
        const datas = {
          deviceSerial: this.indexObj2.device,
          channelSerial: this.indexObj2.channel,
          startTime: this.value1,
        }
        fetchPlayback(datas).then((ress) => {
          this.streamId = ress.data.data?.streamId
          this.linelist = ress.data.data
        })
      })
    },
    // 设备选择后
    indexSelect02() {
      // indexId2  item item.name== name return device
      const mapData = this.select02.filter((item) => {
        return item.monitorName === this.indexId2
      })
      this.indexObj2 = mapData[0]
      const datas = {
        deviceSerial: this.indexObj2.device,
        channelSerial: this.indexObj2.channel,
        startTime: this.value1,
      }
      fetchPlayback(datas).then((res) => {
        this.streamId = res.data.data?.streamId
        this.linelist = res.data.data
      })
    },
    // 项目点选择后
    indexSelect01() {
      console.log('---------------------------------1')
      let i = 0
      for (i = 0; i < this.select01.length; i++) {
        if (this.select01[i].centerId === this.indexId) {
          this.indexNum = i
          break
        }
      }
      this.select02 = this.select01[this.indexNum].monitorList
      this.indexId2 = this.select02[0].monitorName
    },
  },
}
</script>

<style scoped lang="scss">
.selectdanger {
  width: 2.25rem;
  // height: 0.46rem;
  height: calc(0.8rem - (calc(1080px - 100vh) / 4));
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0.05rem 0.15rem;
  background: rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0 40px #0936b0;
  // text-align: center;
  color: #9eb2d8;
  .left-select {
    height: 100%;
    width: 47%;
    display: flex;
    align-items: center;
  }
  .right-select {
    height: 100%;
    width: 55%;
    font-size: 0.07rem;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    position: relative;
    .title {
      margin-right: 0.03rem;
    }
    .row {
      display: flex;
      // ::v-deep .el-input__suffix{
      //   opacity: 0;
      // }
      ::v-deep .el-input__inner {
        width: 0.6rem;
        height: 0.1rem;
        // background-color: #050E3F;
        border: 1px solid #1c638b;
        border-top-left-radius: 0.05rem;
        border-bottom-right-radius: 0.05rem;
        text-align: center;
        line-height: 0.1rem;
        // white-space: nowrap;
        // text-overflow: ellipsis;
        // overflow: hidden;
        background: url(../assets/img/arrowdown.png) no-repeat;
        background-size: 10% 90%;
        background-position: 95% 50%;
      }
      .options {
        background-color: #050e3f;
        border: 1px solid #1c638b;
        border-top-left-radius: 0.05rem;
        border-bottom-right-radius: 0.05rem;
        width: 0.6rem;
        height: 0.1rem;
        text-align: center;
        line-height: 0.1rem;
      }
      .select {
        border: 1px solid #1c638b;
        border-top-left-radius: 0.05rem;
        border-bottom-right-radius: 0.05rem;
        color: #d4ddec;
        //box-shadow: inset 0 0 40px #0936b0;
        overflow: hidden;
        background-color: #050e3f;
        width: 0.6rem;
        height: 0.1rem;
        background: url(../assets/img/arrowdown.png) no-repeat;
        background-size: 10% 90%;
        background-position: 95% 50%;
        cursor: pointer;
        position: relative;
        // ::v-deep .el-input__inner{
        //   background-color: #050E3F;
        //   width: 0.6rem;
        //   height: 0.1rem;
        //   border: none;
        //   position: absolute;
        //   top: -0.025rem;
        //   border-top-left-radius: 0.05rem;
        //   border-bottom-right-radius: 0.05rem;
        //   font-size: 0.07rem;
        //   color: #D4DDEC;
        //   z-index: 100;
        //   text-align: center;
        //   background: url(../assets/img/arrowdown.png) no-repeat;
        //   background-size: 10% 90%;
        //   background-position: 95% 50%;
        // }
        ::v-deep .el-input__inner {
          width: 0.6rem;
          height: 0.1rem;
          position: absolute;
          border: none;
          background-color: #103579;
          z-index: 200;
          background: url(../assets/img/arrowdown.png) no-repeat;
          background-size: 10% 90%;
          background-position: 95% 50%;
          top: -0.07rem;
          color: #d4ddec;
        }
      }
    }
  }
}
</style>

<style lang="scss">
// .el-date-picker {
//   border: none;
//   background: #142d6f;
//   .el-date-picker__time-header {
//     .el-input {
//       background: white;
//       border-radius: 0.1rem;
//       .el-input__inner {
//         color: #142d6f;
//       }
//     }
//   }
//   .el-date-picker__header {
//     .el-picker-panel__icon-btn,
//     .el-date-picker__prev-btn,
//     .el-icon-d-arrow-left {
//       color: white;
//     }
//   }
//   .el-picker-panel__body {
//     background: #142d6f;
//     .el-date-picker__time-header {
//       border: none;
//     }
//     .el-picker-panel__content {
//       .prev-month {
//         color: #606266;
//       }
//       .available {
//         color: white;
//       }
//     }
//   }
//   .el-picker-panel__footer {
//     background: #142d6f;
//   }
//   color: #030e48;
// }
</style>
