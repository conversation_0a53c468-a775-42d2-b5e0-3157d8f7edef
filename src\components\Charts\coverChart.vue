<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>
<script>
import echarts from 'echarts'
import resize from './mixins'
import { componentMixin } from '@/components/mixin/componentMixin'
export default {
  name: 'CoverChart',
  mixins: [resize, componentMixin],
  props: {
    id: {
      require: false,
      type: String,
      default: 'coverChart'
    },
    width: {
      require: false,
      type: String,
      default: '2rem'
    },
    height: {
      require: false,
      type: String,
      default: '2rem'
    },
    value: {
      require: false,
      type: Number,
      default: 0
    }
  },
  watch: {
    value: { // 深度监听，可监听到对象、数组的变化
      handler(newValue, oldVal) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.option = {
        animationDuration: 3000,
        backgroundColor: 'transparent',
        title: {
          textAlign: 'center',
          top: '60%', // 字体的位置
          left: '47%',
          'text': '接入率',
          'textStyle': {
            'fontWeight': 'normal',
            'color': '#FFF',
            'fontSize': 14
          }
        },
        series: [{
          'name': '',
          'type': 'pie',
          'radius': ['50%', '70%'],
          'avoidLabelOverlap': false,
          'startAngle': 225,
          'color': [{
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0.4,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: '#038FD2' // 0% 处的颜色
            }, {
              offset: 1,
              color: '#41C9FF' // 100% 处的颜色
            }],
            globalCoord: false // 缺省为 false
          }, 'none'],
          'hoverAnimation': false, // 是否开启 hover 在扇区上的放大动画效果。
          'legendHoverLink': false, // 是否启用图例 hover 时的联动高亮。
          'label': {
            'show': false
          },
          'data': [{
            'value': 75,
            'name': '1'
          }, {
            'value': 25,
            'name': '2'
          }]
        }, {
          'name': ' ',
          'type': 'pie',
          'radius': ['43%', '45%'],
          'avoidLabelOverlap': false, // 是否启用防止标签重叠策略
          'startAngle': 225,
          'hoverAnimation': false,
          'legendHoverLink': false,
          'label': {
            'show': false
          },
          'data': [{
            'value': 75,
            'name': '1'
          }, {
            'value': 25,
            'name': '2'
          }]
        }, {
          'name': '',
          'type': 'pie',
          'radius': ['50%', '70%'],
          'avoidLabelOverlap': false,
          'startAngle': 315,
          'color': ['#082150', '#ff7a00', 'transparent'],
          'hoverAnimation': false,
          'legendHoverLink': false,
          'clockwise': false, // 饼图的扇区是否是顺时针排布。
          'z': 10,
          'label': {
            'show': false
          },
          'data': [{
            'value': (100 - this.value) * 270 / 360,
            'label': {
              formatter: this.value + '%',
              position: 'center',
              show: true,
              textStyle: {
                fontSize: '26',
                fontWeight: 'normal',
                color: '#fff'
              }
            },
            'name': ''
          }, {
            'value': 1,
            'name': ''
          }, {
            'value': 100 - (100 - this.value) * 270 / 360,
            'name': ''
          }]
        }]
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
<style scoped>

</style>
